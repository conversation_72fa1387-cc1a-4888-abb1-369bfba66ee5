# App Structure

## Clean Architecture Layers

Our application follows a Clean Architecture design pattern with the following layers:
1. **UI Layer**: Responsible for displaying data to the user and capturing user interactions.
2. **ViewModel Layer**: Acts as a bridge between the UI and business logic. It fetches data from the Repository and exposes it to the UI.
3. **Repository Layer**: Serves as an abstraction over data sources. It aggregates data from one or more data sources.
4. **DataSource Layer**: Interfaces directly with data storage solutions like Firestore.
5. **Firestore**: Our NoSQL cloud database used for storing and syncing data in real time.

## Key Cross-Cutting Utilities

- **QueryBuilder**: Facilitates the creation of complex Firestore queries with filtering and sorting.
- **EnhancedFirebasePagingSource**: Provides an efficient way to paginate data fetched from Firestore.
- **SearchIndexUtils**: Helps in generating and maintaining search indexes for faster query execution.
- **Hilt DI (Dependency Injection)**: Manages dependencies in our application, improving modularity and testability.

## Major Feature Modules

- **Commands**: Manages command operations within the app.
- **Cargos**: Handles cargo-related functionalities.
- **Transactions**: Operates on financial transaction logic.
- **Shipments**: Deals with shipment processing and tracking.
- **Countries**: Manages country-specific data and operations.
- **AddImage**: Handles the management of basic product information, such as uploading and categorizing images. This module is yet to be optimized.

## Data Flow

### Read Path
1. **Paging**: Implemented using `EnhancedFirebasePagingSource` to load data in chunks.
2. **Server-Side Filters**: Applied using `QueryBuilder` to optimize data retrieval from Firestore.
3. **Statistics & Aggregates**: Uses Firebase's aggregate count queries via `AggregateQueries` utility to efficiently load counts without reading all documents.

### Write Path
1. **Data Modification**: Changes are propagated to Firestore.
2. **Search Index Generation**: Utilizes `SearchIndexUtils` to update indexes, ensuring efficient data retrieval.

### Stats Data Flow (Optimized for Dashboard)
1. **Aggregate Queries**: The `HomeSelectionViewModelOptimized` uses Firebase aggregate count queries to load statistics without fetching entire collections.
2. **Data Sources**: Each data source (e.g., `FirebaseCommandDataSource`, `FirebaseCargoDataSource`) implements aggregate count methods:
   - `getCommandsCount()`: Total count using `AggregateQueries.getCount()`
   - `getCommandsCountByStatus()`: Count filtered by status
   - `getCommandsCountByAllStatuses()`: Batch count for all statuses
3. **Real-time Updates**: Stats can be observed in real-time using Flow-based aggregate listeners:
   - `getCommandsCountByAllStatusesFlow()`: Real-time counts with snapshot listeners
4. **Repository Layer**: Repositories expose aggregate methods that are consumed by ViewModels
5. **DTO Pattern**: `StatsDTO` and related data classes encapsulate aggregated statistics

## Real-Time Listener Strategy

- **Firestore Listeners**: Used to observe data changes in Firestore in real-time. They trigger ViewModel updates to reflect changes in the UI instantly.

## File/Folder Map

- **UI**: `src/ui/`
- **ViewModel**: `src/viewmodel/`
- **Repository**: `src/repository/`
- **DataSource**: `src/datasource/`
- **Firestore Setup**: `src/firestore/`
- **Utilities**: `src/utils/`
- **Commands Feature**: `src/features/commands/`
- **Cargos Feature**: `src/features/cargos/`
- **Transactions Feature**: `src/features/transactions/`
- **Shipments Feature**: `src/features/shipments/`
- **Countries Feature**: `src/features/countries/`
- **AddImage Feature**: `src/ui/screens/addimagedatascreen/`

## Guidelines for Adding Future Aggregates

When adding new aggregate statistics to the application:

### 1. Use Firebase Aggregate Queries
- Always use `AggregateQueries.getCount()` for counting documents
- Avoid loading entire collections just to get counts
- Example:
```kotlin
suspend fun getProductsCountByCategory(category: String): Result<Long> = FirebaseRepository.tryFetch {
    val query = firestore.collection(COLLECTION_PRODUCTS)
        .whereEqualTo("category", category)
    val count = AggregateQueries.getCount(query)
    Result.success(count)
}
```

### 2. Implement at DataSource Level
- Add aggregate methods to the appropriate Firebase data source
- Support both one-time fetch and real-time Flow variants
- Batch multiple related counts when possible

### 3. Expose Through Repository
- Add corresponding methods to the repository interface
- Keep the repository abstraction clean

### 4. Use in ViewModels Efficiently
- Call aggregate methods in ViewModels, not full list fetches
- Use StateFlow to expose stats to UI
- Example from `HomeSelectionViewModelOptimized`:
```kotlin
private fun loadCommandCounts() {
    viewModelScope.launch {
        commandRepository.getCommandsCountByAllStatuses().fold(
            onSuccess = { counts -> _commandCounts.value = counts },
            onFailure = { /* handle error */ }
        )
    }
}
```

### 5. Create DTOs for Complex Stats
- Use data classes like `StatsDTO` to structure aggregated data
- Keep stats separate from entity models

## ⚠️ WARNING: Avoid Binding Entire Collections to App-Wide ViewModels

**DO NOT** load entire collections (all commands, all cargos, etc.) in app-wide ViewModels like `ApplicationViewModel`. This approach:

### Problems:
1. **Memory Issues**: Keeping thousands of items in memory causes OutOfMemoryErrors
2. **Performance**: Slows down the entire app, especially on low-end devices
3. **Unnecessary Reads**: Wastes Firestore read quota by loading data that isn't displayed
4. **Scalability**: App performance degrades as data grows

### Instead:
1. **Use Paging**: Load data in chunks with `EnhancedFirebasePagingSource`
2. **Use Aggregates**: Get counts and statistics without loading documents
3. **Screen-Specific Data**: Load only what's needed for the current screen
4. **Selective Listeners**: Use filtered real-time listeners, not collection-wide ones

### Example of What NOT to Do:
```kotlin
// ❌ BAD: Loading entire collection in ApplicationViewModel
class ApplicationViewModel : ViewModel() {
    val allCommands = commandRepository.getAllCommands() // Loads thousands of items!
    val allCargos = cargoRepository.getAllCargos()       // Memory disaster!
}
```

### Example of What TO Do:
```kotlin
// ✅ GOOD: Using aggregates for statistics
class HomeSelectionViewModelOptimized : ViewModel() {
    fun loadCommandCounts() {
        commandRepository.getCommandsCountByAllStatuses() // Just counts, not documents!
    }
}

// ✅ GOOD: Using paging for lists
class CommandListViewModel : ViewModel() {
    val commands = commandRepository.getCommandsPagingData() // Loads in chunks!
}
```
