# Migrating from `tryFetch` to `safeCall`

This document outlines the steps to refactor code that uses the deprecated `FirebaseRepository.tryFetch` method to the new `safeCall` utility function.

## Overview

The `FirebaseRepository.tryFetch` method is deprecated and will be removed in a future release. All code should be migrated to use the new `safeCall` utility function from `CoroutineUtils.kt`.

## Migration Steps

1. **Add import for `safeCall`**:
   ```kotlin
   import com.tfkcolin.josyandchris.util.safeCall
   ```

2. **Remove import for `FirebaseRepository`** (if it's no longer used elsewhere in the file):
   ```kotlin
   // Remove this
   import com.tfkcolin.josyandchris.repository.FirebaseRepository
   ```

3. **Replace `tryFetch` with `safeCall`**:

   Before:
   ```kotlin
   suspend fun someFunction(): Type = FirebaseRepository.tryFetch {
       // Some operations
       Result.success(value)
   }
   ```

   After:
   ```kotlin
   suspend fun someFunction(): Result<Type> = safeCall("Context description") {
       // Some operations
       value
   }
   ```

   Key differences:
   - Add a meaningful context description as the first parameter to `safeCall`
   - Return type changes from `Type` to `Result<Type>`
   - Remove `Result.success()` wrapping

4. **Update method signatures**:
   
   Before:
   ```kotlin
   @Throws(FirebaseRepository.FetchException::class)
   suspend fun someFunction(): Type
   ```

   After:
   ```kotlin
   suspend fun someFunction(): Result<Type>
   ```

5. **Update method callers**:

   Before:
   ```kotlin
   try {
       val result = someFunction()
       // Use result directly
   } catch (e: FirebaseRepository.FetchException) {
       // Handle error
   }
   ```

   After:
   ```kotlin
   val result = someFunction()
   if (result.isSuccess) {
       val value = result.getOrNull()
       // Use value
   } else {
       val exception = result.exceptionOrNull()
       // Handle error
   }
   ```

## Example Refactoring

### Before:

```kotlin
@Throws(FirebaseRepository.FetchException::class)
suspend fun uploadImageUrlSync(filename: String, imageDataId: String, url: String): Uri = 
    FirebaseRepository.tryFetch {
        val res = Tasks.await(
            storage.child(filename).putFile(url.toUri()), 15, TimeUnit.MINUTES
        )
        val uri = Tasks.await(res.storage.downloadUrl)
        Tasks.await(
            imageDB.document(imageDataId).update(
                mapOf(
                    "url" to uri.toString(),
                    "upload" to true,
                    "path" to res.storage.path
                )
            ),
            15, TimeUnit.MINUTES
        )
        uri
    }
```

### After:

```kotlin
suspend fun uploadImageUrlSync(filename: String, imageDataId: String, url: String): Result<Uri> = 
    safeCall("Upload Image URL") {
        val res = Tasks.await(
            storage.child(filename).putFile(url.toUri()), 15, TimeUnit.MINUTES
        )
        val uri = Tasks.await(res.storage.downloadUrl)
        Tasks.await(
            imageDB.document(imageDataId).update(
                mapOf(
                    "url" to uri.toString(),
                    "upload" to true,
                    "path" to res.storage.path
                )
            ),
            15, TimeUnit.MINUTES
        )
        uri
    }
```

## Complete Refactoring Checklist

To fully migrate away from `FirebaseRepository`, the following files need to be refactored:

1. DataSource files:
   - FirebaseFinancialTransactionDataSource.kt
   - FirebaseCargoDataSource.kt 
   - FirebaseCommandDataSource.kt
   - FirebaseShipmentDataSource.kt

2. ViewModel files:
   - EditImageDataViewModel.kt
   - ShipmentDetailsViewModel.kt
   - App.kt (potential usage)

Once all usages have been refactored, the `FirebaseRepository.kt` file can be safely removed.
