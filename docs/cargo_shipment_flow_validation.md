# Cargo and Shipment Flow Validation

This document outlines the testing approach for validating Cargo and Shipment flows with live updates in the JocelExpress application.

## Test Coverage Overview

### 1. CargoDetailsScreen Tests

#### Create Mode Tests
- ✅ Verify UI displays "Nouveau cargo" title
- ✅ Verify origin and destination fields are displayed
- ✅ Verify shipments section is hidden in create mode
- ✅ Verify save button is available for ADMIN and EMPLOYEE roles

#### Edit Mode Tests
- ✅ Verify UI displays "Détails du cargo" title
- ✅ Verify cargo data is properly displayed
- ✅ Verify shipments section is visible
- ✅ Verify existing shipments are listed
- ✅ Verify "Add shipment" button is available

#### Live Mode Toggle Tests
- ✅ Verify live mode switch is displayed
- ✅ Verify "LIVE" indicator appears when enabled
- ✅ Verify toggle subscribes/unsubscribes from Firestore snapshots
- ✅ Verify live updates are properly handled in ViewModel

#### Status Change Tests
- ✅ Verify all cargo status FilterChips are displayed
- ✅ Verify status can be changed by clicking FilterChips
- ✅ Verify status changes persist when saved

#### Delete Confirmation Tests
- ✅ Verify delete button only appears for ADMIN role
- ✅ Verify confirmation dialog appears with warning message
- ✅ Verify cascade delete of associated shipments
- ✅ Verify proper cleanup of orphaned shipments

### 2. ShipmentDetailsScreen Tests

#### Create Mode Tests
- ✅ Verify shipment creation with correct cargoId
- ✅ Verify all input fields are displayed
- ✅ Verify no products initially

#### Edit Mode Tests
- ✅ Verify existing shipment data is displayed
- ✅ Verify products are listed
- ✅ Verify status can be changed

#### Product Management Tests
- ✅ Verify add product dialog functionality
- ✅ Verify product deletion (ADMIN/EMPLOYEE only)
- ✅ Verify CLIENT role cannot add/delete products

#### Navigation Tests
- ✅ Verify navigation from CargoDetailsScreen passes correct cargoId
- ✅ Verify new shipments are created with the parent cargoId

### 3. CargoDetailsViewModel Tests

#### Live Mode Management
- ✅ Initial state has live mode disabled
- ✅ toggleLiveMode() properly enables/disables live updates
- ✅ startLiveUpdates() subscribes to Firestore snapshots
- ✅ stopLiveUpdates() unsubscribes and cancels listeners
- ✅ Live mode doesn't start in create mode
- ✅ Document updates are reflected in currentCargo state
- ✅ Document deletion is handled properly
- ✅ Errors in live updates are logged but don't crash

#### Cascade Delete
- ✅ deleteCargoSync() first deletes all associated shipments
- ✅ Then deletes the cargo itself
- ✅ Proper error handling for failed deletions

### 4. ShipmentDetailsViewModel Tests

#### CargoId Propagation
- ✅ ViewModel properly initializes with cargoId from navigation args
- ✅ Create mode correctly identifies when shipmentId is empty
- ✅ updateShipmentSync() sets cargoId when creating new shipment
- ✅ Existing shipments maintain their cargoId when updated

### 5. Integration Tests

#### Cargo List Screen
- ✅ All cargos are displayed in the list
- ✅ Status filter chips work correctly
- ✅ Search functionality filters cargos
- ✅ Navigation to detail screen works
- ✅ Cargo status is displayed correctly in list items

#### Status Persistence
- ✅ Status changes in detail screen reflect in list
- ✅ Filter by status shows correct cargos
- ✅ Search index is updated with status changes

## Running the Tests

### Unit Tests
```bash
./gradlew test
```

### Instrumented Tests
```bash
./gradlew connectedAndroidTest
```

### Specific Test Classes
```bash
# ViewModel Tests
./gradlew test --tests "*CargoDetailsViewModelTest"
./gradlew test --tests "*ShipmentDetailsViewModelTest"

# UI Tests
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.tfkcolin.josyandchris.ui.screens.cargodetails.CargoDetailsScreenTest
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.tfkcolin.josyandchris.ui.screens.shipmentdetails.ShipmentDetailsScreenTest
```

## Key Validation Points

### 1. Live Updates
- The toggle switch in CargoDetailsScreen properly controls real-time listeners
- When enabled, changes in Firestore are immediately reflected in the UI
- When disabled, the screen only shows static data
- Memory leaks are prevented by proper cleanup in onCleared()

### 2. Shipment Creation Flow
1. User clicks "Ajouter une expédition" in CargoDetailsScreen
2. Navigation passes empty shipmentId and the current cargoId
3. ShipmentDetailsViewModel identifies create mode
4. When saved, the shipment is created with the correct cargoId
5. The new shipment appears in the cargo's shipment list

### 3. Status Changes
1. User clicks a status FilterChip in CargoDetailsScreen
2. The cargo's statusIndex is updated
3. When saved, the searchIndex is also updated for filtering
4. The status change is reflected in:
   - The cargo list screen
   - Any filtered views
   - Search results

### 4. Delete Cascade
1. Admin user clicks delete in CargoDetailsScreen
2. Confirmation dialog warns about deleting shipments
3. On confirmation:
   - All shipments with matching cargoId are deleted first
   - Then the cargo itself is deleted
   - No orphaned shipments remain

## Future Improvements

1. **Offline Support**: Add tests for offline mode and sync when back online
2. **Performance Tests**: Add tests for handling large numbers of shipments
3. **Conflict Resolution**: Add tests for concurrent updates in live mode
4. **Error Recovery**: Add more comprehensive error handling tests
5. **Accessibility**: Add tests for screen reader support and navigation

## Conclusion

The test suite comprehensively validates:
- ✅ CargoDetailsScreen in both create and edit modes
- ✅ Live mode toggle subscribes/unsubscribes from Firestore
- ✅ Shipment creation with correct cargoId propagation
- ✅ Status changes persist and reflect across screens
- ✅ Delete confirmation with cascade delete handling

All critical flows have been tested to ensure data integrity and proper user experience.
