# File Navigation Guide - JocelEpress

## 📁 Root Level Folder Purposes

| **Folder** | **Purpose** |
|------------|-------------|
| **auth** | Authentication logic, user management, and authorization |
| **cache** | Caching utilities and data caching logic |
| **data** | Data models, entities, and data-related logic |
| **logic** | Business logic implementation and domain rules |
| **network** | Networking utilities and API communication |
| **remote** | Remote data sources (Firebase data sources) |
| **repository** | Data abstraction layer, aggregates data from multiple sources |
| **ui** | User Interface components, screens, and themes |
| **util** | Utility classes, helper functions, and common tools |

## 🎯 Where to Touch for Common Tasks

### Adding a New Firestore Query
**Path**: `util/query/` → `repository/`
1. Create `QueryBuilder` in `util/query/` (e.g., `XQueryBuilder.kt`)
2. Expose via `Repository` in `repository/` package
3. Update `JACModule.kt` if new dependency injection needed

### Changing Dependency Injection (DI)
**Path**: `JACModule.kt`
- Main DI configuration file
- Add new `@Provides` or `@Binds` methods
- Update `RepositoryModule` for repository bindings

### Updating UI
**Path**: `ui/screens/**` → `ViewModel`
1. Modify screen components in `ui/screens/[screen_name]/`
2. Update corresponding `ViewModel` in same directory
3. Update `ui/components/` for reusable components

### Updating Paging Behavior
**Path**: `util/query/QueryBuilderFactory.kt`
- Modify `EnhancedFirebasePagingSource` class
- Update cursor configuration and pagination logic
- Test with existing `*PagingSource` implementations

### Modifying Search Logic
**Path**: `util/SearchIndexUtils.kt`
- Update `generatePrefixTokens()` method
- Modify `generateSearchIndex()` for new search fields
- Update `generateQueryTokens()` for query processing

### Adding Composite Indexes
**Path**: `firestore.indexes.json` + `docs/FIRESTORE_INDEXES_DEPLOYMENT.md`
1. Add index definition to `firestore.indexes.json`
2. Update deployment documentation
3. Deploy using `firebase deploy --only firestore:indexes`

## 📋 Common Tasks → Files/Classes Reference

| **Task** | **Primary Files** | **Secondary Files** |
|----------|-------------------|---------------------|
| **Add Firestore Query** | `util/query/*QueryBuilder.kt`<br>`repository/*Repository.kt` | `JACModule.kt`<br>`remote/datasource/*DataSource.kt` |
| **Change DI** | `JACModule.kt` | `auth/di/AuthModule.kt` |
| **Update UI** | `ui/screens/[screen]/[Screen].kt`<br>`ui/screens/[screen]/[Screen]ViewModel.kt` | `ui/components/*.kt`<br>`ui/theme/*.kt` |
| **Update Paging** | `util/query/QueryBuilderFactory.kt`<br>`EnhancedFirebasePagingSource` | `remote/datasource/*PagingSource.kt` |
| **Modify Search** | `util/SearchIndexUtils.kt` | `util/query/*QueryBuilder.kt` |
| **Add Indexes** | `firestore.indexes.json`<br>`FIRESTORE_INDEXES_DEPLOYMENT.md` | Firebase Console |
| **Authentication** | `auth/AuthManager.kt`<br>`auth/data/*Repository.kt` | `auth/di/AuthModule.kt` |
| **Data Models** | `data/*.kt` | `logic/*.kt` |
| **Business Logic** | `logic/*.kt` | `repository/*Repository.kt` |
| **Network/Remote** | `remote/datasource/*DataSource.kt` | `network/*.kt` |

## 🔧 Quick Reference Commands

### Creating New Components
```bash
# New screen structure
mkdir -p ui/screens/newscreen
touch ui/screens/newscreen/NewScreen.kt
touch ui/screens/newscreen/NewScreenViewModel.kt
touch ui/screens/newscreen/NewScreenNavigation.kt
```

### Adding New Query Builder
```bash
# New query builder
touch util/query/NewQueryBuilder.kt
# Update factory
# Edit util/query/QueryBuilderFactory.kt
```

### Firestore Index Deployment
```bash
# Deploy indexes
firebase deploy --only firestore:indexes
```

## 📝 File Naming Conventions

| **Type** | **Pattern** | **Example** |
|----------|-------------|-------------|
| **Screen** | `[Name]Screen.kt` | `CommandListScreen.kt` |
| **ViewModel** | `[Name]ViewModel.kt` | `CommandListViewModel.kt` |
| **Repository** | `[Name]Repository.kt` | `CommandRepository.kt` |
| **Repository Impl** | `Firebase[Name]RepositoryImpl.kt` | `FirebaseCommandRepositoryImpl.kt` |
| **Data Source** | `Firebase[Name]DataSource.kt` | `FirebaseCommandDataSource.kt` |
| **Paging Source** | `Firebase[Name]PagingSource.kt` | `FirebaseCommandPagingSource.kt` |
| **Query Builder** | `[Name]QueryBuilder.kt` | `CommandQueryBuilder.kt` |
| **Navigation** | `[Name]Navigation.kt` | `CommandListNavigation.kt` |

## 🚀 Development Workflow

1. **Define Data Model** → `data/`
2. **Create Query Builder** → `util/query/`
3. **Implement Data Source** → `remote/datasource/`
4. **Create Repository** → `repository/`
5. **Add DI Bindings** → `JACModule.kt`
6. **Build ViewModel** → `ui/screens/[screen]/`
7. **Create UI Screen** → `ui/screens/[screen]/`
8. **Add Navigation** → `ui/screens/[screen]/`
9. **Update Indexes** → `firestore.indexes.json`

---
*This guide serves as a quick reference for navigating and modifying the JocelEpress codebase efficiently.*
