# Error Handling Migration Guide

This document provides examples of migrating existing code to use the new error handling pattern with `safeCall`.

## Migration Examples

### 1. ShipmentDetailsViewModel

#### Before:
```kotlin
fun updateShipmentSync(shipment: Shipment) = launchWithErrorHandling {
    safeCall {
        if (isCreate) {
            val newShipment = shipment.copy(
                id = shipmentRef.value.id,
                cargoId = cargoId
            )
            Tasks.await(shipmentRef.value.set(newShipment), 15, TimeUnit.MINUTES)
            shipmentRef.value = shipmentDB.document()
        } else {
            Tasks.await(shipmentRef.value.set(shipment), 15, TimeUnit.MINUTES)
        }
    }
}
```

#### After:
```kotlin
suspend fun saveShipment(shipment: Shipment): Result<Unit> = safeCall("Save shipment") {
    if (isCreate) {
        val newShipment = shipment.copy(
            id = shipmentRef.value.id,
            cargoId = cargoId
        )
        Tasks.await(shipmentRef.value.set(newShipment), 15, TimeUnit.MINUTES)
        shipmentRef.value = shipmentDB.document()
    } else {
        Tasks.await(shipmentRef.value.set(shipment), 15, TimeUnit.MINUTES)
    }
}
```

Key changes:
- Renamed from `updateShipmentSync` to `saveShipment` for better clarity
- Changed from using `launchWithErrorHandling` to returning a `Result<Unit>`
- Added an operation name to `safeCall` for better error reporting
- Made the function `suspend` to properly work with coroutines

### 2. EditImageDataViewModel

#### Before:
```kotlin
fun uploadImage(filename: String, fileUri: Uri) = launchWithErrorHandling {
    _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
    safeCall { 
        storageRepository.uploadFile(storage, filename, fileUri) 
    }.handleResult(
        // Result handling logic
    )
}
```

#### After:
```kotlin
fun uploadImage(filename: String, fileUri: Uri) = launchWithErrorHandling {
    _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
    safeCall("Upload image") { 
        storageRepository.uploadFile(storage, filename, fileUri) 
    }.handleResult(
        // Result handling logic
    )
}
```

Key changes:
- Added operation name to `safeCall` for better error reporting and tracing

### 3. Error Handling for Chained Operations

For more complex cases with chained operations, each operation should have its own descriptive name:

```kotlin
fun replaceImage(oldPath: String, newFilename: String, newFileUri: Uri) = launchWithErrorHandling {
    _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
    
    // First delete the old image
    safeCall("Delete old image") { 
        storageRepository.deleteFile(storage, oldPath) 
    }.handleResult(
        onSuccess = {
            // Then upload the new image
            safeCall("Upload new image") { 
                storageRepository.uploadFile(storage, newFilename, newFileUri) 
            }.handleResult(
                // Result handling logic
            )
        },
        onError = { exception ->
            // Error handling logic
        }
    )
}
```

## Best Practices

1. **Use descriptive operation names**: Include a clear operation name in `safeCall` to make it easier to identify where errors occur.

2. **Return Result for suspend functions**: When converting functions, make them return `Result<T>` and make them `suspend` functions.

3. **Consistent naming**: Use consistent method names that describe the action being performed (e.g., `saveShipment` instead of `updateShipmentSync`).

4. **Separate UI state updates**: Ensure UI state updates are handled in the result handlers, not mixed with the core operation logic.

## Migration Steps

1. Identify functions that use `launchWithErrorHandling` and `safeCall`
2. Determine if the function should return a `Result` or continue using `launchWithErrorHandling`
3. Add descriptive operation names to all `safeCall` invocations
4. Ensure error handling is properly implemented in `.handleResult()` blocks
5. Update function signatures and return types as needed
