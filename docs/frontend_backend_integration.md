# Frontend-Backend Integration Guide

## Overview
This guide explains how to integrate optimized repositories with the UI using Paging 3 and Jetpack Compose, enabling efficient data loading with real-time updates and proper error handling.

## 1. Pattern: ViewModel exposes Flow<PagingData> from Repository

### Core Pattern
```kotlin
// ViewModel exposes Flow<PagingData>
class CargoListViewModel @Inject constructor(
    private val cargoRepository: CargoRepository
) : ViewModel() {
    
    val paginatedCargos: Flow<PagingData<Cargo>> = cargoRepository
        .getCargosPagingDataWithFilters(filters)
        .cachedIn(viewModelScope)
}

// Composable uses collectAsLazyPagingItems
@Composable
fun CargoListScreen(viewModel: CargoListViewModel) {
    val pager = viewModel.paginatedCargos.collectAsLazyPagingItems()
    
    LazyColumn {
        items(pager) { cargo ->
            JACCargoItem(cargo = cargo)
        }
    }
}
```

## 2. Step-by-Step Refactor Guide: CargoListScreen Example

### Step 1: Update ViewModel
Replace in-memory pagination with Paging 3:

```kotlin
// OLD: In-memory pagination
private val _cargos = MutableStateFlow<List<Cargo>>(emptyList())
val cargos: StateFlow<List<Cargo>> = _cargos.asStateFlow()

// NEW: Paging 3 with server-side filtering
private val _filters = MutableStateFlow(QueryFilters())
val paginatedCargos: Flow<PagingData<Cargo>> = _filters
    .flatMapLatest { filters ->
        cargoRepository.getCargosPagingDataWithFilters(filters)
    }
    .cachedIn(viewModelScope)
```

### Step 2: Update Composable Parameters
```kotlin
// OLD: List parameter
@Composable
fun CargoListScreen(
    cargos: List<Cargo>,
    // ... other parameters
)

// NEW: ViewModel parameter
@Composable
fun CargoListScreen(
    viewModel: CargoListViewModel = hiltViewModel(),
    // ... other parameters
)
```

### Step 3: Replace LazyColumn Implementation
```kotlin
// OLD: Manual pagination
LazyColumn {
    items(paginatedCargos) { cargo ->
        JACCargoItem(cargo = cargo)
    }
}

// NEW: Paging 3 implementation
val pager = viewModel.paginatedCargos.collectAsLazyPagingItems()

LazyColumn {
    items(pager) { cargo ->
        cargo?.let {
            JACCargoItem(cargo = it)
        }
    }
    
    // Handle loading states
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }
            
            loadState.append is LoadState.Loading -> {
                item {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }
            
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error",
                        onRetry = { retry() }
                    )
                }
            }
            
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error",
                        onRetry = { retry() }
                    )
                }
            }
        }
    }
}
```

### Step 4: Remove Manual Pagination Controls
Delete all manual pagination logic:
```kotlin
// REMOVE: Manual pagination state and controls
var currentPage by remember { mutableStateOf(0) }
val itemsPerPage = 10
val paginatedCargos by remember(filteredCargos, currentPage, itemsPerPage) { ... }
val totalPages by remember(filteredCargos, itemsPerPage) { ... }

// REMOVE: Pagination buttons
TextButton(onClick = { if (currentPage > 0) currentPage-- }) { ... }
```

## 3. Real-Time Toggle Usage

### ViewModel Implementation
```kotlin
class CargoListViewModel @Inject constructor(
    private val cargoRepository: CargoRepository
) : ViewModel() {
    
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null
    
    fun toggleLiveMode() {
        if (_isLiveMode.value) {
            stopLiveUpdates()
        } else {
            startLiveUpdates()
        }
    }
    
    private fun startLiveUpdates() {
        _isLiveMode.value = true
        liveUpdatesJob?.cancel()
        
        liveUpdatesJob = viewModelScope.launch {
            cargoRepository.listenForUpdates(getCurrentFilters())
                .collect { updatedCargos ->
                    // Handle real-time updates
                    // This will trigger refresh in paging data
                    _filters.value = _filters.value.copy(
                        timestamp = System.currentTimeMillis()
                    )
                }
        }
    }
    
    private fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
    }
    
    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
    }
}
```

### UI Integration
```kotlin
@Composable
fun CargoListScreen(viewModel: CargoListViewModel = hiltViewModel()) {
    val isLiveMode by viewModel.isLiveMode.collectAsState()
    
    TopAppBar(
        title = { Text("Cargo List") },
        actions = {
            IconButton(onClick = { viewModel.toggleLiveMode() }) {
                Icon(
                    imageVector = if (isLiveMode) Icons.Default.Pause else Icons.Default.PlayArrow,
                    contentDescription = if (isLiveMode) "Stop Live Updates" else "Start Live Updates",
                    tint = if (isLiveMode) Color.Red else Color.Green
                )
            }
        }
    )
}
```

## 4. Handling Loading/Error States from Paging 3

### Error Item Component
```kotlin
@Composable
fun ErrorItem(
    message: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "Error",
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = onRetry) {
                Text("Retry")
            }
        }
    }
}
```

### Loading State Handling
```kotlin
@Composable
fun LoadingItem() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = "Loading...",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
```

### Empty State Handling
```kotlin
@Composable
fun EmptyStateItem(message: String) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Empty",
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.outline
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}
```

## 5. Migration Checklist for Each Feature Module

### Commands Module
- [ ] Update `CommandListViewModel` to expose `Flow<PagingData<Command>>`
- [ ] Replace `CommandListScreen` to use `collectAsLazyPagingItems`
- [ ] Implement loading/error states
- [ ] Add real-time toggle functionality
- [ ] Remove manual pagination logic
- [ ] Test with Firestore console

### Transactions Module
- [ ] Update `AccountingScreenViewModel` to expose `Flow<PagingData<FinancialTransaction>>`
- [ ] Replace transaction list UI to use `collectAsLazyPagingItems`
- [ ] Implement loading/error states
- [ ] Add real-time toggle functionality
- [ ] Remove manual pagination logic
- [ ] Test with Firestore console

### Shipments Module
- [ ] Update `ShipmentListViewModel` to expose `Flow<PagingData<Shipment>>`
- [ ] Replace shipment list UI to use `collectAsLazyPagingItems`
- [ ] Implement loading/error states
- [ ] Add real-time toggle functionality
- [ ] Remove manual pagination logic
- [ ] Test with Firestore console

## 6. Code Snippets for ViewModel and Composable Setup

### ViewModel Pattern
```kotlin
@HiltViewModel
class FeatureListViewModel @Inject constructor(
    private val repository: FeatureRepository
) : ViewModel() {
    
    private val _filters = MutableStateFlow(QueryFilters())
    
    val paginatedItems: Flow<PagingData<FeatureItem>> = _filters
        .flatMapLatest { filters ->
            repository.getItemsPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)
    
    fun updateFilters(newFilters: QueryFilters) {
        _filters.value = newFilters
    }
    
    fun refreshData() {
        _filters.value = _filters.value.copy(
            timestamp = System.currentTimeMillis()
        )
    }
}
```

### Composable Pattern
```kotlin
@Composable
fun FeatureListScreen(
    viewModel: FeatureListViewModel = hiltViewModel(),
    onNavigateToDetail: (String) -> Unit
) {
    val pager = viewModel.paginatedItems.collectAsLazyPagingItems()
    
    LazyColumn {
        items(pager) { item ->
            item?.let {
                FeatureItemCard(
                    item = it,
                    onClick = { onNavigateToDetail(it.id) }
                )
            }
        }
        
        // Handle states
        handlePagingStates(pager)
    }
}

@Composable
private fun LazyListScope.handlePagingStates(pager: LazyPagingItems<*>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item { LoadingItem() }
            }
            loadState.append is LoadState.Loading -> {
                item { LoadingItem() }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item { EmptyStateItem("No items found") }
            }
        }
    }
}
```

## 7. Testing Tips to Validate Firestore Reads

### Console Logging
```kotlin
// Add to your data source
class FirebaseCargoDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    fun getCargosPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Cargo>> {
        Log.d("FirebaseCargoDataSource", "Loading cargos with filters: $filters")
        
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                Log.d("FirebaseCargoDataSource", "Creating new paging source")
                FirebaseCargoPagingSource(firestore, filters)
            }
        ).flow
    }
}
```

### Test Queries
```kotlin
// Add test functions to verify queries
suspend fun testCargoQuery(filters: QueryFilters) {
    val query = CargoQueryBuilder(firestore).buildQuery(filters)
    
    Log.d("TestQuery", "Query: ${query.toString()}")
    
    query.get().addOnSuccessListener { snapshot ->
        Log.d("TestQuery", "Results: ${snapshot.documents.size} documents")
        snapshot.documents.forEach { doc ->
            Log.d("TestQuery", "Document: ${doc.id} -> ${doc.data}")
        }
    }.addOnFailureListener { exception ->
        Log.e("TestQuery", "Error: ${exception.message}")
    }
}
```

### UI Testing
```kotlin
// Test paging behavior
@Test
fun testPagingBehavior() {
    composeTestRule.onNodeWithText("Loading...").assertIsDisplayed()
    
    // Wait for data to load
    composeTestRule.waitForIdle()
    
    // Verify items are displayed
    composeTestRule.onNodeWithText("Cargo Item 1").assertIsDisplayed()
    
    // Test scrolling triggers more loading
    composeTestRule.onNodeWithTag("cargo_list").performScrollToIndex(15)
    
    // Verify more items load
    composeTestRule.onNodeWithText("Cargo Item 16").assertIsDisplayed()
}
```

### Performance Monitoring
```kotlin
// Monitor Firestore reads
class FirestoreReadMonitor {
    companion object {
        private var readCount = 0
        
        fun incrementReadCount() {
            readCount++
            Log.d("FirestoreMonitor", "Total reads: $readCount")
        }
        
        fun resetReadCount() {
            readCount = 0
        }
    }
}
```

## Best Practices

1. **Always cache paging data** in ViewModel scope using `cachedIn(viewModelScope)`
2. **Handle all load states** (Loading, Error, NotLoading) appropriately
3. **Use proper error handling** with retry functionality
4. **Implement empty states** for better UX
5. **Monitor Firestore reads** to ensure efficiency
6. **Use real-time updates selectively** to avoid unnecessary resource consumption
7. **Test thoroughly** with various network conditions and data states

## Common Pitfalls to Avoid

1. **Don't forget to handle null items** in the paging list
2. **Don't cache paging data in the wrong scope** (use viewModelScope)
3. **Don't ignore error states** - always provide retry functionality
4. **Don't start real-time listeners without cleanup** in onCleared()
5. **Don't mix manual pagination with Paging 3** - choose one approach
6. **Don't forget to remove manual pagination controls** when migrating

This guide provides a comprehensive approach to integrating Paging 3 with Jetpack Compose for efficient, scalable data loading with proper error handling and real-time capabilities.
