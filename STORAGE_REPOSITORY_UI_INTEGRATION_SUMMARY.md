# StorageRepository UI Integration Summary

## Overview
This document summarizes the integration of `StorageRepository` into the image-related UI layers, including proper state management and offline/network error handling.

## Changes Made

### 1. Created UI State Classes
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/states/ImageOperationState.kt`
- **Purpose**: Manages loading, success, and error states for image operations
- **Classes**:
  - `ImageOperationState`: Sealed class with Idle, Loading, Success, and Error states
  - `ImageScreenUiState`: Overall UI state including network availability and retry options

### 2. Updated ViewModels

#### AddImageDataScreenViewModel
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/addimagedatascreen/AddImageDataScreenViewModel.kt`
- **Changes**:
  - Added `StorageRepository` dependency injection
  - Added `uiState: StateFlow<ImageScreenUiState>` for reactive UI updates
  - Implemented `uploadImage()` method that:
    - Uses `viewModelScope.launch` for coroutines
    - Calls `storageRepository.uploadFile()` 
    - Updates UI state based on success/failure
  - Added `clearState()` method to reset UI state

#### EditImageDataViewModel
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/editimagedata/EditImageDataViewModel.kt`
- **Changes**:
  - Added `StorageRepository` dependency injection
  - Added `uiState: StateFlow<ImageScreenUiState>`
  - Implemented three main methods:
    - `uploadImage()`: Uploads a new image
    - `deleteImage()`: Deletes an existing image
    - `replaceImage()`: Deletes old image and uploads new one
  - All methods use proper error handling and state management

### 3. Updated UI Screens

#### AddImageDataScreen
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/addimagedatascreen/AddImageDataScreen.kt`
- **Changes**:
  - Added `uiState` parameter to observe loading/error states
  - Added `onDismissError` callback for error handling
  - Updated `onUploadImage` to be a regular function (not suspend)
  - Added error state handling with LaunchedEffect
  - Updated `ImageDataItem` to show loading state during upload

#### EditImageDataScreen
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/editimagedata/EditImageDataScreen.kt`
- **Changes**:
  - Updated `onDelete` parameter to be a regular function (not suspend)
  - Removed coroutine scope usage in delete button handler

### 4. Updated Navigation

#### AddImageDataNavigation
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/addimagedatascreen/AddImageDataNavigation.kt`
- **Changes**:
  - Observes `uiState` from ViewModel
  - Passes UI state to screen
  - Calls new `uploadImage()` method instead of old sync method
  - Adds `onDismissError` handler that calls `clearState()`

#### EditImageDataNavigation
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/screens/editimagedata/EditImageDataNavigation.kt`
- **Changes**:
  - Observes `uiState` from ViewModel
  - Simplified delete handling using new `deleteImage()` method

### 5. Created Network Error Components
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/components/NetworkErrorSnackbar.kt`
- **Purpose**: Reusable components for showing network errors and loading states
- **Components**:
  - `NetworkErrorSnackbar`: Shows error messages with retry option
  - `LoadingOverlay`: Shows loading dialog during operations

### 6. Enhanced Image Picker Components
- **File**: `app/src/main/java/com/tfkcolin/josyandchris/ui/components/JACImageDataCreator.kt`
- **Changes**:
  - Added `onImageReplace` parameter for handling image replacement
  - Added logic to differentiate between new upload and replacement

## Key Benefits

1. **Proper State Management**: All image operations now have clear loading, success, and error states
2. **Offline Support**: Network errors are properly caught and displayed to users
3. **Retry Capability**: Users can retry failed operations through snackbar actions
4. **Clean Architecture**: ViewModels properly delegate to repository, maintaining separation of concerns
5. **Reactive UI**: UI automatically updates based on operation state changes
6. **Consistent Error Handling**: All image operations follow the same error handling pattern

## Usage Example

```kotlin
// In ViewModel
fun uploadImage(filename: String, imageDataId: String, url: String) {
    viewModelScope.launch {
        _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
        val result = storageRepository.uploadFile(storage, filename, url.toUri())
        _uiState.value = if (result.isSuccess) {
            // Update Firestore and emit success state
            _uiState.value.copy(imageOperationState = ImageOperationState.Success("Upload successful"))
        } else {
            _uiState.value.copy(imageOperationState = ImageOperationState.Error(result.exceptionOrNull()!!))
        }
    }
}

// In Screen
val uiState by viewModel.uiState.collectAsState()

// Show loading indicator
if (uiState.imageOperationState is ImageOperationState.Loading) {
    CircularProgressIndicator()
}

// Handle errors
NetworkErrorSnackbar(
    snackbarHostState = snackbarHostState,
    imageOperationState = uiState.imageOperationState,
    onRetry = { viewModel.retryLastOperation() },
    onDismiss = { viewModel.clearState() }
)
```

## Next Steps

1. Add network connectivity monitoring to automatically retry when connection is restored
2. Implement image caching for offline viewing
3. Add progress percentage for large file uploads
4. Consider adding image compression before upload to save bandwidth
