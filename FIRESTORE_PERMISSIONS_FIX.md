# Firestore Security Rules - Permission Denied Fix

## Issue
After connecting to production Firestore, the app crashes with:
```
FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
```

This occurs when trying to read from collections: `cargos`, `shipments`, `countries`, `transactions`.

## Root Cause
The Firestore security rules on the server are not configured to allow authenticated users to read these collections.

## Solution

### 1. Go to Firebase Console
1. Open [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Navigate to **Firestore Database** → **Rules**

### 2. Update Security Rules

Replace the existing rules with the following (adjust based on your security requirements):

#### Option A: Basic Authenticated Access (Recommended for Testing)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read all documents
    match /{document=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
  }
}
```

#### Option B: More Granular Rules (Recommended for Production)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return isAuthenticated() && 
        request.auth.token.role == 'ADMIN';
    }
    
    // Helper function to check if user is admin or employee
    function isStaff() {
      return isAuthenticated() && 
        (request.auth.token.role == 'ADMIN' || 
         request.auth.token.role == 'EMPLOYEE');
    }
    
    // Countries - all authenticated users can read
    match /countries/{country} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Transactions - based on role
    match /transactions/{transaction} {
      allow read: if isAuthenticated();
      allow create: if isAdmin();
      allow update, delete: if isAdmin();
    }
    
    // Commands - staff can read/write
    match /commands/{command} {
      allow read: if isAuthenticated();
      allow write: if isStaff();
    }
    
    // Cargos - staff can read/write
    match /cargos/{cargo} {
      allow read: if isAuthenticated();
      allow write: if isStaff();
    }
    
    // Shipments - staff can read/write
    match /shipments/{shipment} {
      allow read: if isAuthenticated();
      allow write: if isStaff();
    }
    
    // Image data
    match /imagesData/{imageData} {
      allow read: if isAuthenticated();
      allow write: if isStaff();
    }
    
    // User profiles
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && request.auth.uid == userId;
    }
  }
}
```

### 3. Deploy the Rules
1. Click **Publish** in the Firebase Console
2. Wait for the rules to deploy (usually takes a few seconds)

### 4. Set User Roles (If Using Role-Based Rules)

If using role-based security, you need to set custom claims for users. This can be done via:

#### Admin SDK (Server-side)
```javascript
// Set custom user claims
admin.auth().setCustomUserClaims(uid, { role: 'ADMIN' })
  .then(() => {
    console.log('Custom claims set for user');
  });
```

#### Cloud Function Example
```javascript
exports.setUserRole = functions.https.onCall(async (data, context) => {
  // Verify the request is made by an admin
  if (!context.auth || context.auth.token.role !== 'ADMIN') {
    throw new functions.https.HttpsError('permission-denied', 'Must be an admin');
  }
  
  const { userId, role } = data;
  
  // Set custom user claims
  await admin.auth().setCustomUserClaims(userId, { role });
  
  return { message: `Role ${role} assigned to user ${userId}` };
});
```

## Alternative: Temporary Fix for Development

If you need to quickly test, you can temporarily use these very permissive rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

⚠️ **WARNING**: Never use these rules in production as they allow anyone to read/write your entire database!

## Testing the Fix
1. After updating the rules, force close the app
2. Reopen the app
3. The data should now load without permission errors

## Additional Notes
- The app seems to be using role-based access control with roles: ADMIN, EMPLOYEE, CLIENT
- Make sure user roles are properly set in your authentication system
- Consider implementing proper role management in your app
