# StorageRepository Usage Example

## Overview
The StorageRepository has been modernized with the following improvements:
- Uses coroutines with `suspend` functions
- Returns `Result<T>` for standardized error handling
- Properly registered with <PERSON>lt for dependency injection
- Simplified API without callback lambdas

## Usage in ViewModels

### Before (Old Pattern)
```kotlin
// Old callback-based approach
StorageRepository.uploadFile(
    storage = storage,
    path = "images/$filename",
    fileUri = uri,
    onSuccess = { downloadUrl, error ->
        if (error != null) {
            // Handle error
        } else {
            // Use downloadUrl
        }
    },
    onError = { errorMsg ->
        // Handle error
    }
)
```

### After (New Pattern)
```kotlin
@HiltViewModel
class MyViewModel @Inject constructor(
    private val storageRepository: StorageRepository,
    @Named("PRODUCT_IMAGES_STORAGE") private val storage: StorageReference
) : ViewModel() {
    
    fun uploadImage(uri: Uri, filename: String) {
        viewModelScope.launch {
            val result = storageRepository.uploadFile(
                storage = storage,
                path = "images/$filename",
                fileUri = uri
            )
            
            result.fold(
                onSuccess = { downloadUrl ->
                    // Handle successful upload
                    // downloadUrl is the URL string of the uploaded file
                },
                onFailure = { exception ->
                    // Handle error
                    // exception contains the error details
                }
            )
        }
    }
    
    fun deleteImage(path: String) {
        viewModelScope.launch {
            val result = storageRepository.deleteFile(
                storage = storage,
                path = path
            )
            
            result.fold(
                onSuccess = {
                    // Handle successful deletion
                },
                onFailure = { exception ->
                    // Handle error
                }
            )
        }
    }
}
```

## Benefits

1. **Cleaner Code**: No nested callbacks, linear flow with coroutines
2. **Type Safety**: Result<T> provides clear success/failure paths
3. **Testability**: Easy to mock StorageRepository interface for unit tests
4. **Consistency**: Follows the same pattern as other repositories in the app
5. **Error Handling**: Standardized error handling with Result type

## Migration Guide

To migrate existing code:
1. Inject `StorageRepository` instead of using the object directly
2. Replace callback-based calls with coroutine calls in a `viewModelScope.launch` block
3. Use `result.fold()` or `result.getOrNull()` to handle the response
4. Remove callback lambda parameters
