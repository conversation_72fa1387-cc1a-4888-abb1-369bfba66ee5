# ImageData Pagination and Filtering Capabilities

## Overview

This document describes the pagination and filtering capabilities implemented for the `ImageData` entity. These features allow for efficient data retrieval from Firestore with server-side filtering and cursor-based pagination.

## Key Components

### 1. Data Model

**ImageData** - Core entity representing image metadata:
```kotlin
data class ImageData(
    val id: String = "",
    val url: String? = null,
    val category: String = "",
    val genre: String = "",
    val path: String? = null,
    val name: String? = null,
    val upload: Boolean = false,
    val created: Date = Calendar.getInstance().time,
    val searchIndex: List<String> = emptyList()
)
```

### 2. Query Infrastructure

#### QueryFilters
```kotlin
data class QueryFilters(
    val statusIndex: Int? = null,
    val dateRange: DateRange? = null,
    val searchTerm: String? = null,
    val searchTokens: List<String>? = null,
    val additionalFilters: Map<String, Any> = emptyMap()
)
```

#### CursorConfig
```kotlin
data class CursorConfig(
    val startAfter: DocumentSnapshot? = null,
    val limit: Int = 20,
    val orderBy: String = "created",
    val orderDirection: Query.Direction = Query.Direction.DESCENDING
)
```

#### DateRange
```kotlin
data class DateRange(
    val startDate: Date,
    val endDate: Date
)
```

### 3. Query Builders

**ImageQueryBuilder** - Specialized query builder for ImageData:
- Handles specialized filtering for category, genre, upload status, date ranges, and search terms
- Builds Firebase Firestore queries with appropriate filtering and ordering
- Supports cursor-based pagination

### 4. Pagination Components

**EnhancedFirebasePagingSource** - Custom PagingSource implementation for Firestore:
- Integrates with Android Paging 3 library
- Supports cursor-based pagination using DocumentSnapshots
- Handles filtering through QueryFilters
- Provides efficient forward-only pagination

## Filtering Capabilities

The ImageData entity supports the following filter types:

1. **Category Filtering**
   - Filter images by specific category
   - Server-side filtering using `whereEqualTo("category", category)`

2. **Genre Filtering**
   - Filter images by specific genre
   - Server-side filtering using `whereEqualTo("genre", genre)`

3. **Upload Status Filtering**
   - Filter images by upload status (uploaded/pending)
   - Server-side filtering using `whereEqualTo("upload", uploadStatus)`

4. **Date Range Filtering**
   - Filter images created within a specific date range
   - Server-side filtering using `whereGreaterThanOrEqualTo` and `whereLessThanOrEqualTo`

5. **Text Search**
   - Search across image metadata using `searchIndex` field
   - Server-side filtering using `whereGreaterThanOrEqualTo` for prefix search

6. **Combined Filtering**
   - Any combination of the above filters can be applied simultaneously
   - All filtering occurs server-side for optimal performance

## Pagination Implementation

ImageData pagination uses cursor-based pagination which offers several advantages:

1. **Efficient** - Only fetches the data needed for the current page
2. **Consistent** - No duplicate or missing items when data changes
3. **Server-side** - Offloads filtering and pagination to Firestore
4. **Cursor-based** - Uses DocumentSnapshot as cursor for reliable pagination

### Implementation Details

1. **PagingConfig** - Controls page size and placeholders
2. **EnhancedFirebasePagingSource** - Custom PagingSource for Firestore
3. **Cursor Handling** - Uses last document of each page as cursor for next page
4. **Forward Pagination** - Supports forward-only pagination (most common use case)

## Usage Examples

### Basic Pagination

```kotlin
// Get all images with pagination
val imagesFlow = imageDataRepository.getImageDataPagingData()

// Collect and display in UI
viewLifecycleOwner.lifecycleScope.launch {
    imagesFlow.collectLatest { pagingData ->
        imageAdapter.submitData(pagingData)
    }
}
```

### Filtered Pagination

```kotlin
// Filter by category
val filters = QueryFilters(
    additionalFilters = mapOf("category" to "landscape")
)
val landscapeImages = imageDataRepository.getImageDataPagingData(filters)

// Filter by upload status
val filters = QueryFilters(
    additionalFilters = mapOf("upload" to true)
)
val uploadedImages = imageDataRepository.getImageDataPagingData(filters)

// Filter by date range
val oneWeekAgo = Calendar.getInstance().apply { add(Calendar.DAY_OF_YEAR, -7) }.time
val now = Calendar.getInstance().time
val filters = QueryFilters(
    dateRange = DateRange(startDate = oneWeekAgo, endDate = now)
)
val recentImages = imageDataRepository.getImageDataPagingData(filters)

// Search images
val filters = QueryFilters(
    searchTerm = "mountain"
)
val searchResults = imageDataRepository.getImageDataPagingData(filters)

// Combined filters
val filters = QueryFilters(
    additionalFilters = mapOf(
        "category" to "nature",
        "upload" to true
    ),
    dateRange = DateRange(startDate = oneWeekAgo, endDate = now)
)
val filteredImages = imageDataRepository.getImageDataPagingData(filters)
```

## Benefits

1. **Performance** - Server-side filtering reduces network traffic
2. **Scalability** - Only relevant documents are retrieved from Firebase
3. **Flexibility** - Supports multiple filter types and combinations
4. **Consistency** - Reliable cursor-based pagination
5. **Integration** - Works with Android Paging 3 library

## Technical Details

- **EnhancedFirebasePagingSource** - Custom PagingSource for Firestore pagination
- **QueryBuilder Pattern** - Flexible query construction with filters
- **Cursor-based Pagination** - Uses DocumentSnapshot for reliable pagination
- **Server-side Filtering** - All filtering happens at the Firestore level
- **Reactive Architecture** - Returns Flow<PagingData<ImageData>> for reactive UI updates
