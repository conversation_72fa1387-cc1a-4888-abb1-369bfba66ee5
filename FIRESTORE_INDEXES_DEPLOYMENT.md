# Firestore Indexes Deployment Guide

## Overview
This document provides instructions for deploying the Firestore composite indexes defined in `firestore.indexes.json`.

## Created Composite Indexes

### Commands Collection
1. **commandStepIndex + created** - For filtering commands by step and ordering by creation date
2. **searchIndex + created** - For text search functionality with creation date ordering

### Cargos Collection  
1. **statusIndex + created** - For filtering cargos by status and ordering by creation date
2. **origin + destination + created** - For route-based queries with creation date ordering

### Transactions Collection
1. **country + transactionTypeIndex + created** - For filtering by country and transaction type
2. **commandId + created** - For retrieving transactions related to specific commands
3. **transactionTypeIndex + created** - For filtering by transaction type only
4. **marked + created** - For filtering marked/unmarked transactions

## Deployment Options

### Option 1: Using Firebase CLI (Recommended)
```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project (if not already done)
firebase init firestore

# Deploy the indexes
firebase deploy --only firestore:indexes
```

### Option 2: Using Firebase Console
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database
4. Go to the "Indexes" tab
5. Create composite indexes manually with the following configurations:

#### Commands Collection Indexes:
- **Index 1**: `commandStepIndex` (Ascending) + `created` (Descending)
- **Index 2**: `searchIndex` (Array-contains) + `created` (Descending)

#### Cargos Collection Indexes:
- **Index 1**: `statusIndex` (Ascending) + `created` (Descending)
- **Index 2**: `origin` (Ascending) + `destination` (Ascending) + `created` (Descending)

#### Transactions Collection Indexes:
- **Index 1**: `country` (Ascending) + `transactionTypeIndex` (Ascending) + `created` (Descending)
- **Index 2**: `commandId` (Ascending) + `created` (Descending)
- **Index 3**: `transactionTypeIndex` (Ascending) + `created` (Descending)
- **Index 4**: `marked` (Ascending) + `created` (Descending)

## Important Notes

1. **Index Building Time**: Composite indexes can take several minutes to build, especially for collections with existing data.

2. **Query Performance**: These indexes are designed to support the common query patterns in your application:
   - Filtering commands by step and searching with text
   - Filtering cargos by status and route
   - Filtering transactions by country, type, and marking status

3. **Cost Considerations**: Each composite index has storage and query costs. Monitor your usage in the Firebase Console.

4. **Maintenance**: Review and update indexes as your query patterns evolve.

## Verification

After deployment, verify the indexes are working by:
1. Checking the Firebase Console Indexes tab
2. Testing queries in your application
3. Monitoring query performance in Firebase Console

## Troubleshooting

If you encounter issues:
- Ensure Firebase CLI is properly authenticated
- Check that the `firestore.indexes.json` file is in the correct format
- Verify your Firebase project configuration
- Check Firebase Console for any error messages during index creation
