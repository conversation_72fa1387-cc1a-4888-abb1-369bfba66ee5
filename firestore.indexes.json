{"indexes": [{"collectionGroup": "commands", "queryScope": "COLLECTION", "fields": [{"fieldPath": "commandStepIndex", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "commands", "queryScope": "COLLECTION", "fields": [{"fieldPath": "searchIndex", "arrayConfig": "CONTAINS"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "cargos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "statusIndex", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "cargos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "origin", "order": "ASCENDING"}, {"fieldPath": "destination", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country", "order": "ASCENDING"}, {"fieldPath": "transactionTypeIndex", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "commandId", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "transactionTypeIndex", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "marked", "order": "ASCENDING"}, {"fieldPath": "created", "order": "DESCENDING"}]}], "fieldOverrides": []}