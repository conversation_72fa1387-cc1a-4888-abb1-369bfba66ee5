<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AppInsightsSettings">
    <option name="tabSettings">
      <map>
        <entry key="Android Vitals">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.tfkcolin.josyandchris" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="SEVEN_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
        <entry key="Firebase Crashlytics">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.tfkcolin.josyandchris" />
                  <option name="mobileSdkAppId" value="1:523681448642:android:9fc353b9e4216acec4715b" />
                  <option name="projectId" value="prixusinechine-20b6b" />
                  <option name="projectNumber" value="523681448642" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="THIRTY_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>