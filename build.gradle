buildscript {
    ext {
        compose_version = '1.4.3'
        //kotlin_version = '1.0.0'
    }
    /*dependencies {
        classpath 'com.google.gms:google-services:4.3.15'
        //classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }*/
    repositories {
        mavenCentral()
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.11.1' apply false
    id 'com.android.library' version '8.11.1' apply false
    id 'org.jetbrains.kotlin.android' version '2.1.20' apply false
    id 'com.google.dagger.hilt.android' version "2.56.2" apply false
	id 'com.google.gms.google-services' version "4.4.3" apply false
	id 'com.google.firebase.crashlytics' version "3.0.4" apply false
    id "org.jetbrains.kotlin.plugin.compose" version "2.1.20" apply false
    id("com.google.devtools.ksp") version "2.1.20-2.0.1" apply false
}