package com.tfkcolin.josyandchris.dto

import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.CargoStatus

/**
 * Data Transfer Object for aggregated application statistics
 * Used for displaying summary information without loading full lists
 */
data class StatsDTO(
    val commandStats: CommandStatsDetail = CommandStatsDetail(),
    val cargoStats: CargoStatsDetail = CargoStatsDetail(),
    val shipmentStats: ShipmentStats = ShipmentStats(0, 0, 0, 0),
    val transactionStats: TransactionStats = TransactionStats(0, 0, 0),
    val countryStats: Map<String, CountryStatsDetail> = emptyMap(),
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Detailed command statistics with counts by status
 */
data class CommandStatsDetail(
    val totalCommands: Int = 0,
    val countsByStatus: Map<CommandStep, Int> = emptyMap()
)

/**
 * Detailed cargo statistics with counts by status
 */
data class CargoStatsDetail(
    val totalCargo: Int = 0,
    val countsByStatus: Map<CargoStatus, Int> = emptyMap()
)

/**
 * Country-specific statistics
 */
data class CountryStatsDetail(
    val countryName: String,
    val inputTotal: Double = 0.0,
    val outputTotal: Double = 0.0,
    val balance: Double = 0.0,
    val transactionCount: Int = 0
)
