package com.tfkcolin.josyandchris.dto

data class CommandStats(val totalCommands: Int, val successfulCommands: Int, val failedCommands: Int)

data class CargoStats(val totalCargo: Int, val deliveredCargo: Int, val pendingCargo: Int)

data class TransactionStats(val totalTransactions: Int, val successfulTransactions: Int, val failedTransactions: Int)

data class ShipmentStats(val totalShipments: Int, val shipped: Int, val inTransit: Int, val delivered: Int)
