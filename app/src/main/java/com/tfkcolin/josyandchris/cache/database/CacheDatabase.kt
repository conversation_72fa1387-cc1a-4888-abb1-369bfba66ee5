package com.tfkcolin.josyandchris.cache.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tfkcolin.josyandchris.cache.database.dao.*
import com.tfkcolin.josyandchris.cache.database.entity.*

/**
 * Room database for caching frequently accessed data to improve performance
 */
@Database(
    entities = [
        CachedClientSuggestion::class,
        CachedCountryPreset::class,
        CachedCommandPreset::class,
        CachedSearchHistory::class
    ],
    version = 1,
    exportSchema = false
)
abstract class CacheDatabase : RoomDatabase() {
    
    abstract fun clientSuggestionDao(): CachedClientSuggestionDao
    abstract fun countryPresetDao(): CachedCountryPresetDao
    abstract fun commandPresetDao(): CachedCommandPresetDao
    abstract fun searchHistoryDao(): CachedSearchHistoryDao
    
    companion object {
        private const val DATABASE_NAME = "jac_cache_database"
        
        @Volatile
        private var INSTANCE: CacheDatabase? = null
        
        fun getInstance(context: Context): CacheDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CacheDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(object : RoomDatabase.Callback() {
                        override fun onCreate(db: SupportSQLiteDatabase) {
                            super.onCreate(db)
                            // Initialize with some default data if needed
                        }
                    })
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * In-memory LRU cache for hot data that needs immediate access
 */
class InMemoryLruCache<K, V>(maxSize: Int) {
    
    private val cache = object : LinkedHashMap<K, V>(maxSize + 1, 0.75f, true) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>?): Boolean {
            return size > maxSize
        }
    }
    
    @Synchronized
    fun get(key: K): V? {
        return cache[key]
    }
    
    @Synchronized
    fun put(key: K, value: V) {
        cache[key] = value
    }
    
    @Synchronized
    fun remove(key: K): V? {
        return cache.remove(key)
    }
    
    @Synchronized
    fun clear() {
        cache.clear()
    }
    
    @Synchronized
    fun size(): Int {
        return cache.size
    }
    
    @Synchronized
    fun containsKey(key: K): Boolean {
        return cache.containsKey(key)
    }
}

/**
 * Cache manager that combines Room database and in-memory LRU cache
 */
class CacheManager private constructor(context: Context) {
    
    private val database = CacheDatabase.getInstance(context)
    
    // In-memory LRU caches for hot data
    private val clientSuggestionCache = InMemoryLruCache<String, List<String>>(50)
    private val countryPresetsCache = InMemoryLruCache<String, List<String>>(20)
    private val searchHistoryCache = InMemoryLruCache<String, List<String>>(100)
    
    val clientSuggestionDao = database.clientSuggestionDao()
    val countryPresetDao = database.countryPresetDao()
    val commandPresetDao = database.commandPresetDao()
    val searchHistoryDao = database.searchHistoryDao()
    
    companion object {
        @Volatile
        private var INSTANCE: CacheManager? = null
        
        fun getInstance(context: Context): CacheManager {
            return INSTANCE ?: synchronized(this) {
                val instance = CacheManager(context)
                INSTANCE = instance
                instance
            }
        }
    }
    
    /**
     * Get client suggestions from memory cache first, then database
     */
    suspend fun getClientSuggestions(query: String): List<String> {
        val cacheKey = "client_$query"
        
        // Check memory cache first
        clientSuggestionCache.get(cacheKey)?.let { cachedResult ->
            return cachedResult
        }
        
        // If not in memory, check database
        val dbResult = if (query.isEmpty()) {
            clientSuggestionDao.getFrequentClients(10).let { flow ->
                // Since this is a suspend function, we need to handle the Flow differently
                // For now, return empty list and implement proper flow handling later
                emptyList<String>()
            }
        } else {
            clientSuggestionDao.searchClients(query, 10).map { it.clientName }
        }
        
        // Store in memory cache
        clientSuggestionCache.put(cacheKey, dbResult)
        return dbResult
    }
    
    /**
     * Add client suggestion to cache and database
     */
    suspend fun addClientSuggestion(clientName: String) {
        // Clear relevant memory cache entries
        clientSuggestionCache.clear()
        
        // Add to database
        val existing = clientSuggestionDao.searchClients(clientName, 1).firstOrNull()
        if (existing != null) {
            clientSuggestionDao.incrementClientFrequency(clientName)
        } else {
            clientSuggestionDao.insertOrUpdateClient(
                CachedClientSuggestion(clientName = clientName)
            )
        }
        
        // Cleanup old entries if cache is getting too big
        val count = clientSuggestionDao.getCount()
        if (count > 100) {
            clientSuggestionDao.deleteOldestEntries(20)
        }
    }
    
    /**
     * Clear expired cache entries periodically
     */
    suspend fun performCacheCleanup() {
        val cutoffTime = System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000) // 30 days
        
        clientSuggestionDao.cleanOldEntries(cutoffTime)
        searchHistoryDao.cleanOldSearches(cutoffTime)
        
        // Clear in-memory caches
        clientSuggestionCache.clear()
        countryPresetsCache.clear()
        searchHistoryCache.clear()
    }
}
