package com.tfkcolin.josyandchris.cache.di

import android.content.Context
import com.tfkcolin.josyandchris.cache.database.CacheDatabase
import com.tfkcolin.josyandchris.cache.database.CacheManager
import com.tfkcolin.josyandchris.cache.database.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing cache-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object CacheModule {
    
    /**
     * Provides the Room cache database instance
     */
    @Provides
    @Singleton
    fun provideCacheDatabase(@ApplicationContext context: Context): CacheDatabase {
        return CacheDatabase.getInstance(context)
    }
    
    /**
     * Provides the cache manager singleton instance
     */
    @Provides
    @Singleton
    fun provideCacheManager(@ApplicationContext context: Context): CacheManager {
        return CacheManager.getInstance(context)
    }
    
    /**
     * Provides the client suggestion DAO
     */
    @Provides
    fun provideClientSuggestionDao(database: CacheDatabase): CachedClientSuggestionDao {
        return database.clientSuggestionDao()
    }
    
    /**
     * Provides the country preset DAO
     */
    @Provides
    fun provideCountryPresetDao(database: CacheDatabase): CachedCountryPresetDao {
        return database.countryPresetDao()
    }
    
    /**
     * Provides the command preset DAO
     */
    @Provides
    fun provideCommandPresetDao(database: CacheDatabase): CachedCommandPresetDao {
        return database.commandPresetDao()
    }
    
    /**
     * Provides the search history DAO
     */
    @Provides
    fun provideSearchHistoryDao(database: CacheDatabase): CachedSearchHistoryDao {
        return database.searchHistoryDao()
    }
}
