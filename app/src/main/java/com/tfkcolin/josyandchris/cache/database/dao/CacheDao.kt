package com.tfkcolin.josyandchris.cache.database.dao

import androidx.room.*
import com.tfkcolin.josyandchris.cache.database.entity.CachedClientSuggestion
import com.tfkcolin.josyandchris.cache.database.entity.CachedCountryPreset
import com.tfkcolin.josyandchris.cache.database.entity.CachedCommandPreset
import com.tfkcolin.josyandchris.cache.database.entity.CachedSearchHistory
import kotlinx.coroutines.flow.Flow

/**
 * Room DAO for cached client suggestions
 */
@Dao
interface CachedClientSuggestionDao {
    
    @Query("SELECT * FROM cached_client_suggestions ORDER BY frequency DESC, lastUsed DESC LIMIT :limit")
    fun getFrequentClients(limit: Int = 10): Flow<List<CachedClientSuggestion>>
    
    @Query("SELECT * FROM cached_client_suggestions WHERE clientName LIKE '%' || :query || '%' ORDER BY frequency DESC LIMIT :limit")
    suspend fun searchClients(query: String, limit: Int = 10): List<CachedClientSuggestion>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateClient(client: CachedClientSuggestion)
    
    @Query("UPDATE cached_client_suggestions SET frequency = frequency + 1, lastUsed = :timestamp WHERE clientName = :clientName")
    suspend fun incrementClientFrequency(clientName: String, timestamp: Long = System.currentTimeMillis())
    
    @Query("DELETE FROM cached_client_suggestions WHERE lastUsed < :cutoffTime")
    suspend fun cleanOldEntries(cutoffTime: Long)
    
    @Query("SELECT COUNT(*) FROM cached_client_suggestions")
    suspend fun getCount(): Int
    
    @Query("DELETE FROM cached_client_suggestions WHERE clientName IN (SELECT clientName FROM cached_client_suggestions ORDER BY lastUsed ASC LIMIT :count)")
    suspend fun deleteOldestEntries(count: Int)
}

/**
 * Room DAO for cached country presets
 */
@Dao
interface CachedCountryPresetDao {
    
    @Query("SELECT * FROM cached_country_presets WHERE isActive = 1 ORDER BY sortOrder, countryName")
    fun getActiveCountries(): Flow<List<CachedCountryPreset>>
    
    @Query("SELECT * FROM cached_country_presets WHERE countryName LIKE '%' || :query || '%' AND isActive = 1 ORDER BY lastUsed DESC")
    suspend fun searchCountries(query: String): List<CachedCountryPreset>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateCountry(country: CachedCountryPreset)
    
    @Query("UPDATE cached_country_presets SET lastUsed = :timestamp WHERE countryName = :countryName")
    suspend fun updateLastUsed(countryName: String, timestamp: Long = System.currentTimeMillis())
    
    @Query("DELETE FROM cached_country_presets WHERE countryName = :countryName")
    suspend fun deleteCountry(countryName: String)
}

/**
 * Room DAO for cached command presets
 */
@Dao
interface CachedCommandPresetDao {
    
    @Query("SELECT * FROM cached_command_presets ORDER BY frequency DESC, lastUsed DESC")
    fun getCommandPresets(): Flow<List<CachedCommandPreset>>
    
    @Query("SELECT * FROM cached_command_presets WHERE stepIndex = :stepIndex ORDER BY frequency DESC")
    suspend fun getPresetsByStep(stepIndex: Int): List<CachedCommandPreset>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdatePreset(preset: CachedCommandPreset)
    
    @Query("UPDATE cached_command_presets SET frequency = frequency + 1, lastUsed = :timestamp WHERE presetId = :presetId")
    suspend fun incrementPresetFrequency(presetId: String, timestamp: Long = System.currentTimeMillis())
}

/**
 * Room DAO for search history caching
 */
@Dao
interface CachedSearchHistoryDao {
    
    @Query("SELECT * FROM cached_search_history WHERE searchType = :type ORDER BY frequency DESC, lastSearched DESC LIMIT :limit")
    fun getSearchHistory(type: String, limit: Int = 10): Flow<List<CachedSearchHistory>>
    
    @Query("SELECT DISTINCT searchTerm FROM cached_search_history WHERE searchType = :type AND searchTerm LIKE '%' || :query || '%' ORDER BY frequency DESC LIMIT :limit")
    suspend fun getSuggestedSearchTerms(type: String, query: String, limit: Int = 5): List<String>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateSearch(search: CachedSearchHistory)
    
    @Query("UPDATE cached_search_history SET frequency = frequency + 1, lastSearched = :timestamp WHERE searchTerm = :term AND searchType = :type")
    suspend fun incrementSearchFrequency(term: String, type: String, timestamp: Long = System.currentTimeMillis())
    
    @Query("DELETE FROM cached_search_history WHERE lastSearched < :cutoffTime")
    suspend fun cleanOldSearches(cutoffTime: Long)
}
