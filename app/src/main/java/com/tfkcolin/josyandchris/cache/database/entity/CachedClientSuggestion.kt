package com.tfkcolin.josyandchris.cache.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Room entity for caching client suggestions to improve autocomplete performance
 */
@Entity(tableName = "cached_client_suggestions")
data class CachedClientSuggestion(
    @PrimaryKey
    val clientName: String,
    val frequency: Int = 1, // How often this client name was used
    val lastUsed: Long = System.currentTimeMillis(), // Timestamp of last usage
    val isFrequent: Boolean = false, // Mark frequently used clients
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Room entity for caching country presets
 */
@Entity(tableName = "cached_country_presets")
data class CachedCountryPreset(
    @PrimaryKey
    val countryName: String,
    val countryCode: String?,
    val isActive: Boolean = true,
    val sortOrder: Int = 0, // For custom ordering
    val lastUsed: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Room entity for caching command step presets
 */
@Entity(tableName = "cached_command_presets")
data class CachedCommandPreset(
    @PrimaryKey
    val presetId: String,
    val stepIndex: Int,
    val stepName: String,
    val description: String?,
    val isDefault: Boolean = false,
    val frequency: Int = 1,
    val lastUsed: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Room entity for general search history caching
 */
@Entity(tableName = "cached_search_history")
data class CachedSearchHistory(
    @PrimaryKey
    val searchTerm: String,
    val searchType: String, // "command", "cargo", "shipment", "client"
    val resultCount: Int = 0, // Number of results returned
    val frequency: Int = 1,
    val lastSearched: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis()
)
