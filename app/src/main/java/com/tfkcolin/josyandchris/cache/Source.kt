package com.tfkcolin.josyandchris.cache

/**
 * Enum representing data source types for cache and network operations
 */
enum class Source {
    /**
     * Data should be retrieved from local cache only
     */
    CACHE,
    
    /**
     * Data should be retrieved from network only
     */
    NETWORK,
    
    /**
     * Data should be retrieved from cache first, then network if not available
     */
    CACHE_FIRST,
    
    /**
     * Data should be retrieved from network first, then cache if network fails
     */
    NETWORK_FIRST
}
