package com.tfkcolin.josyandchris.monitoring

import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.perf.metrics.Trace
import com.tfkcolin.josyandchris.config.RemoteConfigManager
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.system.measureTimeMillis

/**
 * Performance monitoring and crash reporting manager
 * Integrates with Firebase Performance Monitoring and Crashlytics
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    private val remoteConfigManager: RemoteConfigManager
) {

    private val crashlytics = FirebaseCrashlytics.getInstance()
    private val performance = FirebasePerformance.getInstance()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    companion object {
        // Performance trace names
        const val TRACE_QUERY_EXECUTION = "query_execution"
        const val TRACE_FILTER_APPLICATION = "filter_application"
        const val TRACE_UI_RENDERING = "ui_rendering"
        const val TRACE_CACHE_OPERATIONS = "cache_operations"
        const val TRACE_DATA_LOADING = "data_loading"
        const val TRACE_SCREEN_LOAD = "screen_load"
        
        // Custom metrics
        const val METRIC_QUERY_TIME = "query_time_ms"
        const val METRIC_CACHE_HIT_RATE = "cache_hit_rate"
        const val METRIC_FILTER_COUNT = "filter_count"
        const val METRIC_PAGINATION_SIZE = "pagination_size"
        const val METRIC_MEMORY_USAGE = "memory_usage_mb"
        
        // Performance thresholds (ms)
        const val SLOW_QUERY_THRESHOLD = 3000L
        const val SLOW_UI_RENDER_THRESHOLD = 500L
        const val SLOW_SCREEN_LOAD_THRESHOLD = 2000L
    }

    init {
        setupCrashlytics()
        setupPerformanceMonitoring()
    }

    private fun setupCrashlytics() {
        crashlytics.setCrashlyticsCollectionEnabled(
            remoteConfigManager.isCrashReportingEnabled()
        )
        
        // Set custom keys for better crash analysis
        crashlytics.setCustomKey("app_version", com.tfkcolin.josyandchris.BuildConfig.VERSION_NAME)
        crashlytics.setCustomKey("build_type", com.tfkcolin.josyandchris.BuildConfig.BUILD_TYPE)
        crashlytics.setCustomKey("performance_monitoring_enabled", remoteConfigManager.isPerformanceMonitoringEnabled())
    }

    private fun setupPerformanceMonitoring() {
        performance.isPerformanceCollectionEnabled = remoteConfigManager.isPerformanceMonitoringEnabled()
    }

    /**
     * Start a performance trace
     */
    fun startTrace(traceName: String): Trace? {
        return if (remoteConfigManager.isPerformanceMonitoringEnabled()) {
            performance.newTrace(traceName).also { 
                it.start()
                Timber.d("Started trace: $traceName")
            }
        } else {
            null
        }
    }

    /**
     * Stop a performance trace with optional metrics
     */
    fun stopTrace(trace: Trace?, metrics: Map<String, Long> = emptyMap()) {
        trace?.let {
            metrics.forEach { (key, value) ->
                it.putMetric(key, value)
            }
            it.stop()
            Timber.d("Stopped trace with metrics: $metrics")
        }
    }

    /**
     * Measure and report query performance
     */
    suspend fun <T> measureQueryPerformance(
        queryName: String,
        filterCount: Int = 0,
        operation: suspend () -> T
    ): T {
        val trace = startTrace(TRACE_QUERY_EXECUTION)
        
        return try {
            val result: T
            val executionTime = measureTimeMillis {
                result = operation()
            }
            
            // Record metrics
            val metrics = mapOf(
                METRIC_QUERY_TIME to executionTime,
                METRIC_FILTER_COUNT to filterCount.toLong()
            )
            
            stopTrace(trace, metrics)
            
            // Log slow queries
            if (executionTime > SLOW_QUERY_THRESHOLD) {
                logSlowOperation("Slow query detected", queryName, executionTime)
            }
            
            result
        } catch (e: Exception) {
            stopTrace(trace)
            recordException("Query execution failed: $queryName", e)
            throw e
        }
    }

    /**
     * Measure and report filter application performance
     */
    suspend fun <T> measureFilterPerformance(
        filterTypes: List<String>,
        operation: suspend () -> T
    ): T {
        val trace = startTrace(TRACE_FILTER_APPLICATION)
        
        return try {
            val result: T
            val executionTime = measureTimeMillis {
                result = operation()
            }
            
            stopTrace(trace, mapOf(
                METRIC_QUERY_TIME to executionTime,
                METRIC_FILTER_COUNT to filterTypes.size.toLong()
            ))
            
            // Record filter usage
            recordFilterUsage(filterTypes)
            
            result
        } catch (e: Exception) {
            stopTrace(trace)
            recordException("Filter application failed", e)
            throw e
        }
    }

    /**
     * Measure UI rendering performance
     */
    fun <T> measureUIRenderTime(
        screenName: String,
        operation: () -> T
    ): T {
        val trace = startTrace(TRACE_UI_RENDERING)
        
        return try {
            val result: T
            val executionTime = measureTimeMillis {
                result = operation()
            }
            
            stopTrace(trace, mapOf(METRIC_QUERY_TIME to executionTime))
            
            if (executionTime > SLOW_UI_RENDER_THRESHOLD) {
                logSlowOperation("Slow UI render", screenName, executionTime)
            }
            
            result
        } catch (e: Exception) {
            stopTrace(trace)
            recordException("UI rendering failed: $screenName", e)
            throw e
        }
    }

    /**
     * Measure screen load performance
     */
    suspend fun measureScreenLoad(
        screenName: String,
        operation: suspend () -> Unit
    ) {
        val trace = startTrace(TRACE_SCREEN_LOAD)
        
        try {
            val executionTime = measureTimeMillis {
                operation()
            }
            
            stopTrace(trace, mapOf(METRIC_QUERY_TIME to executionTime))
            
            if (executionTime > SLOW_SCREEN_LOAD_THRESHOLD) {
                logSlowOperation("Slow screen load", screenName, executionTime)
            }
            
            // Record screen view
            recordScreenView(screenName, executionTime)
            
        } catch (e: Exception) {
            stopTrace(trace)
            recordException("Screen load failed: $screenName", e)
            throw e
        }
    }

    /**
     * Measure cache operation performance
     */
    suspend fun <T> measureCachePerformance(
        operation: String,
        cacheHit: Boolean,
        block: suspend () -> T
    ): T {
        val trace = startTrace(TRACE_CACHE_OPERATIONS)
        
        return try {
            val result: T
            val executionTime = measureTimeMillis {
                result = block()
            }
            
            stopTrace(trace, mapOf(
                METRIC_QUERY_TIME to executionTime,
                METRIC_CACHE_HIT_RATE to if (cacheHit) 1L else 0L
            ))
            
            recordCacheUsage(operation, cacheHit, executionTime)
            
            result
        } catch (e: Exception) {
            stopTrace(trace)
            recordException("Cache operation failed: $operation", e)
            throw e
        }
    }

    /**
     * Record memory usage
     */
    fun recordMemoryUsage() {
        scope.launch {
            try {
                val runtime = Runtime.getRuntime()
                val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
                val maxMemory = runtime.maxMemory() / (1024 * 1024)
                
                // Log memory usage as custom key
                crashlytics.setCustomKey("memory_used_mb", usedMemory)
                crashlytics.setCustomKey("memory_max_mb", maxMemory)
                
                // Check for memory pressure
                val memoryUsagePercent = (usedMemory.toDouble() / maxMemory) * 100
                if (memoryUsagePercent > 80) {
                    logNonFatalIssue("High memory usage detected", mapOf(
                        "used_memory_mb" to usedMemory.toString(),
                        "max_memory_mb" to maxMemory.toString(),
                        "usage_percent" to "%.1f%%".format(memoryUsagePercent)
                    ))
                }
                
            } catch (e: Exception) {
                Timber.w(e, "Failed to record memory usage")
            }
        }
    }

    /**
     * Record custom exception with context
     */
    fun recordException(message: String, exception: Throwable, additionalData: Map<String, String> = emptyMap()) {
        if (remoteConfigManager.isCrashReportingEnabled()) {
            crashlytics.setCustomKey("error_message", message)
            additionalData.forEach { (key, value) ->
                crashlytics.setCustomKey(key, value)
            }
            crashlytics.recordException(exception)
            Timber.e(exception, "Recorded exception: $message")
        }
    }

    /**
     * Record non-fatal issue for monitoring
     */
    fun logNonFatalIssue(issue: String, context: Map<String, String> = emptyMap()) {
        if (remoteConfigManager.isCrashReportingEnabled()) {
            context.forEach { (key, value) ->
                crashlytics.setCustomKey(key, value)
            }
            crashlytics.log("NON_FATAL: $issue")
            Timber.w("Non-fatal issue: $issue, context: $context")
        }
    }

    /**
     * Set user identifier for crash reports
     */
    fun setUserId(userId: String) {
        if (remoteConfigManager.isCrashReportingEnabled()) {
            crashlytics.setUserId(userId)
            crashlytics.setCustomKey("user_id", userId)
        }
    }

    /**
     * Record feature usage
     */
    fun recordFeatureUsage(featureName: String, additionalContext: Map<String, String> = emptyMap()) {
        scope.launch {
            try {
                crashlytics.setCustomKey("last_feature_used", featureName)
                crashlytics.setCustomKey("feature_usage_timestamp", System.currentTimeMillis())
                
                additionalContext.forEach { (key, value) ->
                    crashlytics.setCustomKey("feature_$key", value)
                }
                
                Timber.d("Recorded feature usage: $featureName")
            } catch (e: Exception) {
                Timber.w(e, "Failed to record feature usage")
            }
        }
    }

    private fun logSlowOperation(type: String, operationName: String, timeMs: Long) {
        val message = "$type: $operationName took ${timeMs}ms"
        logNonFatalIssue(message, mapOf(
            "operation_type" to type,
            "operation_name" to operationName,
            "execution_time_ms" to timeMs.toString()
        ))
    }

    private fun recordFilterUsage(filterTypes: List<String>) {
        scope.launch {
            try {
                crashlytics.setCustomKey("filters_applied", filterTypes.joinToString(","))
                crashlytics.setCustomKey("filter_count", filterTypes.size)
                
                filterTypes.forEach { filterType ->
                    recordFeatureUsage("filter_$filterType")
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to record filter usage")
            }
        }
    }

    private fun recordScreenView(screenName: String, loadTimeMs: Long) {
        scope.launch {
            try {
                crashlytics.setCustomKey("last_screen", screenName)
                crashlytics.setCustomKey("screen_load_time_ms", loadTimeMs)
                recordFeatureUsage("screen_view", mapOf(
                    "screen_name" to screenName,
                    "load_time_ms" to loadTimeMs.toString()
                ))
            } catch (e: Exception) {
                Timber.w(e, "Failed to record screen view")
            }
        }
    }

    private fun recordCacheUsage(operation: String, hit: Boolean, timeMs: Long) {
        scope.launch {
            try {
                crashlytics.setCustomKey("last_cache_operation", operation)
                crashlytics.setCustomKey("cache_hit", hit)
                crashlytics.setCustomKey("cache_operation_time_ms", timeMs)
                
                recordFeatureUsage("cache_$operation", mapOf(
                    "cache_hit" to hit.toString(),
                    "time_ms" to timeMs.toString()
                ))
            } catch (e: Exception) {
                Timber.w(e, "Failed to record cache usage")
            }
        }
    }

    /**
     * Generate performance summary for debugging
     */
    fun getPerformanceSummary(): Map<String, Any> {
        return mapOf(
            "performance_monitoring_enabled" to remoteConfigManager.isPerformanceMonitoringEnabled(),
            "crash_reporting_enabled" to remoteConfigManager.isCrashReportingEnabled(),
            "max_query_time_ms" to remoteConfigManager.getMaxQueryTimeMs(),
            "max_cache_size_mb" to remoteConfigManager.getMaxCacheSizeMb(),
            "slow_query_threshold_ms" to SLOW_QUERY_THRESHOLD,
            "slow_ui_render_threshold_ms" to SLOW_UI_RENDER_THRESHOLD,
            "slow_screen_load_threshold_ms" to SLOW_SCREEN_LOAD_THRESHOLD
        )
    }

    /**
     * Cleanup resources when done
     */
    fun cleanup() {
        scope.cancel()
    }
}
