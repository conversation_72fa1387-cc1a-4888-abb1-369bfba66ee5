package com.tfkcolin.josyandchris

import android.app.Application
import com.google.firebase.FirebaseApp
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.firestoreSettings
import com.google.firebase.storage.FirebaseStorage
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import com.tfkcolin.josyandchris.util.logging.CrashlyticsTree
import kotlinx.coroutines.CoroutineExceptionHandler
import com.tfkcolin.josyandchris.ui.base.BaseViewModel

@HiltAndroidApp
class JACApplication : Application() {
    val globalHandler = CoroutineExceptionHandler { _, t ->
        Timber.e(t, "Uncaught coroutine error")
    }

    override fun onCreate() {
        BaseViewModel.initialize(this)
        super.onCreate()
        
        // Initialize Timber for logging
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            // In production, plant a tree that sends logs to Firebase Crashlytics
            Timber.plant(CrashlyticsTree())
        }
        FirebaseApp.initializeApp(applicationContext)
        
        // Temporarily disabled emulator for testing with production data
        // TODO: Add a build config flag to control emulator usage
        /*
        if(BuildConfig.DEBUG){
            // emulator host ********
            // local host **************
            val firestore = FirebaseFirestore.getInstance()
            firestore.useEmulator("**************", 8080)
            firestore.firestoreSettings = firestoreSettings { isPersistenceEnabled = true }
            FirebaseStorage.getInstance().useEmulator("**************", 9199)
        } else {
            // Enable Firestore persistence for production
            val firestore = FirebaseFirestore.getInstance()
            firestore.firestoreSettings = firestoreSettings { isPersistenceEnabled = true }
        }
        */
        
        // Always use production Firestore with persistence enabled
        val firestore = FirebaseFirestore.getInstance()
        firestore.firestoreSettings = firestoreSettings { isPersistenceEnabled = true }
    }
}