package com.tfkcolin.josyandchris.logic
/*
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.data.TransactionType
import java.util.*

object TransactionLogic {
    /**
     * @param transactions the list of transactions to work with
     * @param country if provided the resulting totals will only includes transactions performed
     * in this country
     * @param startDate date from which we want to have insight
     * @param endDate date to which we want to stop looking
     * @return a pair of containing the total of the money gained and lost
     * from the selected transaction
     */
    fun getTotalTransactionPrice(
        transactions: List<FinancialTransaction>,
        country: String? = null,
        startDate: Date? = null,
        endDate: Date? = null
    ): Pair<Int, Int> {
        var inputsTotalPrice = 0
        var outputsTotalPrice = 0

        transactions.forEach {
            val predicate = (if(country == null) true else it.country == country)
                    && (if(startDate == null) true else it.created >= startDate)
                    && (if(endDate == null) true else it.created <= endDate)
            if(it.transactionTypeIndex == TransactionType.INPUT.ordinal){
                if(predicate)
                    inputsTotalPrice += it.price
            }
            else if(it.transactionTypeIndex == TransactionType.OUTPUT.ordinal) {
                if(predicate)
                    outputsTotalPrice += it.price
            }
        }
        return Pair(inputsTotalPrice, outputsTotalPrice)
    }
}*/