package com.tfkcolin.josyandchris.logic
/*
import com.tfkcolin.josyandchris.data.Command
import java.util.*

object CommandLogic {
    /**
     * @param commands the list of transactions to work with
     * @param country if provided the resulting totals will only includes commands performed
     * in this country
     * @param startDate date from which we want to have insight
     * @param endDate date to which we want to stop looking
     * @return a pair of containing the total of the money gained and lost
     * from the selected transaction
     */
    fun getTotalCommandPrice(
        commands: List<Command>,
        clientName: String? = null,
        country: String? = null,
        startDate: Date? = null,
        endDate: Date? = null
    ): Pair<Int, Int> {
        var inputsTotalPrice = 0
        var outputsTotalPrice = 0

        commands.forEach {
            val predicate = (if(country == null) true else it.client.country == country)
                    && (if(clientName == null) true else it.client.name == clientName)
                    && (if(startDate == null) true else it.created >= startDate)
                    && (if(endDate == null) true else it.created <= endDate)
            if(predicate){
                inputsTotalPrice += it.products.sumOf { p -> p.unitSellingPrice }
                outputsTotalPrice += it.products.sumOf { p -> p.unitBuyingPrice }
            }
        }

        return Pair(
            inputsTotalPrice,
            outputsTotalPrice
        )
    }
}*/