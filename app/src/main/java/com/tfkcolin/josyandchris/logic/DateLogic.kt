package com.tfkcolin.josyandchris.logic

import android.os.Build
import androidx.annotation.RequiresApi
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

/**
 * @param month from 0 to 11
 */
fun Date.isMonth(month: Int) : Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
        val date = this.toLocalDate()
        return date.monthValue - 1 == month
    }
    else {
        val format = SimpleDateFormat("dd/MM/yyyy", Locale.FRANCE)
        println(format.format(this))
        println(" this = ${this.month}")
        this.month == month
    }
}

fun Date.isYear(year: Int) : Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
        val date = this.toLocalDate()
        date.year == year
    }
    else {
        println(" this = ${this.year}")
        this.year + 1900 == year
    }
}

@RequiresApi(Build.VERSION_CODES.O)
fun Date.toLocalDate(): LocalDate{
    return this.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
}
/*
@RequiresApi(Build.VERSION_CODES.O)
fun LocalDate.toDate(): Date{
    return Date.from(this.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant())
}*/