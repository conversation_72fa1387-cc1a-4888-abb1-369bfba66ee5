package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.util.safeCall
import com.google.firebase.firestore.DocumentSnapshot
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.query.CargoQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*
import timber.log.Timber
import com.tfkcolin.josyandchris.util.firebase.AggregateQueries
import com.tfkcolin.josyandchris.data.CargoStatus
import com.google.firebase.firestore.ListenerRegistration

@Singleton
class FirebaseCargoDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    
    companion object {
        private const val COLLECTION_CARGOS = "cargos"
        private const val PAGE_SIZE = 20
    }
    
    // QueryBuilder instance for flexible query construction
    private val queryBuilder = CargoQueryBuilder(firestore)
    
    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        try {
            val settings = firestore.firestoreSettings
            // Note: Firestore settings are typically configured at app startup
            // and don't need to be reconfigured here
        } catch (e: Exception) {
            // Handle any Firestore initialization errors
        }
    }
    
suspend fun createCargo(cargo: Cargo): Result<String> = safeCall("createCargo") {
        val docRef = firestore.collection(COLLECTION_CARGOS).document()
        val cargoWithId = cargo.copy(
            id = docRef.id,
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(cargo.origin, cargo.destination))
        )
        docRef.set(cargoWithId).await()
        docRef.id
    }
    
suspend fun getCargo(id: String): Result<Cargo> = safeCall("getCargo") {
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .document(id)
            .get()
            .await()
        
        val cargo = safeConvertToCargo(snapshot)
            ?: throw Exception("Cargo not found")
        cargo
    }
    
suspend fun updateCargo(cargo: Cargo): Result<Unit> = safeCall("updateCargo") {
        val cargoWithSearchIndex = cargo.copy(
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(cargo.origin, cargo.destination))
        )
        firestore.collection(COLLECTION_CARGOS)
            .document(cargo.id)
            .set(cargoWithSearchIndex)
            .await()
        Unit
    }
    
suspend fun deleteCargo(id: String): Result<Unit> = safeCall("deleteCargo") {
        firestore.collection(COLLECTION_CARGOS)
            .document(id)
            .delete()
            .await()
        Unit
    }
    
    fun getCargosFlow(): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getCargosPagingData(): Flow<PagingData<Cargo>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                // Use EnhancedFirebasePagingSource instead of FirebaseCargoPagingSource
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = QueryFilters(),
                    cursorConfig = CursorConfig(limit = PAGE_SIZE)
                ) { doc ->
                    safeConvertToCargo(doc)
                }
            }
        ).flow
    }
    
suspend fun getCargosByOrigin(origin: String): Result<List<Cargo>> = safeCall("getCargosByOrigin") {
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
            .get()
            .await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
suspend fun getCargosByDestination(destination: String): Result<List<Cargo>> = safeCall("getCargosByDestination") {
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("destination", destination)
            .get()
            .await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
suspend fun getCargosByStatus(statusIndex: Int): Result<List<Cargo>> = safeCall("getCargosByStatus") {
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("statusIndex", statusIndex)
            .get()
            .await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
suspend fun getCargosByRoute(origin: String, destination: String): Result<List<Cargo>> = safeCall("getCargosByRoute") {
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
            .whereEqualTo("destination", destination)
            .get()
            .await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    fun getCargosByOriginFlow(origin: String): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getCargosByDestinationFlow(destination: String): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("destination", destination)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getCargosByStatusFlow(statusIndex: Int): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("statusIndex", statusIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getCargosByRouteFlow(origin: String, destination: String): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
            .whereEqualTo("destination", destination)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
suspend fun searchCargos(query: String): Result<List<Cargo>> = safeCall("searchCargos") {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
return@safeCall emptyList<Cargo>()
        }
        
        val snapshot = firestore.collection(COLLECTION_CARGOS)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .get()
            .await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    fun searchCargosFlow(query: String): Flow<List<Cargo>> = callbackFlow {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
            trySend(emptyList())
            awaitClose { /* no listener to remove */ }
            return@callbackFlow
        }
        
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    // ===== NEW QUERYBUILDER-BASED METHODS =====
    
    /**
     * Get cargos with flexible filtering using QueryBuilder
     */
    suspend fun getCargosWithFilters(
        filters: QueryFilters,
        limit: Int = PAGE_SIZE
): Result<List<Cargo>> = safeCall("getCargosWithFilters") {
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    /**
     * Get cargos with filters and pagination support
     */
    fun getCargosPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<PagingData<Cargo>> {
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    safeConvertToCargo(doc)
                }
            }
        ).flow
    }
    
    /**
     * Get cargos flow with flexible filtering
     */
    fun getCargosFlowWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<List<Cargo>> = callbackFlow {
        val query = queryBuilder.build(filters, cursorConfig)
        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }
            
            val cargos = snapshot?.documents?.mapNotNull { doc ->
                safeConvertToCargo(doc)
            } ?: emptyList()
            
            trySend(cargos)
        }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Get cargos by date range
     */
    suspend fun getCargosByDateRange(
        startDate: Date,
        endDate: Date,
        statusIndex: Int? = null,
        limit: Int = PAGE_SIZE
): Result<List<Cargo>> = safeCall("getCargosByDateRange") {
        val filters = QueryFilters(
            dateRange = DateRange(startDate, endDate),
            statusIndex = statusIndex
        )
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    /**
     * Get cargos by route with status filtering
     */
    suspend fun getCargosByRouteAndStatus(
        origin: String,
        destination: String,
        statusIndex: Int? = null,
        limit: Int = PAGE_SIZE
): Result<List<Cargo>> = safeCall("getCargosByRouteAndStatus") {
        val additionalFilters = mutableMapOf<String, Any>()
        additionalFilters["origin"] = origin
        additionalFilters["destination"] = destination
        
        val filters = QueryFilters(
            statusIndex = statusIndex,
            additionalFilters = additionalFilters
        )
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    /**
     * Get cargos with multiple criteria
     */
    suspend fun getCargosByMultipleCriteria(
        origin: String? = null,
        destination: String? = null,
        statusIndex: Int? = null,
        searchTerm: String? = null,
        dateRange: DateRange? = null,
        limit: Int = PAGE_SIZE
): Result<List<Cargo>> = safeCall("getCargosByMultipleCriteria") {
        val additionalFilters = mutableMapOf<String, Any>()
        origin?.let { additionalFilters["origin"] = it }
        destination?.let { additionalFilters["destination"] = it }
        
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val cargos = snapshot.documents.mapNotNull { doc ->
            safeConvertToCargo(doc)
        }
        cargos
    }
    
    // ===== SELECTIVE REAL-TIME LISTENERS =====
    
    /**
     * Listen for updates with optional filters
     * This is the main selective real-time listener method
     */
    fun listenForUpdates(filters: QueryFilters? = null): Flow<List<Cargo>> = callbackFlow {
        val query = if (filters != null) {
            queryBuilder.build(filters, CursorConfig())
        } else {
            firestore.collection(COLLECTION_CARGOS)
                .orderBy("created", Query.Direction.DESCENDING)
        }
        
        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }
            
            val cargos = snapshot?.documents?.mapNotNull { doc ->
                safeConvertToCargo(doc)
            } ?: emptyList()
            
            trySend(cargos)
        }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Listen for updates to a specific cargo
     */
    fun listenForCargoUpdates(cargoId: String): Flow<Cargo?> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .document(cargoId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargo = snapshot?.let { safeConvertToCargo(it) }
                trySend(cargo)
            }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Listen for updates to cargos by status
     */
    fun listenForCargosByStatusUpdates(statusIndex: Int): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("statusIndex", statusIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Listen for updates to cargos by route
     */
    fun listenForCargosByRouteUpdates(origin: String, destination: String): Flow<List<Cargo>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
            .whereEqualTo("destination", destination)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val cargos = snapshot?.documents?.mapNotNull { doc ->
                    safeConvertToCargo(doc)
                } ?: emptyList()
                
                trySend(cargos)
            }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Safely convert a DocumentSnapshot to Cargo, handling potential data type mismatches
     */
    private fun safeConvertToCargo(doc: DocumentSnapshot): Cargo? {
        return try {
            // First try direct conversion
            doc.toObject<Cargo>()?.copy(id = doc.id)
        } catch (e: Exception) {
            // If direct conversion fails, try manual mapping
            Timber.w(e, "Failed to convert document ${doc.id} to Cargo, attempting manual conversion")
            
            try {
                val data = doc.data ?: return null
                
                // Handle searchIndex field that might be String instead of List<String>
                val searchIndexRaw = data["searchIndex"]
                val searchIndex = when (searchIndexRaw) {
                    is List<*> -> searchIndexRaw.filterIsInstance<String>()
                    is String -> listOf(searchIndexRaw) // Convert single string to list
                    else -> emptyList()
                }
                
                Cargo(
                    id = doc.id,
                    origin = data["origin"] as? String ?: "",
                    destination = data["destination"] as? String ?: "",
                    statusIndex = (data["statusIndex"] as? Long)?.toInt() ?: 0,
                    departureDate = (data["departureDate"] as? com.google.firebase.Timestamp)?.toDate(),
                    estimatedArrivalDate = (data["estimatedArrivalDate"] as? com.google.firebase.Timestamp)?.toDate(),
                    actualArrivalDate = (data["actualArrivalDate"] as? com.google.firebase.Timestamp)?.toDate(),
                    searchIndex = searchIndex,
                    created = (data["created"] as? com.google.firebase.Timestamp)?.toDate() ?: Date()
                )
            } catch (e: Exception) {
                Timber.e(e, "Failed to manually convert document ${doc.id} to Cargo")
                null
            }
        }
    }
    
    // ===== AGGREGATE COUNT METHODS =====
    
    /**
     * Get total count of all cargos (uses aggregate query for efficiency)
     */
suspend fun getCargosCount(): Result<Long> = safeCall("getCargosCount") {
        val query = firestore.collection(COLLECTION_CARGOS)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Get count of cargos by status (uses aggregate query for efficiency)
     */
suspend fun getCargosCountByStatus(statusIndex: Int): Result<Long> = safeCall("getCargosCountByStatus") {
        val query = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("statusIndex", statusIndex)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Get counts for all cargo statuses in one go
     * Returns a map of statusIndex to count
     */
suspend fun getCargosCountByAllStatuses(): Result<Map<Int, Long>> = safeCall("getCargosCountByAllStatuses") {
        val counts = mutableMapOf<Int, Long>()
        
        // Create queries for each status
        val queries = CargoStatus.values().map { status ->
            status.ordinal to firestore.collection(COLLECTION_CARGOS)
                .whereEqualTo("statusIndex", status.ordinal)
        }
        
        // Execute all count queries
        queries.forEach { (statusIndex, query) ->
            val count = AggregateQueries.getCount(query)
            counts[statusIndex] = count
        }
        
        counts
    }
    
    /**
     * Get count of cargos by origin
     */
suspend fun getCargosCountByOrigin(origin: String): Result<Long> = safeCall("getCargosCountByOrigin") {
        val query = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("origin", origin)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Get count of cargos by destination
     */
suspend fun getCargosCountByDestination(destination: String): Result<Long> = safeCall("getCargosCountByDestination") {
        val query = firestore.collection(COLLECTION_CARGOS)
            .whereEqualTo("destination", destination)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Flow-based count of cargos by all statuses with real-time updates
     */
    fun getCargosCountByAllStatusesFlow(): Flow<Map<Int, Long>> = callbackFlow {
        val statuses = CargoStatus.values()
        val listeners = mutableListOf<ListenerRegistration>()
        val counts = mutableMapOf<Int, Long>()
        
        // Initialize all counts to 0
        statuses.forEach { status ->
            counts[status.ordinal] = 0L
        }
        
        // Set up listeners for each status
        statuses.forEach { status ->
            val query = firestore.collection(COLLECTION_CARGOS)
                .whereEqualTo("statusIndex", status.ordinal)
            
            // Use regular query with snapshot listener and count documents
            val listener = query.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    // Continue with other listeners even if one fails
                    return@addSnapshotListener
                }
                
                snapshot?.let {
                    counts[status.ordinal] = it.size().toLong()
                    // Send updated counts map
                    trySend(counts.toMap())
                }
            }
            
            listeners.add(listener)
        }
        
        // Send initial state
        send(counts.toMap())
        
        awaitClose {
            // Clean up all listeners
            listeners.forEach { it.remove() }
        }
    }
}
