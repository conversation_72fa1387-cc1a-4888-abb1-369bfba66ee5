package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import com.tfkcolin.josyandchris.util.query.QueryBuilderFactory
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*

@Singleton
class FirebaseCountryDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    
    companion object {
        private const val COLLECTION_COUNTRIES = "countries"
        private const val PAGE_SIZE = 20
    }
    
    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        // Set persistence settings without using toBuilder
        val settings = firestore.firestoreSettings
        firestore.firestoreSettings = settings
    }
    
suspend fun createCountry(country: CountryData): Result<String> = safeCall("Create Country") {
        val docRef = firestore.collection(COLLECTION_COUNTRIES).document()
        val countryWithId = country.copy(
            id = docRef.id,
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(country.name))
        )
        docRef.set(countryWithId).await()
        docRef.id
    }
    
suspend fun getCountry(id: String): Result<CountryData> = safeCall("Get Country") {
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .document(id)
            .get()
            .await()
        
        snapshot.toObject<CountryData>()?.copy(id = snapshot.id)
            ?: throw Exception("Country not found")
    }
    
suspend fun updateCountry(country: CountryData): Result<Unit> = safeCall("Update Country") {
        val countryWithSearchIndex = country.copy(
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(country.name))
        )
        firestore.collection(COLLECTION_COUNTRIES)
            .document(country.id)
            .set(countryWithSearchIndex)
            .await()
    }
    
suspend fun deleteCountry(id: String): Result<Unit> = safeCall("Delete Country") {
        firestore.collection(COLLECTION_COUNTRIES)
            .document(id)
            .delete()
            .await()
    }
    
suspend fun loadCountries(limit: Int = 20, startAfter: String? = null): Result<List<CountryData>> = safeCall("Load Countries") {
        var query = firestore.collection(COLLECTION_COUNTRIES)
            .orderBy("name", Query.Direction.ASCENDING)
            .limit(limit.toLong())
        
        // Apply pagination cursor if provided
        startAfter?.let { cursor ->
            // First, get the document snapshot for the cursor
            val cursorDoc = firestore.collection(COLLECTION_COUNTRIES)
                .document(cursor)
                .get()
                .await()
            
            if (cursorDoc.exists()) {
                query = query.startAfter(cursorDoc)
            }
        }
        
        val snapshot = query.get().await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
fun getCountriesFlow(): Flow<List<CountryData>> = flow {
        val result = safeCall("Get Countries Flow") { 
            val snapshot = firestore.collection(COLLECTION_COUNTRIES)
                .orderBy("name", Query.Direction.ASCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject<CountryData>()?.copy(id = doc.id)
            }
        }
        
        if (result.isSuccess) {
            emit(result.getOrNull() ?: emptyList())
        } else {
            // Propagate error but don't crash
            val exception = result.exceptionOrNull() ?: Exception("Unknown error loading countries")
            timber.log.Timber.e(exception, "Error loading countries flow")
            emit(emptyList())
        }
    }
    
suspend fun getCountriesByRegion(region: String): Result<List<CountryData>> = safeCall("Get Countries By Region") {
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .whereEqualTo("region", region)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
suspend fun getCountriesByName(name: String): Result<List<CountryData>> = safeCall("Get Countries By Name") {
        val queryTokens = SearchIndexUtils.generateQueryTokens(name)
        if (queryTokens.isEmpty()) {
            return@safeCall emptyList<CountryData>()
        }
        
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
suspend fun getAllCountries(): Result<List<CountryData>> = safeCall("Get All Countries") {
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .orderBy("name", Query.Direction.ASCENDING)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
    // Advanced querying
suspend fun searchCountries(query: String): Result<List<CountryData>> = safeCall("Search Countries") {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
            return@safeCall emptyList<CountryData>()
        }
        
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
    fun searchCountriesFlow(query: String): Flow<List<CountryData>> = callbackFlow {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
            trySend(emptyList())
            awaitClose { /* no listener to remove */ }
            return@callbackFlow
        }
        
        val listener = firestore.collection(COLLECTION_COUNTRIES)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    timber.log.Timber.e(error, "Error in searchCountriesFlow snapshot listener")
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                
                val countries = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<CountryData>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(countries)
            }
        
        awaitClose { listener.remove() }
    }
    
    // Additional methods needed for the repository implementation
    
    fun getCountriesPagingData(): Flow<PagingData<CountryData>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                object : androidx.paging.PagingSource<Int, CountryData>() {
                    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CountryData> {
                        return try {
                            val page = params.key ?: 0
                            val result = getAllCountries()
                            
                            if (result.isSuccess) {
                                val data = result.getOrNull() ?: emptyList()
                                val start = page * PAGE_SIZE
                                val end = minOf((page + 1) * PAGE_SIZE, data.size)
                                
                                val pagedData = if (start < data.size) {
                                    data.subList(start, end)
                                } else {
                                    emptyList()
                                }
                                
                                LoadResult.Page(
                                    data = pagedData,
                                    prevKey = if (page > 0) page - 1 else null,
                                    nextKey = if (end < data.size) page + 1 else null
                                )
                            } else {
                                LoadResult.Error(Exception("Failed to load countries"))
                            }
                        } catch (e: Exception) {
                            LoadResult.Error(e)
                        }
                    }
                    
                    override fun getRefreshKey(state: androidx.paging.PagingState<Int, CountryData>): Int? {
                        return state.anchorPosition?.let { anchorPosition ->
                            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
                        }
                    }
                }
            }
        ).flow
    }
    
    fun getCountriesPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<PagingData<CountryData>> {
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                object : androidx.paging.PagingSource<Int, CountryData>() {
                    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CountryData> {
                        return try {
                            val page = params.key ?: 0
                            
                            // Apply filters manually since we don't have a proper QueryBuilder for countries
                            val result = getAllCountries()
                            
                            if (result.isSuccess) {
                                var filteredData = result.getOrNull() ?: emptyList()
                                
                                // Apply name filter
                                filters.additionalFilters["name"]?.let { name ->
                                    filteredData = filteredData.filter { it.name.contains(name.toString(), true) }
                                }
                                
                                // Apply devise filter
                                filters.additionalFilters["devise"]?.let { devise ->
                                    filteredData = filteredData.filter { it.devise == devise.toString() }
                                }
                                
                                // Apply search tokens filter
                                filters.searchTokens?.let { tokens ->
                                    if (tokens.isNotEmpty()) {
                                        filteredData = filteredData.filter { country ->
                                            country.searchIndex.any { token ->
                                                tokens.any { searchToken ->
                                                    token.contains(searchToken, true)
                                                }
                                            }
                                        }
                                    }
                                }
                                
                                // Apply pagination
                                val start = page * params.loadSize
                                val end = minOf(start + params.loadSize, filteredData.size)
                                
                                val pagedData = if (start < filteredData.size) {
                                    filteredData.subList(start, end)
                                } else {
                                    emptyList()
                                }
                                
                                LoadResult.Page(
                                    data = pagedData,
                                    prevKey = if (page > 0) page - 1 else null,
                                    nextKey = if (end < filteredData.size) page + 1 else null
                                )
                            } else {
                                LoadResult.Error(Exception("Failed to load countries"))
                            }
                        } catch (e: Exception) {
                            LoadResult.Error(e)
                        }
                    }
                    
                    override fun getRefreshKey(state: androidx.paging.PagingState<Int, CountryData>): Int? {
                        return state.anchorPosition?.let { anchorPosition ->
                            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
                        }
                    }
                }
            }
        ).flow
    }
    
    // Implementation for missing methods needed by CountryRepository
    
suspend fun getCountriesByDevise(devise: String): Result<List<CountryData>> = safeCall("Get Countries By Devise") {
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .whereEqualTo("devise", devise)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
suspend fun getCountriesBySearchTokens(tokens: List<String>): Result<List<CountryData>> = safeCall("Get Countries By Search Tokens") {
        if (tokens.isEmpty()) {
            return@safeCall emptyList<CountryData>()
        }
        
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .whereArrayContainsAny("searchIndex", tokens)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
    fun getCountriesByNameFlow(name: String): Flow<List<CountryData>> = callbackFlow {
        val queryTokens = SearchIndexUtils.generateQueryTokens(name)
        if (queryTokens.isEmpty()) {
            trySend(emptyList())
            awaitClose { /* no listener to remove */ }
            return@callbackFlow
        }
        
        val listener = firestore.collection(COLLECTION_COUNTRIES)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    timber.log.Timber.e(error, "Error in getCountriesByNameFlow snapshot listener")
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                
                val countries = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<CountryData>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(countries)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getCountriesByDeviseFlow(devise: String): Flow<List<CountryData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_COUNTRIES)
            .whereEqualTo("devise", devise)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    timber.log.Timber.e(error, "Error in getCountriesByDeviseFlow snapshot listener")
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                
                val countries = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<CountryData>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(countries)
            }
        
        awaitClose { listener.remove() }
    }
    
suspend fun getCountriesAlphabetically(): Result<List<CountryData>> = safeCall("Get Countries Alphabetically") {
        val snapshot = firestore.collection(COLLECTION_COUNTRIES)
            .orderBy("name", Query.Direction.ASCENDING)
            .get()
            .await()
        
        snapshot.documents.mapNotNull { doc ->
            doc.toObject<CountryData>()?.copy(id = doc.id)
        }
    }
    
    fun getCountriesAlphabeticallyFlow(): Flow<List<CountryData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_COUNTRIES)
            .orderBy("name", Query.Direction.ASCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    timber.log.Timber.e(error, "Error in getCountriesAlphabeticallyFlow snapshot listener")
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                
                val countries = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<CountryData>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(countries)
            }
        
        awaitClose { listener.remove() }
    }
}
