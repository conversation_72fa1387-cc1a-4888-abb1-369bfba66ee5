package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.ImageData
// import removed
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import com.tfkcolin.josyandchris.util.query.QueryBuilderFactory
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*

@Singleton
class FirebaseImageDataDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {

    companion object {
        private const val COLLECTION_IMAGES = "images_data"
        private const val PAGE_SIZE = 20
    }

    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        // No need to use toBuilder
        val settings = firestore.firestoreSettings
        firestore.firestoreSettings = settings
    }

    suspend fun createImage(image: ImageData): Result<String> = safeCall("Create Image") {
        val docRef = firestore.collection(COLLECTION_IMAGES).document()
        val imgWithIndex = image.copy(
            id = docRef.id,
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(image.category, image.genre, image.name ?: "")).joinToString(" ")
        )
        docRef.set(imgWithIndex).await()
        docRef.id
    }

    suspend fun getImage(id: String): Result<ImageData> = safeCall("Get Image") {
        val snapshot = firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .get()
            .await()

        snapshot.toObject<ImageData>()?.copy(id = snapshot.id)
            ?: throw Exception("Image not found")
    }

    suspend fun updateImage(image: ImageData): Result<Unit> = safeCall("Update Image") {
        val imgWithIndex = image.copy(
            searchIndex = SearchIndexUtils.generateSearchIndex(listOf(image.category, image.genre, image.name ?: "")).joinToString(" ")
        )
        firestore.collection(COLLECTION_IMAGES)
            .document(image.id)
            .set(imgWithIndex)
            .await()
    }

    suspend fun updateImageFields(id: String, fields: Map<String, Any>): Result<Unit> = safeCall("Update Image Fields") {
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .update(fields)
            .await()
    }

    suspend fun deleteImage(id: String): Result<Unit> = safeCall("Delete Image") {
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .delete()
            .await()
    }

    fun getImagesFlow(): Flow<List<ImageData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_IMAGES)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val images = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                } ?: emptyList()

                trySend(images)
            }

        awaitClose { listener.remove() }
    }

    suspend fun getImagesByType(typeIndex: Int): Result<List<ImageData>> = safeCall("Get Images By Type") {
        val snapshot = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("typeIndex", typeIndex)
            .get()
            .await()

        snapshot.documents.mapNotNull { doc ->
            doc.toObject<ImageData>()?.copy(id = doc.id)
        }
    }

    suspend fun getImagesByParent(parentId: String): Result<List<ImageData>> = safeCall("Get Images By Parent") {
        val snapshot = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("parentId", parentId)
            .get()
            .await()

        snapshot.documents.mapNotNull { doc ->
            doc.toObject<ImageData>()?.copy(id = doc.id)
        }
    }

    suspend fun getImagesByParentAndType(parentId: String, typeIndex: Int): Result<List<ImageData>> = safeCall("Get Images By Parent And Type") {
        val snapshot = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("parentId", parentId)
            .whereEqualTo("typeIndex", typeIndex)
            .get()
            .await()

        snapshot.documents.mapNotNull { doc ->
            doc.toObject<ImageData>()?.copy(id = doc.id)
        }
    }

    fun getImagesByTypeFlow(typeIndex: Int): Flow<List<ImageData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("typeIndex", typeIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val images = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                } ?: emptyList()

                trySend(images)
            }

        awaitClose { listener.remove() }
    }

    fun getImagesByParentFlow(parentId: String): Flow<List<ImageData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("parentId", parentId)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val images = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                } ?: emptyList()

                trySend(images)
            }

        awaitClose { listener.remove() }
    }

    fun getImagesByParentAndTypeFlow(parentId: String, typeIndex: Int): Flow<List<ImageData>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("parentId", parentId)
            .whereEqualTo("typeIndex", typeIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val images = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                } ?: emptyList()

                trySend(images)
            }

        awaitClose { listener.remove() }
    }

    // ===== PAGING DATA =====

    /**
     * Get images with paging support
     */
    fun getImagesPagingData(): Flow<PagingData<ImageData>> {
        val queryBuilder = QueryBuilderFactory(firestore).createImageQueryBuilder()
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource<ImageData>(
                    queryBuilder = queryBuilder,
                    filters = QueryFilters(),
                    cursorConfig = CursorConfig(limit = PAGE_SIZE)
                ) { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    /**
     * Get images with filters and pagination support
     */
    fun getImagesPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig(limit = PAGE_SIZE)
    ): Flow<PagingData<ImageData>> {
        val queryBuilder = QueryBuilderFactory(firestore).createImageQueryBuilder()
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource<ImageData>(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    /**
     * Get images by category with pagination support
     */
    fun getImagesByCategoryPagingData(
        category: String,
        cursorConfig: CursorConfig = CursorConfig(limit = PAGE_SIZE)
    ): Flow<PagingData<ImageData>> {
        val queryBuilder = QueryBuilderFactory(firestore).createImageQueryBuilder()
        val filters = QueryFilters(
            additionalFilters = mapOf("category" to category)
        )
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource<ImageData>(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    /**
     * Get images by genre with pagination support
     */
    fun getImagesByGenrePagingData(
        genre: String,
        cursorConfig: CursorConfig = CursorConfig(limit = PAGE_SIZE)
    ): Flow<PagingData<ImageData>> {
        val queryBuilder = QueryBuilderFactory(firestore).createImageQueryBuilder()
        val filters = QueryFilters(
            additionalFilters = mapOf("genre" to genre)
        )
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource<ImageData>(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<ImageData>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    // ===== BATCH OPERATIONS =====

    suspend fun createImages(images: List<ImageData>): Result<List<String>> = safeCall("Create Multiple Images") {
        val batch = firestore.batch()
        val ids = mutableListOf<String>()

        images.forEach { image ->
            val docRef = firestore.collection(COLLECTION_IMAGES).document()
            ids.add(docRef.id)
            val imgWithIndex = image.copy(
                id = docRef.id,
                searchIndex = SearchIndexUtils.generateSearchIndex(listOf(image.category, image.genre, image.name ?: "")).joinToString(" ")
            )
            batch.set(docRef, imgWithIndex)
        }

        batch.commit().await()
        ids
    }

    suspend fun deleteImagesByParent(parentId: String): Result<Unit> = safeCall("Delete Images By Parent") {
        val snapshot = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("parentId", parentId)
            .get()
            .await()

        if (!snapshot.isEmpty) {
            val batch = firestore.batch()
            snapshot.documents.forEach { doc ->
                batch.delete(doc.reference)
            }
            batch.commit().await()
        }
    }
}
