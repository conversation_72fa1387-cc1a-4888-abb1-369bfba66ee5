package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.util.query.CommandQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import kotlinx.coroutines.tasks.await

/**
 * Legacy PagingSource - Kept for backward compatibility
 * Use EnhancedFirebasePagingSource with QueryBuilder for new implementations
 */
class FirebaseCommandPagingSource(
    private val firestore: FirebaseFirestore,
    private val filters: QueryFilters = QueryFilters()
) : PagingSource<DocumentSnapshot, Command>() {
    
    companion object {
        private const val COLLECTION_COMMANDS = "commands"
    }
    
    private val queryBuilder = CommandQueryBuilder(firestore)
    
    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, Command> {
        return try {
            val cursor = CursorConfig(
                startAfter = params.key,
                limit = params.loadSize
            )
            
            val query = queryBuilder.build(filters, cursor)
            val snapshot = query.get().await()
            
            val commands = snapshot.documents.mapNotNull { doc ->
                doc.toObject<Command>()?.copy(id = doc.id)
            }
            
            val nextKey = if (snapshot.documents.isEmpty()) null else snapshot.documents.last()
            
            LoadResult.Page(
                data = commands,
                prevKey = null, // Only paging forward
                nextKey = nextKey
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }
    
    override fun getRefreshKey(state: PagingState<DocumentSnapshot, Command>): DocumentSnapshot? {
        return null
    }
}
