package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.Shipment
import com.google.firebase.firestore.ListenerRegistration
import com.tfkcolin.josyandchris.util.firebase.AggregateQueries
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.query.ShipmentQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*

@Singleton
class FirebaseShipmentDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    
    companion object {
        private const val COLLECTION_SHIPMENTS = "shipments"
        private const val PAGE_SIZE = 20
    }
    
    // QueryBuilder instance for flexible query construction
    private val queryBuilder = ShipmentQueryBuilder(firestore)
    
    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        // No need to use toBuilder anymore
        val settings = firestore.firestoreSettings
        firestore.firestoreSettings = settings
    }
    
suspend fun createShipment(shipment: Shipment): Result<String> = safeCall("Create Shipment") {
        val docRef = firestore.collection(COLLECTION_SHIPMENTS).document()
        val shipmentWithId = shipment.copy(
            id = docRef.id,
            searchIndex = generateSearchIndex(shipment)
        )
        docRef.set(shipmentWithId).await()
        docRef.id
    }
    
    private fun generateSearchIndex(shipment: Shipment): List<String> {
        return SearchIndexUtils.generateSearchIndex(listOf(
            shipment.clientName,
            shipment.clientPhone
            // Shipment doesn't have clientEmail or address fields according to the class definition
        ))
    }
    
suspend fun getShipment(id: String): Result<Shipment> = safeCall("Get Shipment") {
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .document(id)
            .get()
            .await()
        
        val shipment = snapshot.toObject<Shipment>()?.copy(id = snapshot.id)
            ?: throw Exception("Shipment not found")
        shipment
    }
    
suspend fun updateShipment(shipment: Shipment): Result<Unit> = safeCall("Update Shipment") {
        val shipmentWithSearchIndex = shipment.copy(
            searchIndex = generateSearchIndex(shipment)
        )
        firestore.collection(COLLECTION_SHIPMENTS)
            .document(shipment.id)
            .set(shipmentWithSearchIndex)
            .await()
        Unit
    }
    
suspend fun deleteShipment(id: String): Result<Unit> = safeCall("Delete Shipment") {
        firestore.collection(COLLECTION_SHIPMENTS)
            .document(id)
            .delete()
            .await()
        Unit
    }
    
    fun getShipmentsFlow(): Flow<List<Shipment>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getShipmentsPagingData(): Flow<PagingData<Shipment>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = QueryFilters(),
                    cursorConfig = CursorConfig(limit = PAGE_SIZE)
                ) { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                }
            }
        ).flow
    }
    
suspend fun getShipmentsByCargo(cargoId: String): Result<List<Shipment>> = safeCall("Get Shipments By Cargo") {
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("cargoId", cargoId)
            .get()
            .await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
suspend fun getShipmentsByClient(clientName: String): Result<List<Shipment>> = safeCall("Get Shipments By Client") {
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("clientName", clientName)
            .get()
            .await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
suspend fun getShipmentsByStatus(statusIndex: Int): Result<List<Shipment>> = safeCall("Get Shipments By Status") {
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("statusIndex", statusIndex)
            .get()
            .await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
suspend fun getShipmentsByClientPhone(clientPhone: String): Result<List<Shipment>> = safeCall("Get Shipments By Client Phone") {
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("clientPhone", clientPhone)
            .get()
            .await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
    fun getShipmentsByCargoFlow(cargoId: String): Flow<List<Shipment>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("cargoId", cargoId)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getShipmentsByClientFlow(clientName: String): Flow<List<Shipment>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("clientName", clientName)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getShipmentsByStatusFlow(statusIndex: Int): Flow<List<Shipment>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("statusIndex", statusIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getShipmentsByClientPhoneFlow(clientPhone: String): Flow<List<Shipment>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("clientPhone", clientPhone)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
suspend fun searchShipments(query: String): Result<List<Shipment>> = safeCall("Search Shipments") {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
            return@safeCall emptyList<Shipment>()
        }
        
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .get()
            .await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
    fun searchShipmentsFlow(query: String): Flow<List<Shipment>> = callbackFlow {
        val queryTokens = SearchIndexUtils.generateQueryTokens(query)
        if (queryTokens.isEmpty()) {
            trySend(emptyList())
            awaitClose { /* no listener to remove */ }
            return@callbackFlow
        }
        
        val listener = firestore.collection(COLLECTION_SHIPMENTS)
            .whereArrayContainsAny("searchIndex", queryTokens)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val shipments = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(shipments)
            }
        
        awaitClose { listener.remove() }
    }
    
    // ===== QUERYBUILDER-BASED METHODS =====
    
    /**
     * Get shipments with flexible filtering using QueryBuilder
     */
    suspend fun getShipmentsWithFilters(
        filters: QueryFilters,
        limit: Int = PAGE_SIZE
    ): Result<List<Shipment>> = safeCall("Get Shipments With Filters") {
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
    /**
     * Get shipments with filters and pagination support
     */
    fun getShipmentsPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<PagingData<Shipment>> {
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<Shipment>()?.copy(id = doc.id)
                }
            }
        ).flow
    }
    
    /**
     * Get shipments flow with flexible filtering
     */
    fun getShipmentsFlowWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<List<Shipment>> = callbackFlow {
        val query = queryBuilder.build(filters, cursor = cursorConfig)
        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }
            
            val shipments = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<Shipment>()?.copy(id = doc.id)
            } ?: emptyList()
            
            trySend(shipments)
        }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Get shipments with multiple criteria
     */
    suspend fun getShipmentsByMultipleCriteria(
        cargoId: String? = null,
        clientName: String? = null,
        clientPhone: String? = null,
        statusIndex: Int? = null,
        searchTerm: String? = null,
        dateRange: DateRange? = null,
        limit: Int = PAGE_SIZE
    ): Result<List<Shipment>> = safeCall("Get Shipments By Multiple Criteria") {
        val additionalFilters = mutableMapOf<String, Any>()
        cargoId?.let { additionalFilters["cargoId"] = it }
        clientName?.let { additionalFilters["clientName"] = it }
        clientPhone?.let { additionalFilters["clientPhone"] = it }
        
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val shipments = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Shipment>()?.copy(id = doc.id)
        }
        shipments
    }
    
    // ===== AGGREGATE COUNT METHODS =====
    
    /**
     * Get count of shipments by cargo (uses aggregate query for efficiency)
     */
suspend fun getShipmentsCountByCargo(cargoId: String): Result<Long> = safeCall("Get Shipments Count By Cargo") {
        val query = firestore.collection(COLLECTION_SHIPMENTS)
            .whereEqualTo("cargoId", cargoId)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Get counts for all cargos
     * Returns a map of cargoId to count
     */
suspend fun getShipmentsCountByAllCargos(): Result<Map<String, Long>> = safeCall("Get Shipments Count By All Cargos") {
        val counts = mutableMapOf<String, Long>()
        
        // First get all unique cargo IDs
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .get()
            .await()
        
        val cargoIds = snapshot.documents
            .mapNotNull { it.toObject<Shipment>()?.cargoId }
            .distinct()
        
        // Get count for each cargo
        cargoIds.forEach { cargoId ->
            val query = firestore.collection(COLLECTION_SHIPMENTS)
                .whereEqualTo("cargoId", cargoId)
            val count = AggregateQueries.getCount(query)
            counts[cargoId] = count
        }
        
        counts
    }
    
    /**
     * Flow-based count of shipments by cargo with real-time updates
     * Returns Map<cargoId, count>
     */
    fun getShipmentsCountByCargoFlow(): Flow<Map<String, Long>> = callbackFlow {
        val listeners = mutableListOf<ListenerRegistration>()
        val counts = mutableMapOf<String, Long>()
        
        // First get all unique cargo IDs
        val snapshot = firestore.collection(COLLECTION_SHIPMENTS)
            .get()
            .await()
        
        val cargoIds = snapshot.documents
            .mapNotNull { it.toObject<Shipment>()?.cargoId }
            .distinct()
        
        // Initialize counts
        cargoIds.forEach { cargoId ->
            counts[cargoId] = 0L
        }
        
        // Set up listeners for each cargo
        cargoIds.forEach { cargoId ->
            val query = firestore.collection(COLLECTION_SHIPMENTS)
                .whereEqualTo("cargoId", cargoId)
            
            // Use regular query with snapshot listener and count documents
            val listener = query.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    // Continue with other listeners even if one fails
                    return@addSnapshotListener
                }
                
                snapshot?.let {
                    counts[cargoId] = it.size().toLong()
                    // Send updated counts map
                    trySend(counts.toMap())
                }
            }
            
            listeners.add(listener)
        }
        
        // Send initial state
        send(counts.toMap())
        
        awaitClose {
            // Clean up all listeners
            listeners.forEach { it.remove() }
        }
    }
}
