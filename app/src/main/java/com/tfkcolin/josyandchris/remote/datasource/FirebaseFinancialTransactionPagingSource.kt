package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.FinancialTransaction
import kotlinx.coroutines.tasks.await

class FirebaseFinancialTransactionPagingSource(
    private val firestore: FirebaseFirestore
) : PagingSource<DocumentSnapshot, FinancialTransaction>() {

    companion object {
        private const val COLLECTION_TRANSACTIONS = "transactions"
    }

    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, FinancialTransaction> {
        return try {
            val query = firestore.collection(COLLECTION_TRANSACTIONS)
                .orderBy("created", Query.Direction.DESCENDING)
                .limit(params.loadSize.toLong())

            val snapshot = if (params.key != null) {
                query.startAfter(params.key!!).get().await()
            } else {
                query.get().await()
            }

            val transactions = snapshot.documents.mapNotNull { doc ->
                doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
            }

            val nextKey = if (snapshot.documents.isEmpty()) null else snapshot.documents.last()

            LoadResult.Page(
                data = transactions,
                prevKey = null, // Only paging forward
                nextKey = nextKey
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }

    override fun getRefreshKey(state: PagingState<DocumentSnapshot, FinancialTransaction>): DocumentSnapshot? {
        return null
    }
}
