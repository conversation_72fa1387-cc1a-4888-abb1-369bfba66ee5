package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.ImageData
import kotlinx.coroutines.tasks.await

class FirebaseImageDataPagingSource(
    private val firestore: FirebaseFirestore
) : PagingSource<DocumentSnapshot, ImageData>() {
    
    companion object {
        private const val COLLECTION_IMAGEDATA = "images_data"
    }
    
    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, ImageData> {
        return try {
            val query = firestore.collection(COLLECTION_IMAGEDATA)
                .orderBy("created", Query.Direction.DESCENDING)
                .limit(params.loadSize.toLong())
            
            val snapshot = if (params.key != null) {
                query.startAfter(params.key!!).get().await()
            } else {
                query.get().await()
            }
            
            val imageDataList = snapshot.documents.mapNotNull { doc ->
                doc.toObject<ImageData>()?.copy(id = doc.id)
            }
            
            val nextKey = if (snapshot.documents.isEmpty()) null else snapshot.documents.last()
            
            LoadResult.Page(
                data = imageDataList,
                prevKey = null, // Only paging forward
                nextKey = nextKey
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }
    
    override fun getRefreshKey(state: PagingState<DocumentSnapshot, ImageData>): DocumentSnapshot? {
        return null
    }
}
