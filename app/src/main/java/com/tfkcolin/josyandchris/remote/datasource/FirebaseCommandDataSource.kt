package com.tfkcolin.josyandchris.remote.datasource

import android.util.Log
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.query.CommandQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*
import com.tfkcolin.josyandchris.util.firebase.AggregateQueries
import com.tfkcolin.josyandchris.data.CommandStep
import kotlinx.coroutines.flow.flow
import com.google.firebase.firestore.ListenerRegistration
import timber.log.Timber

@Singleton
class FirebaseCommandDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {

    companion object {
        private const val COLLECTION_COMMANDS = "commands"
        private const val PAGE_SIZE = 20
    }

    // QueryBuilder instance for flexible query construction
    private val queryBuilder = CommandQueryBuilder(firestore)

    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        // No need to use toBuilder for settings
        val settings = firestore.firestoreSettings
        firestore.firestoreSettings = settings
    }

    private fun generateSearchIndex(command: Command): List<String> {
        val clientName = command.client?.name ?: ""
        val clientPhone = command.client?.tel ?: ""
        val clientCountry = command.client?.country ?: ""

        return SearchIndexUtils.generateSearchIndex(
            listOf(clientName, clientPhone, clientCountry)
        )
    }

    suspend fun createCommand(command: Command): Result<String> = safeCall("Create Command") {
        val docRef = firestore.collection(COLLECTION_COMMANDS).document()
        val commandWithId = command.copy(
            id = docRef.id,
            searchIndex = generateSearchIndex(command)
        )
        docRef.set(commandWithId).await()
        docRef.id
    }

    suspend fun getCommand(id: String): Result<Command> = safeCall("Get Command") {
        val snapshot = firestore.collection(COLLECTION_COMMANDS)
            .document(id)
            .get()
            .await()

        val command = snapshot.toObject<Command>()?.copy(id = snapshot.id)
            ?: throw Exception("Command not found")
        command
    }

    suspend fun updateCommand(command: Command): Result<Unit> = safeCall("Update Command") {
        val commandWithSearchIndex = command.copy(
            searchIndex = generateSearchIndex(command)
        )
        firestore.collection(COLLECTION_COMMANDS)
            .document(command.id)
            .set(commandWithSearchIndex)
            .await()
        Unit
    }

    suspend fun deleteCommand(id: String): Result<Unit> = safeCall("Delete Command") {
        firestore.collection(COLLECTION_COMMANDS)
            .document(id)
            .delete()
            .await()
        Unit
    }

    fun getCommandsFlow(): Flow<List<Command>> = callbackFlow {
        val filters = QueryFilters() // Default filters (no restrictions)
        val cursor = CursorConfig() // Default cursor with created DESC ordering
        val query = queryBuilder.build(filters, cursor)

        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }

            val commands = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<Command>()?.copy(id = doc.id)
            } ?: emptyList()

            trySend(commands)
        }

        awaitClose { listener.remove() }
    }

    fun getCommandsPagingData(): Flow<PagingData<Command>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = QueryFilters(),
                    cursorConfig = CursorConfig(limit = PAGE_SIZE)
                ) { doc ->
                    doc.toObject<Command>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    suspend fun getCommandsByClient(clientName: String): Result<List<Command>> = safeCall("Get Commands By Client") {
        val snapshot = firestore.collection(COLLECTION_COMMANDS)
            .whereEqualTo("client.name", clientName)
            .get()
            .await()

        val commands = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Command>()?.copy(id = doc.id)
        }
        commands
    }

    suspend fun getCommandsByStatus(statusIndex: Int): Result<List<Command>> = safeCall("Get Commands By Status") {
        val snapshot = firestore.collection(COLLECTION_COMMANDS)
            .whereEqualTo("commandStepIndex", statusIndex)
            .get()
            .await()

        val commands = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Command>()?.copy(id = doc.id)
        }
        commands
    }

    fun getCommandsByClientFlow(clientName: String): Flow<List<Command>> = callbackFlow {
        val filters = QueryFilters(
            additionalFilters = mapOf("client.name" to clientName)
        )
        val cursor = CursorConfig() // Default cursor with created DESC ordering
        val query = queryBuilder.build(filters, cursor)

        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }

            val commands = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<Command>()?.copy(id = doc.id)
            } ?: emptyList()

            trySend(commands)
        }

        awaitClose { listener.remove() }
    }

    fun getCommandsByStatusFlow(statusIndex: Int): Flow<List<Command>> = callbackFlow {
        val filters = QueryFilters(
            statusIndex = statusIndex
        )
        val cursor = CursorConfig() // Default cursor with created DESC ordering
        val query = queryBuilder.build(filters, cursor)

        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }

            val commands = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<Command>()?.copy(id = doc.id)
            } ?: emptyList()

            trySend(commands)
        }

        awaitClose { listener.remove() }
    }

    suspend fun searchCommands(query: String): Result<List<Command>> = safeCall("Search Commands") {
        val filters = QueryFilters(
            searchTerm = query
        )
        val cursor = CursorConfig()
        val firestoreQuery = queryBuilder.build(filters, cursor)
        val snapshot = firestoreQuery.get().await()

        val commands = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Command>()?.copy(id = doc.id)
        }
        commands
    }

    // Additional method to support multiple criteria search
    suspend fun getCommandsByMultipleCriteria(
        clientName: String? = null,
        statusIndex: Int? = null,
        searchTerm: String? = null,
        dateRange: DateRange? = null,
        limit: Int = PAGE_SIZE
    ): Result<List<Command>> = safeCall("Get Commands By Multiple Criteria") {
        val additionalFilters = mutableMapOf<String, Any>()
        clientName?.let { additionalFilters["client.name"] = it }

        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()

        val commands = snapshot.documents.mapNotNull { doc ->
            doc.toObject<Command>()?.copy(id = doc.id)
        }
        commands
    }

    // Implement this method to support the CommandRepository
    fun getCommandsFlowWithFilters(filters: QueryFilters): Flow<List<Command>> = callbackFlow {
        val query = queryBuilder.build(filters, CursorConfig())

        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }

            val commands = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<Command>()?.copy(id = doc.id)
            } ?: emptyList()

            trySend(commands)
        }

        awaitClose { listener.remove() }
    }

    /**
     * Flow-based count of commands by all statuses with real-time updates
     */
    fun getCommandsCountByAllStatusesFlow(): Flow<Map<Int, Long>> = callbackFlow {
        // Reset cache when starting a new flow
        cachedCommandCounts = null
        lastCountQueryTime = 0
        val statuses = CommandStep.entries.toTypedArray()
        val listeners = mutableListOf<ListenerRegistration>()
        val counts = mutableMapOf<Int, Long>()

        // Initialize all counts to 0
        statuses.forEach { status ->
            counts[status.ordinal] = 0L
        }

        // Set up listeners for each status
        statuses.forEach { status ->
            val query = firestore.collection(COLLECTION_COMMANDS)
                .whereEqualTo("commandStepIndex", status.ordinal)

            // Use regular query with snapshot listener and count documents
            val listener = query.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    // Continue with other listeners even if one fails
                    return@addSnapshotListener
                }

                snapshot?.let {
                    counts[status.ordinal] = it.size().toLong()
                    // Send updated counts map
                    trySend(counts.toMap())
                }
            }

            listeners.add(listener)
        }

        // Send initial state
        send(counts.toMap())

        awaitClose {
            // Clean up all listeners
            listeners.forEach { it.remove() }
        }
    }

    /**
     * Get commands with filters and pagination support
     */
    fun getCommandsPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<PagingData<Command>> {
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<Command>()?.copy(id = doc.id)
                }
            }
        ).flow
    }

    // ===== AGGREGATE COUNT METHODS =====

    /**
     * Get total count of all commands (uses aggregate query for efficiency)
     */
    suspend fun getCommandsCount(): Result<Long> = safeCall("Get Commands Count") {
        val query = firestore.collection(COLLECTION_COMMANDS)
        val count = AggregateQueries.getCount(query)
        count
    }

    /**
     * Get count of commands by status (uses aggregate query for efficiency)
     */
    suspend fun getCommandsCountByStatus(statusIndex: Int): Result<Long> = safeCall("Get Commands Count By Status") {
        val query = firestore.collection(COLLECTION_COMMANDS)
            .whereEqualTo("commandStepIndex", statusIndex)
        val count = AggregateQueries.getCount(query)
        count
    }

    /**
     * Get counts for all command statuses in one go
     * Returns a map of statusIndex to count
     */
    // Add caching to avoid frequent duplicate queries
    private var cachedCommandCounts: Map<Int, Long>? = null
    private var lastCountQueryTime: Long = 0
    private val COUNT_CACHE_DURATION = 30000 // 30 seconds cache
    
    suspend fun getCommandsCountByAllStatuses(): Result<Map<Int, Long>> = safeCall("Get Commands Count By All Statuses") {
        val currentTime = System.currentTimeMillis()
        
        // Return cached result if available and not expired
        if (cachedCommandCounts != null && (currentTime - lastCountQueryTime < COUNT_CACHE_DURATION)) {
            Timber.tag("FirebaseCommandDataSour").d("getCommandsCountByAllStatuses: Using cached data: $cachedCommandCounts")
            return@safeCall cachedCommandCounts!!
        }
        
        val counts = mutableMapOf<Int, Long>()

        // Create queries for each status
        val queries = CommandStep.entries.map { step ->
            step.ordinal to firestore.collection(COLLECTION_COMMANDS)
                .whereEqualTo("commandStepIndex", step.ordinal)
        }

        // Execute all count queries
        queries.forEach { (statusIndex, query) ->
            val count = AggregateQueries.getCount(query)
            counts[statusIndex] = count
        }
        
        // Update cache
        cachedCommandCounts = counts.toMap()
        lastCountQueryTime = currentTime
        
        Timber.tag("FirebaseCommandDataSour").i("getCommandsCountByAllStatuses: $counts")
        counts
    }

    /**
     * Get count of commands by client country
     */
    suspend fun getCommandsCountByCountry(country: String): Result<Long> = safeCall("Get Commands Count By Country") {
        val query = firestore.collection(COLLECTION_COMMANDS)
            .whereEqualTo("client.country", country)
        val count = AggregateQueries.getCount(query)
        count
    }
    
    /**
     * Get distinct client names for autocomplete suggestions using searchIndex
     * More efficient approach using existing search infrastructure
     */
    fun clientSuggestionsFlow(term: String = ""): Flow<List<String>> = flow {
        try {
            if (term.isEmpty()) {
                // Return recent commands' client names when no search term
                val snapshot = firestore.collection(COLLECTION_COMMANDS)
                    .orderBy("created", Query.Direction.DESCENDING)
                    .limit(50)
                    .get()
                    .await()
                
                val clientNames = snapshot.documents
                    .mapNotNull { doc -> doc.toObject<Command>()?.client?.name }
                    .filter { name -> name.isNotBlank() }
                    .distinct()
                    .take(10)
                
                emit(clientNames)
            } else {
                // Use search index for filtering - more efficient
                val searchTokens = SearchIndexUtils.generateSearchIndex(listOf(term.lowercase()))
                val queries = searchTokens.map { token ->
                    firestore.collection(COLLECTION_COMMANDS)
                        .whereArrayContains("searchIndex", token)
                        .limit(30)
                        .get()
                }
                
                val allResults = mutableSetOf<String>()
                queries.forEach { queryTask ->
                    try {
                        val snapshot = queryTask.await()
                        val names = snapshot.documents
                            .mapNotNull { doc -> doc.toObject<Command>()?.client?.name }
                            .filter { name -> 
                                name.isNotBlank() && name.lowercase().contains(term.lowercase())
                            }
                        allResults.addAll(names)
                    } catch (e: Exception) {
                        // Continue with other queries even if one fails
                    }
                }
                
                val filteredNames = allResults
                    .sorted()
                    .take(10)
                
                emit(filteredNames)
            }
        } catch (e: Exception) {
            Timber.e(e, "Error getting client suggestions")
            emit(emptyList())
        }
    }
}
