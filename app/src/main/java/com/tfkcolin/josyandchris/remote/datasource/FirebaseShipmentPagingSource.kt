package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.Shipment
import kotlinx.coroutines.tasks.await

class FirebaseShipmentPagingSource(
    private val firestore: FirebaseFirestore
) : PagingSource<DocumentSnapshot, Shipment>() {
    
    companion object {
        private const val COLLECTION_SHIPMENTS = "shipments"
    }
    
    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, Shipment> {
        return try {
            val query = firestore.collection(COLLECTION_SHIPMENTS)
                .orderBy("created", Query.Direction.DESCENDING)
                .limit(params.loadSize.toLong())
            
            val snapshot = if (params.key != null) {
                query.startAfter(params.key!!).get().await()
            } else {
                query.get().await()
            }
            
            val shipments = snapshot.documents.mapNotNull { doc ->
                doc.toObject<Shipment>()?.copy(id = doc.id)
            }
            
            val nextKey = if (snapshot.documents.isEmpty()) null else snapshot.documents.last()
            
            LoadResult.Page(
                data = shipments,
                prevKey = null, // Only paging forward
                nextKey = nextKey
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }
    
    override fun getRefreshKey(state: PagingState<DocumentSnapshot, Shipment>): DocumentSnapshot? {
        return null
    }
}
