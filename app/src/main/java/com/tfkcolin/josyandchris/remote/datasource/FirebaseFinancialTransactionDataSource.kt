package com.tfkcolin.josyandchris.remote.datasource

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.google.firebase.firestore.ListenerRegistration
import com.tfkcolin.josyandchris.util.firebase.AggregateQueries
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.query.FinancialTransactionQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.EnhancedFirebasePagingSource
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*

@Singleton
class FirebaseFinancialTransactionDataSource @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    
    companion object {
        private const val COLLECTION_TRANSACTIONS = "transactions"
        private const val PAGE_SIZE = 20
    }
    
    // QueryBuilder instance for flexible query construction
    private val queryBuilder = FinancialTransactionQueryBuilder(firestore)
    
    init {
        // Enable Firestore persistence/cache
        firestore.enableNetwork()
        // No need to use toBuilder anymore
        val settings = firestore.firestoreSettings
        firestore.firestoreSettings = settings
    }
    
suspend fun createTransaction(transaction: FinancialTransaction): Result<String> = safeCall("Create Transaction") {
        val docRef = firestore.collection(COLLECTION_TRANSACTIONS).document()
        val transactionWithId = transaction.copy(
            id = docRef.id
            // FinancialTransaction doesn't have searchIndex field, so we don't set it
        )
        docRef.set(transactionWithId).await()
        docRef.id
    }
    
suspend fun getTransaction(id: String): Result<FinancialTransaction> = safeCall("Get Transaction") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .document(id)
            .get()
            .await()
        
        val transaction = snapshot.toObject<FinancialTransaction>()?.copy(id = snapshot.id)
            ?: throw Exception("Transaction not found")
        transaction
    }
    
suspend fun updateTransaction(transaction: FinancialTransaction): Result<Unit> = safeCall("Update Transaction") {
        // FinancialTransaction doesn't have searchIndex field, so we don't set it
        firestore.collection(COLLECTION_TRANSACTIONS)
            .document(transaction.id)
            .set(transaction)
            .await()
        Unit
    }
    
suspend fun deleteTransaction(id: String): Result<Unit> = safeCall("Delete Transaction") {
        firestore.collection(COLLECTION_TRANSACTIONS)
            .document(id)
            .delete()
            .await()
        Unit
    }
    
    fun getTransactionsFlow(): Flow<List<FinancialTransaction>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
suspend fun getTransactionsByType(transactionTypeIndex: Int): Result<List<FinancialTransaction>> = safeCall("Get Transactions By Type") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("transactionTypeIndex", transactionTypeIndex)
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
suspend fun getTransactionsByCommand(commandId: String): Result<List<FinancialTransaction>> = safeCall("Get Transactions By Command") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("commandId", commandId)
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
suspend fun getTransactionsByCountry(country: String): Result<List<FinancialTransaction>> = safeCall("Get Transactions By Country") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("country", country)
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }

suspend fun getTransactionsPaged(limit: Int, startAfter: String?): Result<List<FinancialTransaction>> = safeCall("Get Transactions Paged") {
        val query = firestore.collection(COLLECTION_TRANSACTIONS)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .apply {
                startAfter?.let { orderBy("created").startAfter(it) }
            }
            .get()
            .await()

        val transactions = query.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
suspend fun getMarkedTransactions(marked: Boolean): Result<List<FinancialTransaction>> = safeCall("Get Marked Transactions") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("marked", marked)
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
    fun getTransactionsByTypeFlow(transactionTypeIndex: Int): Flow<List<FinancialTransaction>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("transactionTypeIndex", transactionTypeIndex)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getTransactionsByCommandFlow(commandId: String): Flow<List<FinancialTransaction>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("commandId", commandId)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getTransactionsByCountryFlow(country: String): Flow<List<FinancialTransaction>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("country", country)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun getMarkedTransactionsFlow(marked: Boolean): Flow<List<FinancialTransaction>> = callbackFlow {
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("marked", marked)
            .orderBy("created", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
    // ===== QUERYBUILDER-BASED METHODS =====
    
    /**
     * Get transactions with flexible filtering using QueryBuilder
     */
    suspend fun getTransactionsWithFilters(
        filters: QueryFilters,
        limit: Int = PAGE_SIZE
    ): Result<List<FinancialTransaction>> = safeCall("Get Transactions With Filters") {
        val cursor = CursorConfig(limit = limit)
        val query = queryBuilder.build(filters, cursor)
        val snapshot = query.get().await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
    /**
     * Get transactions with filters and pagination support
     */
    fun getTransactionsPagingDataWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<PagingData<FinancialTransaction>> {
        return Pager(
            config = PagingConfig(
                pageSize = cursorConfig.limit,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                EnhancedFirebasePagingSource(
                    queryBuilder = queryBuilder,
                    filters = filters,
                    cursorConfig = cursorConfig
                ) { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                }
            }
        ).flow
    }
    
    /**
     * Get transactions flow with flexible filtering
     */
    fun getTransactionsFlowWithFilters(
        filters: QueryFilters,
        cursorConfig: CursorConfig = CursorConfig()
    ): Flow<List<FinancialTransaction>> = callbackFlow {
        val query = queryBuilder.build(filters, cursorConfig)
        val listener = query.addSnapshotListener { snapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }
            
            val transactions = snapshot?.documents?.mapNotNull { doc ->
                doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
            } ?: emptyList()
            
            trySend(transactions)
        }
        
        awaitClose { listener.remove() }
    }
    
    /**
     * Get transactions with multiple criteria
     */
    suspend fun getTransactionsByMultipleCriteria(
        transactionTypeIndex: Int? = null,
        country: String? = null,
        commandId: String? = null,
        marked: Boolean? = null,
        priceRange: Pair<Int, Int>? = null,
        dateRange: DateRange? = null,
        limit: Int = PAGE_SIZE
    ): Result<List<FinancialTransaction>> = safeCall("Get Transactions By Multiple Criteria") {
        val additionalFilters = mutableMapOf<String, Any>()
        country?.let { additionalFilters["country"] = it }
        commandId?.let { additionalFilters["commandId"] = it }
        marked?.let { additionalFilters["marked"] = it }
        
        val filters = QueryFilters(
            statusIndex = transactionTypeIndex,
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
        
        // Special handling for price range if needed
        val cursor = CursorConfig(limit = limit)
        val query = if (priceRange != null) {
            queryBuilder.buildPriceRangeFilter(
                minPrice = priceRange.first,
                maxPrice = priceRange.second,
                cursor = cursor
            )
        } else {
            queryBuilder.build(filters, cursor)
        }
        
        val snapshot = query.get().await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
    // Add methods for searching
suspend fun searchTransactions(query: String): Result<List<FinancialTransaction>> = safeCall("Search Transactions") {
        // Use the label field for searching since there's no searchIndex
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereGreaterThanOrEqualTo("label", query)
            .whereLessThanOrEqualTo("label", query + "\uf8ff")
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
    fun searchTransactionsFlow(query: String): Flow<List<FinancialTransaction>> = callbackFlow {
        // Use the label field for searching since there's no searchIndex
        val listener = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereGreaterThanOrEqualTo("label", query)
            .whereLessThanOrEqualTo("label", query + "\uf8ff")
            .orderBy("label")
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val transactions = snapshot?.documents?.mapNotNull { doc ->
                    doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
                } ?: emptyList()
                
                trySend(transactions)
            }
        
        awaitClose { listener.remove() }
    }
    
    // Method needed by repository
    fun getTransactionsPagingData(): Flow<PagingData<FinancialTransaction>> {
        return getTransactionsPagingDataWithFilters(QueryFilters())
    }
    
    /**
     * Get transactions by country and type - used for aggregate calculations
     */
    suspend fun getTransactionsByCountryAndType(
        country: String,
        transactionTypeIndex: Int
    ): Result<List<FinancialTransaction>> = safeCall("Get Transactions By Country And Type") {
        val snapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("country", country)
            .whereEqualTo("transactionTypeIndex", transactionTypeIndex)
            .get()
            .await()
        
        val transactions = snapshot.documents.mapNotNull { doc ->
            doc.toObject<FinancialTransaction>()?.copy(id = doc.id)
        }
        transactions
    }
    
    /**
     * Flow-based totals for a specific country with real-time updates
     * Returns Pair<input_total, output_total>
     */
    fun getTransactionsTotalsByCountryFlow(country: String): Flow<Pair<Double, Double>> = callbackFlow {
        // Set up listeners for both transaction types
        val inputQuery = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("country", country)
            .whereEqualTo("transactionTypeIndex", TransactionType.INPUT.ordinal)
        
        val outputQuery = firestore.collection(COLLECTION_TRANSACTIONS)
            .whereEqualTo("country", country)
            .whereEqualTo("transactionTypeIndex", TransactionType.OUTPUT.ordinal)
        
        var inputTotal = 0.0
        var outputTotal = 0.0
        
        val inputListener = inputQuery.addSnapshotListener { snapshot, error ->
            if (error != null) {
                return@addSnapshotListener
            }
            
            inputTotal = snapshot?.documents?.sumOf { doc ->
                doc.toObject<FinancialTransaction>()?.price?.toDouble() ?: 0.0
            } ?: 0.0
            
            trySend(Pair(inputTotal, outputTotal))
        }
        
        val outputListener = outputQuery.addSnapshotListener { snapshot, error ->
            if (error != null) {
                return@addSnapshotListener
            }
            
            outputTotal = snapshot?.documents?.sumOf { doc ->
                doc.toObject<FinancialTransaction>()?.price?.toDouble() ?: 0.0
            } ?: 0.0
            
            trySend(Pair(inputTotal, outputTotal))
        }
        
        // Send initial state
        send(Pair(inputTotal, outputTotal))
        
        awaitClose {
            inputListener.remove()
            outputListener.remove()
        }
    }
    
    /**
     * Flow-based totals for all countries with real-time updates
     * Returns Map<country, Pair<input_total, output_total>>
     */
    fun getTransactionsTotalsByAllCountriesFlow(): Flow<Map<String, Pair<Double, Double>>> = callbackFlow {
        val listeners = mutableListOf<ListenerRegistration>()
        val totals = mutableMapOf<String, Pair<Double, Double>>()
        
        // First, get all unique countries
        val countriesSnapshot = firestore.collection(COLLECTION_TRANSACTIONS)
            .get()
            .await()
        
        val countries = countriesSnapshot.documents
            .mapNotNull { it.toObject<FinancialTransaction>()?.country }
            .distinct()
        
        // Initialize totals
        countries.forEach { country ->
            totals[country] = Pair(0.0, 0.0)
        }
        
        // Set up listeners for each country and transaction type
        countries.forEach { country ->
            TransactionType.values().forEach { type ->
                val query = firestore.collection(COLLECTION_TRANSACTIONS)
                    .whereEqualTo("country", country)
                    .whereEqualTo("transactionTypeIndex", type.ordinal)
                
                val listener = query.addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        return@addSnapshotListener
                    }
                    
                    val total = snapshot?.documents?.sumOf { doc ->
                        doc.toObject<FinancialTransaction>()?.price?.toDouble() ?: 0.0
                    } ?: 0.0
                    
                    val current = totals[country] ?: Pair(0.0, 0.0)
                    totals[country] = when (type) {
                        TransactionType.INPUT -> Pair(total, current.second)
                        TransactionType.OUTPUT -> Pair(current.first, total)
                    }
                    
                    trySend(totals.toMap())
                }
                
                listeners.add(listener)
            }
        }
        
        // Send initial state
        send(totals.toMap())
        
        awaitClose {
            listeners.forEach { it.remove() }
        }
    }
}
