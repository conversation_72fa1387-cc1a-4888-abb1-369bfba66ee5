package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.toObject
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.remote.datasource.FirebaseImageDataDataSource
import com.tfkcolin.josyandchris.util.SearchIndexUtils
import com.tfkcolin.josyandchris.util.safeCall
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.ImageQueryBuilder
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of the ImageDataRepository.
 * Delegates to FirebaseImageDataDataSource for data operations and uses ImageQueryBuilder
 * for constructing Firebase queries.
 */
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.SetOptions


@Singleton
class FirebaseImageDataRepositoryImpl @Inject constructor(
    private val dataSource: FirebaseImageDataDataSource,
    private val firestore: FirebaseFirestore
) : ImageDataRepository {

    companion object {

        private const val COLLECTION_IMAGES = "images_data"
        private const val COLLECTION_METADATA = "metadata"
        private const val DOCUMENT_CATEGORIES = "categories"
        private const val DOCUMENT_GENRES = "genres"
        private const val FIELD_NAMES = "names"
    }

    // Create query builder instance for building queries
    private val queryBuilder = ImageQueryBuilder(firestore)

    // Basic CRUD operations
    override suspend fun createImageData(imageData: ImageData): Result<String> {
        // Generate search index if needed
        val imageWithSearchIndex = if (!imageData.name.isNullOrBlank()) {
            val searchIndex = SearchIndexUtils.generateSearchIndex(
                listOfNotNull(
                    imageData.name,
                    imageData.category,
                    imageData.genre
                )
            ).joinToString(" ")
            imageData.copy(searchIndex = searchIndex)
        } else {
            imageData
        }

        val result = dataSource.createImage(imageWithSearchIndex)

        // Update distinct categories and genres after successful image creation
        if (result.isSuccess) {
            val category = imageData.category.trim().lowercase()
            val genre = imageData.genre.trim().lowercase()

            if (category.isNotBlank()) {
                updateDistinctValue(DOCUMENT_CATEGORIES, category)
            }
            if (genre.isNotBlank()) {
                updateDistinctValue(DOCUMENT_GENRES, genre)
            }
        }
        return result
    }

    private suspend fun updateDistinctValue(documentId: String, value: String) {
        safeCall("Update Distinct Value") {
            firestore.runTransaction { transaction ->
                val docRef = firestore.collection(COLLECTION_METADATA).document(documentId)
                val snapshot = transaction.get(docRef)

                val currentNames = snapshot.get(FIELD_NAMES) as? List<String> ?: emptyList()
                if (!currentNames.contains(value)) {
                    transaction.set(
                        docRef,
                        mapOf(FIELD_NAMES to FieldValue.arrayUnion(value)),
                        SetOptions.merge()
                    )
                }
            }.await()
        }
    }

    override suspend fun getImageData(id: String): Result<ImageData> {
        return dataSource.getImage(id)
    }

    override suspend fun updateImageData(imageData: ImageData): Result<Unit> {
        return dataSource.updateImage(imageData)
    }

    override suspend fun updateImageDataFields(id: String, fields: Map<String, Any>): Result<Unit> {
        return dataSource.updateImageFields(id, fields)
    }

    override suspend fun deleteImageData(id: String): Result<Unit> {
        return dataSource.deleteImage(id)
    }

    // Streaming operations
    override fun getImageDataFlow(): Flow<List<ImageData>> {
        return dataSource.getImagesFlow()
    }

    override fun getImageDataPagingData(): Flow<PagingData<ImageData>> {
        return getImageDataPagingDataWithFilters(QueryFilters())
    }

    // Filtered paging operations with QueryFilters
    override fun getImageDataPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<ImageData>> {
        return dataSource.getImagesPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig()
        )
    }

    // Filtered paging operations
    override fun getImageDataPagingDataWithFilters(
        category: String?,
        genre: String?,
        uploaded: Boolean?,
        searchTerm: String?
    ): Flow<PagingData<ImageData>> {
        // Build additional filters map
        val additionalFilters = mutableMapOf<String, Any>()
        category?.let { additionalFilters["category"] = it }
        genre?.let { additionalFilters["genre"] = it }
        uploaded?.let { additionalFilters["upload"] = it }

        // Create QueryFilters with server-side filtering
        val filters = QueryFilters(
            searchTerm = searchTerm,
            additionalFilters = additionalFilters
        )

        // Use the enhanced paging data method with filters
        return dataSource.getImagesPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }

    // Distinct value operations
    override suspend fun getDistinctCategories(): Result<List<String>> = safeCall("Get Distinct Categories") {
        val snapshot = firestore.collection(COLLECTION_METADATA).document(DOCUMENT_CATEGORIES).get().await()
        (snapshot.get(FIELD_NAMES) as? List<String>) ?: emptyList()
    }

    override suspend fun getDistinctGenres(): Result<List<String>> = safeCall("Get Distinct Genres") {
        val snapshot = firestore.collection(COLLECTION_METADATA).document(DOCUMENT_GENRES).get().await()
        (snapshot.get(FIELD_NAMES) as? List<String>) ?: emptyList()
    }

    // Query operations
    override suspend fun getImageDataByCategory(category: String): Result<List<ImageData>> =
        safeCall("Get image data by category") {
            val filters = QueryFilters(
                additionalFilters = mapOf("category" to category)
            )
            val query = queryBuilder.build(filters, CursorConfig())

            val snapshot = query.get().await()
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(ImageData::class.java)?.copy(id = doc.id)
            }
        }

    override suspend fun getImageDataByGenre(genre: String): Result<List<ImageData>> =
        safeCall("Get image data by genre") {
            val filters = QueryFilters(
                additionalFilters = mapOf("genre" to genre)
            )
            val query = queryBuilder.build(filters, CursorConfig())

            val snapshot = query.get().await()
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(ImageData::class.java)?.copy(id = doc.id)
            }
        }

    override suspend fun getUploadedImageData(): Result<List<ImageData>> =
        safeCall("Get uploaded image data") {
            val filters = QueryFilters(
                additionalFilters = mapOf("upload" to true)
            )
            val query = queryBuilder.build(filters, CursorConfig())

            val snapshot = query.get().await()
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(ImageData::class.java)?.copy(id = doc.id)
            }
        }

    override suspend fun getPendingImageData(): Result<List<ImageData>> =
        safeCall("Get pending image data") {
            val filters = QueryFilters(
                additionalFilters = mapOf("upload" to false)
            )
            val query = queryBuilder.build(filters, CursorConfig())

            val snapshot = query.get().await()
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(ImageData::class.java)?.copy(id = doc.id)
            }
        }

    // Flow query operations
    override fun getImageDataByCategoryFlow(category: String): Flow<List<ImageData>> {
        // If dataSource has type-based methods, map category to typeIndex
        // Otherwise, create a flow that filters by category
        return dataSource.getImagesFlow()
            .map { imageList ->
                imageList.filter { it.category == category }
            }
    }

    override fun getImageDataByGenreFlow(genre: String): Flow<List<ImageData>> {
        return dataSource.getImagesFlow()
            .map { imageList ->
                imageList.filter { it.genre == genre }
            }
    }

    override fun getUploadedImageDataFlow(): Flow<List<ImageData>> {
        return dataSource.getImagesFlow()
            .map { imageList ->
                imageList.filter { it.upload }
            }
    }

    override fun getPendingImageDataFlow(): Flow<List<ImageData>> {
        return dataSource.getImagesFlow()
            .map { imageList ->
                imageList.filter { !it.upload }
            }
    }

    // Search operations
    override suspend fun searchImageData(query: String): Result<List<ImageData>> =
        safeCall("Search image data") {
            // Generate search tokens from the query
            val searchTokens = SearchIndexUtils.generateQueryTokens(query)

            if (searchTokens.isEmpty()) {
                emptyList()
            } else {
                // Build a query using the search tokens
                val filters = QueryFilters(searchTerm = query)
                val queryObj = queryBuilder.build(filters, CursorConfig())

                val snapshot = queryObj.get().await()
                snapshot.documents.mapNotNull { doc ->
                    doc.toObject(ImageData::class.java)?.copy(id = doc.id)
                }
            }
        }

    override fun searchImageDataFlow(query: String): Flow<List<ImageData>> {
        // If query is blank, return all images
        if (query.isBlank()) {
            return dataSource.getImagesFlow()
        }

        // Otherwise, filter the flow with a client-side search
        val normalizedQuery = query.trim().lowercase()
        return dataSource.getImagesFlow()
            .map { imageList ->
                imageList.filter { image ->
                    image.name?.lowercase()?.contains(normalizedQuery) == true ||
                            image.category.lowercase().contains(normalizedQuery) ||
                            image.genre.lowercase().contains(normalizedQuery)
                }
            }
    }
}
