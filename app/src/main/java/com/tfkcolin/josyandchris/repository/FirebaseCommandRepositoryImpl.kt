package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.remote.datasource.FirebaseCommandDataSource
import com.tfkcolin.josyandchris.ui.data.CommandSortOrder
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseCommandRepositoryImpl @Inject constructor(
    private val commandDataSource: FirebaseCommandDataSource
) : CommandRepository {

    override suspend fun createCommand(command: Command): Result<String> {
        return commandDataSource.createCommand(command)
    }

    override suspend fun getCommand(id: String): Result<Command> {
        return commandDataSource.getCommand(id)
    }

    override suspend fun updateCommand(command: Command): Result<Unit> {
        return commandDataSource.updateCommand(command)
    }

    override suspend fun deleteCommand(id: String): Result<Unit> {
        return commandDataSource.deleteCommand(id)
    }

    override fun getCommandsFlow(): Flow<List<Command>> {
        return commandDataSource.getCommandsFlow()
    }

    override fun getCommandsPagingData(): Flow<PagingData<Command>> {
        return commandDataSource.getCommandsPagingData()
    }

override fun getCommandsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Command>> {
        // Create cursor configuration with ordering
        val cursorConfig = CursorConfig(
            orderBy = when (filters.additionalFilters["sortOrder"] as? CommandSortOrder) {
                CommandSortOrder.CREATED_ASC -> "created"
                CommandSortOrder.CLIENT_NAME_ASC -> "client.name"
                CommandSortOrder.STATUS_ASC -> "commandStepIndex"
                CommandSortOrder.CLIENT_NAME_DESC -> "client.name"
                CommandSortOrder.STATUS_DESC -> "commandStepIndex"
                else -> "created"
            },
            orderDirection = when (filters.additionalFilters["sortOrder"] as? CommandSortOrder) {
                CommandSortOrder.CREATED_ASC, CommandSortOrder.CLIENT_NAME_ASC, CommandSortOrder.STATUS_ASC -> Query.Direction.ASCENDING
                else -> Query.Direction.DESCENDING
            }
        )

        // Remove sortOrder field if used purely for sorting and not a filter
        val filtersForQuery = filters.copy(
            additionalFilters = filters.additionalFilters - "sortOrder"
        )

        return commandDataSource.getCommandsPagingDataWithFilters(filtersForQuery, cursorConfig)
    }

    override fun getCommandsPagingDataWithFiltersAndSorting(
        clientName: String?,
        phone: String?,
        country: String?,
        city: String?,
        statusIndex: Int?,
        searchTerm: String?,
        dateRange: com.tfkcolin.josyandchris.util.query.DateRange?,
        proofUploaded: Boolean?,
        sortOrder: CommandSortOrder
    ): Flow<PagingData<Command>> {
        val additionalFilters = mutableMapOf<String, Any>()
        
        // Apply all filters with combined AND logic using correct Firestore field structure
        clientName?.let { if (it.isNotBlank()) additionalFilters["client.name"] = it }
        phone?.let { if (it.isNotBlank()) additionalFilters["client.tel"] = it }
        country?.let { if (it.isNotBlank()) additionalFilters["client.country"] = it }
        city?.let { if (it.isNotBlank()) additionalFilters["client.city"] = it }
        proofUploaded?.let { additionalFilters["proofUploaded"] = it }
        
        // Create filters for query
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm?.ifBlank { null },
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
        
        // Create cursor configuration with ordering based on sort order
        val cursorConfig = CursorConfig(
            orderBy = when (sortOrder) {
                CommandSortOrder.CREATED_ASC -> "created"
                CommandSortOrder.CLIENT_NAME_ASC -> "client.name"
                CommandSortOrder.STATUS_ASC -> "commandStepIndex"
                CommandSortOrder.CLIENT_NAME_DESC -> "client.name"
                CommandSortOrder.STATUS_DESC -> "commandStepIndex"
                else -> "created"
            },
            orderDirection = when (sortOrder) {
                CommandSortOrder.CREATED_ASC, CommandSortOrder.CLIENT_NAME_ASC, CommandSortOrder.STATUS_ASC -> Query.Direction.ASCENDING
                else -> Query.Direction.DESCENDING
            }
        )
        
        return commandDataSource.getCommandsPagingDataWithFilters(filters, cursorConfig)
    }

    override suspend fun getCommandsByClient(clientName: String): Result<List<Command>> {
        return commandDataSource.getCommandsByMultipleCriteria(
            clientName = clientName,
            limit = 100
        )
    }

    override suspend fun getCommandsByStatus(statusIndex: Int): Result<List<Command>> {
        return commandDataSource.getCommandsByMultipleCriteria(
            statusIndex = statusIndex,
            limit = 100
        )
    }

    override fun getCommandsByClientFlow(clientName: String): Flow<List<Command>> {
        val filters = QueryFilters(
            additionalFilters = mapOf("client.name" to clientName)
        )
        return commandDataSource.getCommandsFlowWithFilters(filters)
    }

    override fun getCommandsByStatusFlow(statusIndex: Int): Flow<List<Command>> {
        val filters = QueryFilters(statusIndex = statusIndex)
        return commandDataSource.getCommandsFlowWithFilters(filters)
    }

    override suspend fun searchCommands(query: String): Result<List<Command>> {
        return commandDataSource.getCommandsByMultipleCriteria(
            searchTerm = query,
            limit = 100
        )
    }

    override fun searchCommandsFlow(query: String): Flow<List<Command>> {
        val filters = QueryFilters(searchTerm = query)
        return commandDataSource.getCommandsFlowWithFilters(filters)
    }

    // Additional methods for better query support
    fun getCommandsPagingDataWithSearch(searchTerm: String): Flow<PagingData<Command>> {
        val filters = QueryFilters(searchTerm = searchTerm)
        return commandDataSource.getCommandsPagingDataWithFilters(filters)
    }

    fun getCommandsPagingDataWithStatus(statusIndex: Int): Flow<PagingData<Command>> {
        val filters = QueryFilters(statusIndex = statusIndex)
        return commandDataSource.getCommandsPagingDataWithFilters(filters)
    }

    fun getCommandsPagingDataWithFilters(
        statusIndex: Int? = null,
        searchTerm: String? = null,
        clientName: String? = null
    ): Flow<PagingData<Command>> {
        val additionalFilters = mutableMapOf<String, Any>()
        clientName?.let { additionalFilters["client.name"] = it }
        
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            additionalFilters = additionalFilters
        )
        return commandDataSource.getCommandsPagingDataWithFilters(filters)
    }
    
    // Aggregate count operations
    override suspend fun getCommandsCount(): Result<Long> {
        return commandDataSource.getCommandsCount()
    }
    
    override suspend fun getCommandsCountByStatus(statusIndex: Int): Result<Long> {
        return commandDataSource.getCommandsCountByStatus(statusIndex)
    }
    
    override suspend fun getCommandsCountByAllStatuses(): Result<Map<Int, Long>> {
        return commandDataSource.getCommandsCountByAllStatuses()
    }
    
    override suspend fun getCommandsCountByCountry(country: String): Result<Long> {
        return commandDataSource.getCommandsCountByCountry(country)
    }
    
    override fun clientSuggestionsFlow(term: String): Flow<List<String>> {
        return commandDataSource.clientSuggestionsFlow(term)
    }
    
    override fun getCommandsCountByAllStatusesFlow(): Flow<Map<Int, Long>> {
        return commandDataSource.getCommandsCountByAllStatusesFlow()
    }
}
