package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow

interface CommandRepository {
    
    // Basic CRUD operations
    suspend fun createCommand(command: Command): Result<String>
    suspend fun getCommand(id: String): Result<Command>
    suspend fun updateCommand(command: Command): Result<Unit>
    suspend fun deleteCommand(id: String): Result<Unit>
    
    // Streaming operations
    fun getCommandsFlow(): Flow<List<Command>>
    fun getCommandsPagingData(): Flow<PagingData<Command>>
    
    // Filtered paging operations
    fun getCommandsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Command>>
    
    // Extended filtering with multi-criteria AND logic and sorting support  
    fun getCommandsPagingDataWithFiltersAndSorting(
        clientName: String? = null,
        phone: String? = null,
        country: String? = null,
        city: String? = null,
        statusIndex: Int? = null,
        searchTerm: String? = null,
        dateRange: com.tfkcolin.josyandchris.util.query.DateRange? = null,
        proofUploaded: Boolean? = null,
        sortOrder: com.tfkcolin.josyandchris.ui.data.CommandSortOrder = com.tfkcolin.josyandchris.ui.data.CommandSortOrder.CREATED_DESC
    ): Flow<PagingData<Command>>
    
    // Query operations
    suspend fun getCommandsByClient(clientName: String): Result<List<Command>>
    suspend fun getCommandsByStatus(statusIndex: Int): Result<List<Command>>
    fun getCommandsByClientFlow(clientName: String): Flow<List<Command>>
    fun getCommandsByStatusFlow(statusIndex: Int): Flow<List<Command>>
    
    // Search operations
    suspend fun searchCommands(query: String): Result<List<Command>>
    fun searchCommandsFlow(query: String): Flow<List<Command>>
    
    // Aggregate count operations (efficient for statistics)
    suspend fun getCommandsCount(): Result<Long>
    suspend fun getCommandsCountByStatus(statusIndex: Int): Result<Long>
    suspend fun getCommandsCountByAllStatuses(): Result<Map<Int, Long>>
    suspend fun getCommandsCountByCountry(country: String): Result<Long>
    
    // Client suggestions for autocomplete
    fun clientSuggestionsFlow(term: String = ""): Flow<List<String>>
    
    // Flow-based aggregate operations for real-time updates
    fun getCommandsCountByAllStatusesFlow(): Flow<Map<Int, Long>>
}
