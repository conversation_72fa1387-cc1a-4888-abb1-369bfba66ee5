package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow

interface FinancialTransactionRepository {
    
// Basic CRUD operations
    suspend fun createTransaction(transaction: FinancialTransaction): Result<String>
    suspend fun getTransaction(id: String): Result<FinancialTransaction>
    suspend fun updateTransaction(transaction: FinancialTransaction): Result<Unit>
    suspend fun deleteTransaction(id: String): Result<Unit>

    // Paginated loading
    suspend fun loadTransactionsPaged(limit: Int = 20, startAfter: String? = null): Result<List<FinancialTransaction>>
    
    // Streaming operations
    fun getTransactionsFlow(): Flow<List<FinancialTransaction>>
    fun getTransactionsPagingData(): Flow<PagingData<FinancialTransaction>>
    
    // Server-side filtering with pagination
    fun getTransactionsPagingDataWithFilters(
        transactionTypeIndex: Int? = null,
        country: String? = null,
        commandId: String? = null,
        marked: Boolean? = null
    ): Flow<PagingData<FinancialTransaction>>
    
    // Filtered paging operations with QueryFilters
    fun getTransactionsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<FinancialTransaction>>
    
    // Query operations
    suspend fun getTransactionsByCommand(commandId: String): Result<List<FinancialTransaction>>
    suspend fun getTransactionsByCountry(country: String): Result<List<FinancialTransaction>>
    suspend fun getTransactionsByType(transactionTypeIndex: Int): Result<List<FinancialTransaction>>
    suspend fun getMarkedTransactions(): Result<List<FinancialTransaction>>
    
    fun getTransactionsByCommandFlow(commandId: String): Flow<List<FinancialTransaction>>
    fun getTransactionsByCountryFlow(country: String): Flow<List<FinancialTransaction>>
    fun getTransactionsByTypeFlow(transactionTypeIndex: Int): Flow<List<FinancialTransaction>>
    fun getMarkedTransactionsFlow(): Flow<List<FinancialTransaction>>
    
    // Search operations
    suspend fun searchTransactions(query: String): Result<List<FinancialTransaction>>
    fun searchTransactionsFlow(query: String): Flow<List<FinancialTransaction>>
    
    // Aggregate operations (efficient for statistics)
    suspend fun getTransactionsTotalByCountryAndType(
        country: String,
        transactionTypeIndex: Int
    ): Result<Double>
    
    // Flow-based aggregate operations for real-time updates
    fun getTransactionsTotalsByCountryFlow(country: String): Flow<Pair<Double, Double>> // input, output
    fun getTransactionsTotalsByAllCountriesFlow(): Flow<Map<String, Pair<Double, Double>>> // country -> (input, output)
}
