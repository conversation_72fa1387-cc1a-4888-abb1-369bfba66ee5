package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.remote.datasource.FirebaseCountryDataSource
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseCountryRepositoryImpl @Inject constructor(
    private val firebaseCountryDataSource: FirebaseCountryDataSource
) : CountryRepository {
    
    // Basic CRUD operations
    override suspend fun createCountry(country: CountryData): Result<String> {
        return firebaseCountryDataSource.createCountry(country)
    }
    
    override suspend fun getCountry(id: String): Result<CountryData> {
        return firebaseCountryDataSource.getCountry(id)
    }
    
    override suspend fun updateCountry(country: CountryData): Result<Unit> {
        return firebaseCountryDataSource.updateCountry(country)
    }
    
    override suspend fun deleteCountry(id: String): Result<Unit> {
        return firebaseCountryDataSource.deleteCountry(id)
    }

    override suspend fun loadCountries(limit: Int, startAfter: String?): Result<List<CountryData>> {
        return firebaseCountryDataSource.loadCountries(limit, startAfter)
    }

    // Streaming operations
    override fun getCountriesFlow(): Flow<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesFlow()
    }
    
    override fun getCountriesPagingData(): Flow<PagingData<CountryData>> {
        return firebaseCountryDataSource.getCountriesPagingData()
    }
    
    // Server-side filtering with pagination
    override fun getCountriesPagingDataWithFilters(
        name: String?,
        devise: String?,
        searchTokens: List<String>?
    ): Flow<PagingData<CountryData>> {
        // Build additional filters map
        val additionalFilters = mutableMapOf<String, Any>()
        name?.let { additionalFilters["name"] = it }
        devise?.let { additionalFilters["devise"] = it }
        
        // Create QueryFilters with server-side filtering
        val filters = QueryFilters(
            searchTokens = searchTokens,
            additionalFilters = additionalFilters
        )
        
        // Use the enhanced paging data method with filters
        return firebaseCountryDataSource.getCountriesPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "name",
                orderDirection = com.google.firebase.firestore.Query.Direction.ASCENDING
            )
        )
    }
    
    // Filtered paging operations with QueryFilters
    override fun getCountriesPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<CountryData>> {
        return firebaseCountryDataSource.getCountriesPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "name",
                orderDirection = com.google.firebase.firestore.Query.Direction.ASCENDING
            )
        )
    }
    
    // Query operations
    override suspend fun getCountriesByName(name: String): Result<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesByName(name)
    }
    
    override suspend fun getCountriesByDevise(devise: String): Result<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesByDevise(devise)
    }
    
    override suspend fun getCountriesBySearchTokens(tokens: List<String>): Result<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesBySearchTokens(tokens)
    }
    
    override fun getCountriesByNameFlow(name: String): Flow<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesByNameFlow(name)
    }
    
    override fun getCountriesByDeviseFlow(devise: String): Flow<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesByDeviseFlow(devise)
    }
    
    // Search operations
    override suspend fun searchCountries(query: String): Result<List<CountryData>> {
        return firebaseCountryDataSource.searchCountries(query)
    }
    
    override fun searchCountriesFlow(query: String): Flow<List<CountryData>> {
        return firebaseCountryDataSource.searchCountriesFlow(query)
    }
    
    // Alphabetical ordering
    override suspend fun getCountriesAlphabetically(): Result<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesAlphabetically()
    }
    
    override fun getCountriesAlphabeticallyFlow(): Flow<List<CountryData>> {
        return firebaseCountryDataSource.getCountriesAlphabeticallyFlow()
    }
}
