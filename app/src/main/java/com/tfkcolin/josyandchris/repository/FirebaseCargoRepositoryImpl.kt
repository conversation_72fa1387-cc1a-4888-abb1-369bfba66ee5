package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.domain.util.ErrorMapper
import com.tfkcolin.josyandchris.domain.util.ErrorMapper.mapError
import com.tfkcolin.josyandchris.remote.datasource.FirebaseCargoDataSource
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseCargoRepositoryImpl @Inject constructor(
    private val firebaseCargoDataSource: FirebaseCargoDataSource
) : CargoRepository {
    
    // Basic CRUD operations
    override suspend fun createCargo(cargo: Cargo): Result<String> {
        Timber.d("Creating cargo with origin: ${cargo.origin}, destination: ${cargo.destination}")
        
        return try {
            firebaseCargoDataSource.createCargo(cargo)
                .mapError("Create cargo")
                .onSuccess { 
                    Timber.i("Successfully created cargo with id: $it")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to create cargo")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Create cargo")
            Result.failure(domainException)
        }
    }
    
    override suspend fun getCargo(id: String): Result<Cargo> {
        Timber.d("Fetching cargo with id: $id")
        
        return try {
            firebaseCargoDataSource.getCargo(id)
                .mapError("Get cargo")
                .onSuccess { 
                    Timber.d("Successfully fetched cargo: ${it.id}")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to fetch cargo with id: $id")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Get cargo $id")
            Result.failure(domainException)
        }
    }
    
    override suspend fun updateCargo(cargo: Cargo): Result<Unit> {
        Timber.d("Updating cargo: ${cargo.id}")
        
        return try {
            firebaseCargoDataSource.updateCargo(cargo)
                .mapError("Update cargo")
                .onSuccess { 
                    Timber.i("Successfully updated cargo: ${cargo.id}")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to update cargo: ${cargo.id}")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Update cargo ${cargo.id}")
            Result.failure(domainException)
        }
    }
    
    override suspend fun deleteCargo(id: String): Result<Unit> {
        Timber.d("Deleting cargo with id: $id")
        
        return try {
            firebaseCargoDataSource.deleteCargo(id)
                .mapError("Delete cargo")
                .onSuccess { 
                    Timber.i("Successfully deleted cargo: $id")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to delete cargo: $id")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Delete cargo $id")
            Result.failure(domainException)
        }
    }
    
    // Streaming operations
    override fun getCargosFlow(): Flow<List<Cargo>> {
        Timber.d("Getting cargos flow")
        return firebaseCargoDataSource.getCargosFlow()
    }
    
    override fun getCargosPagingData(): Flow<PagingData<Cargo>> {
        Timber.d("Getting cargos paging data")
        return firebaseCargoDataSource.getCargosPagingData()
    }
    
    // Filtered paging operations with server-side filtering
    override fun getCargosPagingDataWithFilters(
        statusIndex: Int?,
        origin: String?,
        destination: String?
    ): Flow<PagingData<Cargo>> {
        // Build additional filters map
        val additionalFilters = mutableMapOf<String, Any>()
        origin?.let { additionalFilters["origin"] = it }
        destination?.let { additionalFilters["destination"] = it }
        
        // Create QueryFilters with server-side filtering
        val filters = QueryFilters(
            statusIndex = statusIndex,
            additionalFilters = additionalFilters
        )
        
        // Use the enhanced paging data method with filters
        return firebaseCargoDataSource.getCargosPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Implement the missing method
    override fun getCargosPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Cargo>> {
        return firebaseCargoDataSource.getCargosPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Query operations
    override suspend fun getCargosByOrigin(origin: String): Result<List<Cargo>> {
        Timber.d("Getting cargos by origin: $origin")
        
        return try {
            firebaseCargoDataSource.getCargosByOrigin(origin)
                .mapError("Get cargos by origin")
                .onSuccess { 
                    Timber.d("Found ${it.size} cargos from origin: $origin")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to get cargos by origin: $origin")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Get cargos by origin $origin")
            Result.failure(domainException)
        }
    }
    
    override suspend fun getCargosByDestination(destination: String): Result<List<Cargo>> {
        Timber.d("Getting cargos by destination: $destination")
        
        return try {
            firebaseCargoDataSource.getCargosByDestination(destination)
                .mapError("Get cargos by destination")
                .onSuccess { 
                    Timber.d("Found ${it.size} cargos to destination: $destination")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to get cargos by destination: $destination")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Get cargos by destination $destination")
            Result.failure(domainException)
        }
    }
    
    override suspend fun getCargosByStatus(statusIndex: Int): Result<List<Cargo>> {
        Timber.d("Getting cargos by status: $statusIndex")
        
        return try {
            firebaseCargoDataSource.getCargosByStatus(statusIndex)
                .mapError("Get cargos by status")
                .onSuccess { 
                    Timber.d("Found ${it.size} cargos with status: $statusIndex")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to get cargos by status: $statusIndex")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Get cargos by status $statusIndex")
            Result.failure(domainException)
        }
    }
    
    override suspend fun getCargosByRoute(origin: String, destination: String): Result<List<Cargo>> {
        return firebaseCargoDataSource.getCargosByRoute(origin, destination)
    }
    
    override fun getCargosByOriginFlow(origin: String): Flow<List<Cargo>> {
        return firebaseCargoDataSource.getCargosByOriginFlow(origin)
    }
    
    override fun getCargosByDestinationFlow(destination: String): Flow<List<Cargo>> {
        return firebaseCargoDataSource.getCargosByDestinationFlow(destination)
    }
    
    override fun getCargosByStatusFlow(statusIndex: Int): Flow<List<Cargo>> {
        return firebaseCargoDataSource.getCargosByStatusFlow(statusIndex)
    }
    
    override fun getCargosByRouteFlow(origin: String, destination: String): Flow<List<Cargo>> {
        return firebaseCargoDataSource.getCargosByRouteFlow(origin, destination)
    }
    
    // Search operations
    override suspend fun searchCargos(query: String): Result<List<Cargo>> {
        Timber.d("Searching cargos with query: $query")
        
        return try {
            firebaseCargoDataSource.searchCargos(query)
                .mapError("Search cargos")
                .onSuccess { 
                    Timber.d("Found ${it.size} cargos matching query: $query")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to search cargos with query: $query")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Search cargos with query: $query")
            Result.failure(domainException)
        }
    }
    
    override fun searchCargosFlow(query: String): Flow<List<Cargo>> {
        return firebaseCargoDataSource.searchCargosFlow(query)
    }
    
    // Selective real-time listeners
    override fun listenForUpdates(filters: QueryFilters?): Flow<List<Cargo>> {
        return firebaseCargoDataSource.listenForUpdates(filters)
    }
    
    override fun listenForCargoUpdates(cargoId: String): Flow<Cargo?> {
        return firebaseCargoDataSource.listenForCargoUpdates(cargoId)
    }
    
    override fun listenForCargosByStatusUpdates(statusIndex: Int): Flow<List<Cargo>> {
        return firebaseCargoDataSource.listenForCargosByStatusUpdates(statusIndex)
    }
    
    override fun listenForCargosByRouteUpdates(origin: String, destination: String): Flow<List<Cargo>> {
        return firebaseCargoDataSource.listenForCargosByRouteUpdates(origin, destination)
    }
    
    override fun stopListening() {
        // No-op for Flow-based approach - listeners are automatically cleaned up when Flow is cancelled
    }
    
    // Aggregate count operations
    override suspend fun getCargosCount(): Result<Long> {
        return firebaseCargoDataSource.getCargosCount()
    }
    
    override suspend fun getCargosCountByStatus(statusIndex: Int): Result<Long> {
        return firebaseCargoDataSource.getCargosCountByStatus(statusIndex)
    }
    
    override suspend fun getCargosCountByAllStatuses(): Result<Map<Int, Long>> {
        return firebaseCargoDataSource.getCargosCountByAllStatuses()
    }
    
    override suspend fun getCargosCountByOrigin(origin: String): Result<Long> {
        return firebaseCargoDataSource.getCargosCountByOrigin(origin)
    }
    
    override suspend fun getCargosCountByDestination(destination: String): Result<Long> {
        return firebaseCargoDataSource.getCargosCountByDestination(destination)
    }
    
    override fun getCargosCountByAllStatusesFlow(): Flow<Map<Int, Long>> {
        return firebaseCargoDataSource.getCargosCountByAllStatusesFlow()
    }
}
