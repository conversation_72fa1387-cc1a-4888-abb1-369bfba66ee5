package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow

interface ShipmentRepository {
    
    // Basic CRUD operations
    suspend fun createShipment(shipment: Shipment): Result<String>
    suspend fun getShipment(id: String): Result<Shipment>
    suspend fun updateShipment(shipment: Shipment): Result<Unit>
    suspend fun deleteShipment(id: String): Result<Unit>
    
    // Streaming operations
    fun getShipmentsFlow(): Flow<List<Shipment>>
    fun getShipmentsPagingData(): Flow<PagingData<Shipment>>
    
    // Server-side filtering with pagination
    fun getShipmentsPagingDataWithFilters(
        statusIndex: Int? = null,
        cargoId: String? = null,
        clientName: String? = null,
        clientPhone: String? = null
    ): Flow<PagingData<Shipment>>
    
    // Filtered paging operations with QueryFilters
    fun getShipmentsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Shipment>>
    
    // Query operations
    suspend fun getShipmentsByCargo(cargoId: String): Result<List<Shipment>>
    suspend fun getShipmentsByClient(clientName: String): Result<List<Shipment>>
    suspend fun getShipmentsByStatus(statusIndex: Int): Result<List<Shipment>>
    suspend fun getShipmentsByClientPhone(clientPhone: String): Result<List<Shipment>>
    
    fun getShipmentsByCargoFlow(cargoId: String): Flow<List<Shipment>>
    fun getShipmentsByClientFlow(clientName: String): Flow<List<Shipment>>
    fun getShipmentsByStatusFlow(statusIndex: Int): Flow<List<Shipment>>
    fun getShipmentsByClientPhoneFlow(clientPhone: String): Flow<List<Shipment>>
    
    // Search operations
    suspend fun searchShipments(query: String): Result<List<Shipment>>
    fun searchShipmentsFlow(query: String): Flow<List<Shipment>>
    
    // Aggregate count operations (efficient for statistics)
    suspend fun getShipmentsCountByCargo(cargoId: String): Result<Long>
    suspend fun getShipmentsCountByAllCargos(): Result<Map<String, Long>>
    
    // Flow-based aggregate operations for real-time updates  
    fun getShipmentsCountByCargoFlow(): Flow<Map<String, Long>> // cargoId -> count
}
