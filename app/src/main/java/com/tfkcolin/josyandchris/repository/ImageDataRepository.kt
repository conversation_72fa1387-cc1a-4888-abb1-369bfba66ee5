package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.ImageData
import kotlinx.coroutines.flow.Flow

interface ImageDataRepository {
    
    // Basic CRUD operations
    suspend fun createImageData(imageData: ImageData): Result<String>
    suspend fun getImageData(id: String): Result<ImageData>
    suspend fun updateImageData(imageData: ImageData): Result<Unit>
    suspend fun updateImageDataFields(id: String, fields: Map<String, Any>): Result<Unit>
    suspend fun deleteImageData(id: String): Result<Unit>
    
    // Streaming operations
    fun getImageDataFlow(): Flow<List<ImageData>>
    fun getImageDataPagingData(): Flow<PagingData<ImageData>>
    
    // Filtered paging operations
    fun getImageDataPagingDataWithFilters(
        category: String? = null,
        genre: String? = null,
        uploaded: Boolean? = null,
        searchTerm: String? = null
    ): Flow<PagingData<ImageData>>
    
    // Filtered paging operations with QueryFilters
    fun getImageDataPagingDataWithFilters(filters: com.tfkcolin.josyandchris.util.query.QueryFilters): Flow<PagingData<ImageData>>
    
    // Distinct value operations
    suspend fun getDistinctCategories(): Result<List<String>>
    suspend fun getDistinctGenres(): Result<List<String>>

    // Query operations
    suspend fun getImageDataByCategory(category: String): Result<List<ImageData>>
    suspend fun getImageDataByGenre(genre: String): Result<List<ImageData>>
    suspend fun getUploadedImageData(): Result<List<ImageData>>
    suspend fun getPendingImageData(): Result<List<ImageData>>
    
    fun getImageDataByCategoryFlow(category: String): Flow<List<ImageData>>
    fun getImageDataByGenreFlow(genre: String): Flow<List<ImageData>>
    fun getUploadedImageDataFlow(): Flow<List<ImageData>>
    fun getPendingImageDataFlow(): Flow<List<ImageData>>
    
    // Search operations
    suspend fun searchImageData(query: String): Result<List<ImageData>>
    fun searchImageDataFlow(query: String): Flow<List<ImageData>>
}
