package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.remote.datasource.FirebaseFinancialTransactionDataSource
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseFinancialTransactionRepositoryImpl @Inject constructor(
    private val firebaseTransactionDataSource: FirebaseFinancialTransactionDataSource
) : FinancialTransactionRepository {
    
    // Basic CRUD operations
    override suspend fun createTransaction(transaction: FinancialTransaction): Result<String> {
        return firebaseTransactionDataSource.createTransaction(transaction)
    }
    
    override suspend fun getTransaction(id: String): Result<FinancialTransaction> {
        return firebaseTransactionDataSource.getTransaction(id)
    }
    
    override suspend fun updateTransaction(transaction: FinancialTransaction): Result<Unit> {
        return firebaseTransactionDataSource.updateTransaction(transaction)
    }
    
    override suspend fun deleteTransaction(id: String): Result<Unit> {
        return firebaseTransactionDataSource.deleteTransaction(id)
    }
    
    // Paginated loading
    override suspend fun loadTransactionsPaged(limit: Int, startAfter: String?): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsPaged(limit = limit, startAfter = startAfter)
    }
    
    // Streaming operations
    override fun getTransactionsFlow(): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsFlow()
    }
    
    override fun getTransactionsPagingData(): Flow<PagingData<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsPagingData()
    }
    
    // Server-side filtering with pagination
    override fun getTransactionsPagingDataWithFilters(
        transactionTypeIndex: Int?,
        country: String?,
        commandId: String?,
        marked: Boolean?
    ): Flow<PagingData<FinancialTransaction>> {
        // Build additional filters map
        val additionalFilters = mutableMapOf<String, Any>()
        country?.let { additionalFilters["country"] = it }
        commandId?.let { additionalFilters["commandId"] = it }
        marked?.let { additionalFilters["marked"] = it }
        
        // Create QueryFilters with server-side filtering
        val filters = QueryFilters(
            statusIndex = transactionTypeIndex,
            additionalFilters = additionalFilters
        )
        
        // Use the enhanced paging data method with filters
        return firebaseTransactionDataSource.getTransactionsPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Filtered paging operations with QueryFilters
    override fun getTransactionsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Query operations
    override suspend fun getTransactionsByCommand(commandId: String): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByCommand(commandId)
    }
    
    override suspend fun getTransactionsByCountry(country: String): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByCountry(country)
    }
    
    override suspend fun getTransactionsByType(transactionTypeIndex: Int): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByType(transactionTypeIndex)
    }
    
    override suspend fun getMarkedTransactions(): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getMarkedTransactions(marked = true)
    }
    
    override fun getTransactionsByCommandFlow(commandId: String): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByCommandFlow(commandId)
    }
    
    override fun getTransactionsByCountryFlow(country: String): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByCountryFlow(country)
    }
    
    override fun getTransactionsByTypeFlow(transactionTypeIndex: Int): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getTransactionsByTypeFlow(transactionTypeIndex)
    }
    
    override fun getMarkedTransactionsFlow(): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.getMarkedTransactionsFlow(marked = true)
    }
    
    // Search operations
    override suspend fun searchTransactions(query: String): Result<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.searchTransactions(query)
    }
    
    override fun searchTransactionsFlow(query: String): Flow<List<FinancialTransaction>> {
        return firebaseTransactionDataSource.searchTransactionsFlow(query)
    }
    
    // Aggregate operations
    override suspend fun getTransactionsTotalByCountryAndType(
        country: String,
        transactionTypeIndex: Int
    ): Result<Double> {
        // For now, we'll fetch the transactions and calculate the total
        // In a future optimization, this could be done with Firebase aggregation
        return try {
            val transactions = firebaseTransactionDataSource.getTransactionsByCountryAndType(
                country = country,
                transactionTypeIndex = transactionTypeIndex
            ).getOrElse { emptyList() }
            
            val total = transactions.sumOf { it.price.toDouble() }
            Result.success(total)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override fun getTransactionsTotalsByCountryFlow(country: String): Flow<Pair<Double, Double>> {
        return firebaseTransactionDataSource.getTransactionsTotalsByCountryFlow(country)
    }
    
    override fun getTransactionsTotalsByAllCountriesFlow(): Flow<Map<String, Pair<Double, Double>>> {
        return firebaseTransactionDataSource.getTransactionsTotalsByAllCountriesFlow()
    }
}
