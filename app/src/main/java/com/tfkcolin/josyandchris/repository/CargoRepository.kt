package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow

interface CargoRepository {
    
    // Basic CRUD operations
    suspend fun createCargo(cargo: Cargo): Result<String>
    suspend fun getCargo(id: String): Result<Cargo>
    suspend fun updateCargo(cargo: Cargo): Result<Unit>
    suspend fun deleteCargo(id: String): Result<Unit>
    
    // Streaming operations
    fun getCargosFlow(): Flow<List<Cargo>>
    fun getCargosPagingData(): Flow<PagingData<Cargo>>
    
    // Filtered paging operations
    fun getCargosPagingDataWithFilters(
        statusIndex: Int? = null,
        origin: String? = null,
        destination: String? = null
    ): Flow<PagingData<Cargo>>
    
    // Filtered paging operations with QueryFilters
    fun getCargosPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Cargo>>
    
    // Query operations
    suspend fun getCargosByOrigin(origin: String): Result<List<Cargo>>
    suspend fun getCargosByDestination(destination: String): Result<List<Cargo>>
    suspend fun getCargosByStatus(statusIndex: Int): Result<List<Cargo>>
    suspend fun getCargosByRoute(origin: String, destination: String): Result<List<Cargo>>
    
    fun getCargosByOriginFlow(origin: String): Flow<List<Cargo>>
    fun getCargosByDestinationFlow(destination: String): Flow<List<Cargo>>
    fun getCargosByStatusFlow(statusIndex: Int): Flow<List<Cargo>>
    fun getCargosByRouteFlow(origin: String, destination: String): Flow<List<Cargo>>
    
    // Search operations
    suspend fun searchCargos(query: String): Result<List<Cargo>>
    fun searchCargosFlow(query: String): Flow<List<Cargo>>
    
    // Selective real-time listeners
    fun listenForUpdates(filters: QueryFilters? = null): Flow<List<Cargo>>
    fun listenForCargoUpdates(cargoId: String): Flow<Cargo?>
    fun listenForCargosByStatusUpdates(statusIndex: Int): Flow<List<Cargo>>
    fun listenForCargosByRouteUpdates(origin: String, destination: String): Flow<List<Cargo>>
    
    // Stop listening (handled automatically by Flow cancellation)
    // Use when you need to explicitly stop listening before Flow collection ends
    fun stopListening() // No-op for Flow-based approach, but kept for interface consistency
    
    // Aggregate count operations (efficient for statistics)
    suspend fun getCargosCount(): Result<Long>
    suspend fun getCargosCountByStatus(statusIndex: Int): Result<Long>
    suspend fun getCargosCountByAllStatuses(): Result<Map<Int, Long>>
    suspend fun getCargosCountByOrigin(origin: String): Result<Long>
    suspend fun getCargosCountByDestination(destination: String): Result<Long>
    
    // Flow-based aggregate operations for real-time updates
    fun getCargosCountByAllStatusesFlow(): Flow<Map<Int, Long>>
}
