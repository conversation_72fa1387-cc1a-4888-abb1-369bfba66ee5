package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.util.query.QueryFilters
import kotlinx.coroutines.flow.Flow

interface CountryRepository {
    
// Basic CRUD operations
    suspend fun createCountry(country: CountryData): Result<String>
    suspend fun getCountry(id: String): Result<CountryData>
    suspend fun updateCountry(country: CountryData): Result<Unit>
    suspend fun deleteCountry(id: String): Result<Unit>

    // Paginated loading
    suspend fun loadCountries(limit: Int = 20, startAfter: String? = null): Result<List<CountryData>>
    
    // Streaming operations
    fun getCountriesFlow(): Flow<List<CountryData>>
    fun getCountriesPagingData(): Flow<PagingData<CountryData>>
    
    // Server-side filtering with pagination
    fun getCountriesPagingDataWithFilters(
        name: String? = null,
        devise: String? = null,
        searchTokens: List<String>? = null
    ): Flow<PagingData<CountryData>>
    
    // Filtered paging operations with QueryFilters
    fun getCountriesPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<CountryData>>
    
    // Query operations
    suspend fun getCountriesByName(name: String): Result<List<CountryData>>
    suspend fun getCountriesByDevise(devise: String): Result<List<CountryData>>
    suspend fun getCountriesBySearchTokens(tokens: List<String>): Result<List<CountryData>>
    
    fun getCountriesByNameFlow(name: String): Flow<List<CountryData>>
    fun getCountriesByDeviseFlow(devise: String): Flow<List<CountryData>>
    
    // Search operations
    suspend fun searchCountries(query: String): Result<List<CountryData>>
    fun searchCountriesFlow(query: String): Flow<List<CountryData>>
    
    // Alphabetical ordering
    suspend fun getCountriesAlphabetically(): Result<List<CountryData>>
    fun getCountriesAlphabeticallyFlow(): Flow<List<CountryData>>
}
