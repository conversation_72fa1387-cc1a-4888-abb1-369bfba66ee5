package com.tfkcolin.josyandchris.repository

import android.net.Uri
import com.google.firebase.storage.StorageReference
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

interface StorageRepository {
    suspend fun uploadFile(path: String, fileUri: Uri): Result<String>
    suspend fun deleteFile(path: String): Result<Unit>
}

@Singleton
class StorageRepositoryImpl @Inject constructor(
    @Named("PRODUCT_IMAGES_STORAGE") private val storage: StorageReference
) : StorageRepository {
    
    override suspend fun uploadFile(
        path: String,
        fileUri: Uri
    ): Result<String> {
        return try {
            val uploadTask = storage.child(path).putFile(fileUri).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteFile(
        path: String
    ): Result<Unit> {
        return try {
            storage.child(path).delete().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
