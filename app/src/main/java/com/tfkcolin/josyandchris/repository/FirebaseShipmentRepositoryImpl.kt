package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.domain.util.ErrorMapper
import com.tfkcolin.josyandchris.domain.util.ErrorMapper.mapError
import com.tfkcolin.josyandchris.remote.datasource.FirebaseShipmentDataSource
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseShipmentRepositoryImpl @Inject constructor(
    private val firebaseShipmentDataSource: FirebaseShipmentDataSource
) : ShipmentRepository {
    
    // Basic CRUD operations
    override suspend fun createShipment(shipment: Shipment): Result<String> {
        Timber.d("Creating shipment for cargo: ${shipment.cargoId}")
        
        return try {
            firebaseShipmentDataSource.createShipment(shipment)
                .mapError("Create shipment")
                .onSuccess { 
                    Timber.i("Successfully created shipment with id: $it")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to create shipment")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Create shipment")
            Result.failure(domainException)
        }
    }
    
    override suspend fun getShipment(id: String): Result<Shipment> {
        Timber.d("Fetching shipment with id: $id")
        
        return try {
            firebaseShipmentDataSource.getShipment(id)
                .mapError("Get shipment")
                .onSuccess { 
                    Timber.d("Successfully fetched shipment: ${it.id}")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to fetch shipment with id: $id")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Get shipment $id")
            Result.failure(domainException)
        }
    }
    
    override suspend fun updateShipment(shipment: Shipment): Result<Unit> {
        Timber.d("Updating shipment: ${shipment.id}")
        
        return try {
            firebaseShipmentDataSource.updateShipment(shipment)
                .mapError("Update shipment")
                .onSuccess { 
                    Timber.i("Successfully updated shipment: ${shipment.id}")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to update shipment: ${shipment.id}")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Update shipment ${shipment.id}")
            Result.failure(domainException)
        }
    }
    
    override suspend fun deleteShipment(id: String): Result<Unit> {
        Timber.d("Deleting shipment with id: $id")
        
        return try {
            firebaseShipmentDataSource.deleteShipment(id)
                .mapError("Delete shipment")
                .onSuccess { 
                    Timber.i("Successfully deleted shipment: $id")
                }
                .onFailure { exception ->
                    Timber.e(exception, "Failed to delete shipment: $id")
                }
        } catch (e: Exception) {
            val domainException = ErrorMapper.mapToDomainException(e, "Delete shipment $id")
            Result.failure(domainException)
        }
    }
    
    // Streaming operations
    override fun getShipmentsFlow(): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsFlow()
    }
    
    override fun getShipmentsPagingData(): Flow<PagingData<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsPagingData()
    }
    
    // Server-side filtering with pagination
    override fun getShipmentsPagingDataWithFilters(
        statusIndex: Int?,
        cargoId: String?,
        clientName: String?,
        clientPhone: String?
    ): Flow<PagingData<Shipment>> {
        // Build additional filters map
        val additionalFilters = mutableMapOf<String, Any>()
        cargoId?.let { additionalFilters["cargoId"] = it }
        clientName?.let { additionalFilters["clientName"] = it }
        clientPhone?.let { additionalFilters["clientPhone"] = it }
        
        // Create QueryFilters with server-side filtering
        val filters = QueryFilters(
            statusIndex = statusIndex,
            additionalFilters = additionalFilters
        )
        
        // Use the enhanced paging data method with filters
        return firebaseShipmentDataSource.getShipmentsPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Implement the missing method
    override fun getShipmentsPagingDataWithFilters(filters: QueryFilters): Flow<PagingData<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsPagingDataWithFilters(
            filters = filters,
            cursorConfig = CursorConfig(
                orderBy = "created",
                orderDirection = com.google.firebase.firestore.Query.Direction.DESCENDING
            )
        )
    }
    
    // Query operations
    override suspend fun getShipmentsByCargo(cargoId: String): Result<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByCargo(cargoId)
    }
    
    override suspend fun getShipmentsByClient(clientName: String): Result<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByClient(clientName)
    }
    
    override suspend fun getShipmentsByStatus(statusIndex: Int): Result<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByStatus(statusIndex)
    }
    
    override suspend fun getShipmentsByClientPhone(clientPhone: String): Result<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByClientPhone(clientPhone)
    }
    
    override fun getShipmentsByCargoFlow(cargoId: String): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByCargoFlow(cargoId)
    }
    
    override fun getShipmentsByClientFlow(clientName: String): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByClientFlow(clientName)
    }
    
    override fun getShipmentsByStatusFlow(statusIndex: Int): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByStatusFlow(statusIndex)
    }
    
    override fun getShipmentsByClientPhoneFlow(clientPhone: String): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.getShipmentsByClientPhoneFlow(clientPhone)
    }
    
    // Search operations
    override suspend fun searchShipments(query: String): Result<List<Shipment>> {
        return firebaseShipmentDataSource.searchShipments(query)
    }
    
    override fun searchShipmentsFlow(query: String): Flow<List<Shipment>> {
        return firebaseShipmentDataSource.searchShipmentsFlow(query)
    }
    
    // Aggregate count operations
    override suspend fun getShipmentsCountByCargo(cargoId: String): Result<Long> {
        return firebaseShipmentDataSource.getShipmentsCountByCargo(cargoId)
    }
    
    override suspend fun getShipmentsCountByAllCargos(): Result<Map<String, Long>> {
        return firebaseShipmentDataSource.getShipmentsCountByAllCargos()
    }
    
    override fun getShipmentsCountByCargoFlow(): Flow<Map<String, Long>> {
        return firebaseShipmentDataSource.getShipmentsCountByCargoFlow()
    }
}
