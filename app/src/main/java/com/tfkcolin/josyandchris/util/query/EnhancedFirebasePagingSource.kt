package com.tfkcolin.josyandchris.util.query

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await

/**
 * Enhanced Firebase PagingSource that works with QueryBuilder pattern for consistent pagination
 * across all entity types in the application.
 * 
 * This PagingSource implementation integrates with the QueryBuilder pattern to provide
 * cursor-based pagination for Firestore queries.
 *
 * @param T The data type of items being paginated
 * @param queryBuilder The QueryBuilder instance for creating queries
 * @param filters The query filters to apply
 * @param cursorConfig Initial cursor configuration
 * @param mapper Function to map DocumentSnapshot to data type T
 */
class EnhancedFirebasePagingSource<T : Any>(
    private val queryBuilder: QueryBuilder<T>,
    private val filters: QueryFilters,
    private val cursorConfig: CursorConfig,
    private val mapper: (DocumentSnapshot) -> T?
) : PagingSource<DocumentSnapshot, T>() {

    override fun getRefreshKey(state: PagingState<DocumentSnapshot, T>): DocumentSnapshot? {
        // Return null to always refresh from the beginning
        return null
    }

    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, T> {
        return try {
            // Update cursor with the key from params (for subsequent pages)
            val cursor = cursorConfig.copy(
                startAfter = params.key,
                limit = params.loadSize
            )

            // Build the query using the QueryBuilder
            val query = queryBuilder.build(filters, cursor)
            
            // Execute the query
            val snapshot = query.get().await()
            
            // Map documents to data objects
            val items = snapshot.documents.mapNotNull { doc ->
                mapper(doc)
            }

            // Determine the next key
            val nextKey = if (snapshot.documents.size < params.loadSize) {
                null // No more pages
            } else {
                snapshot.documents.lastOrNull() // Use last document as cursor for next page
            }

            LoadResult.Page(
                data = items,
                prevKey = null, // Only paging forward
                nextKey = nextKey
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }
}
