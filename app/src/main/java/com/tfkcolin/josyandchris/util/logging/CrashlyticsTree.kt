package com.tfkcolin.josyandchris.util.logging

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber

/**
 * A Timber tree that logs to Firebase Crashlytics for production builds.
 * Only logs warnings, errors, and WTF level logs to avoid cluttering Crashlytics.
 */
class CrashlyticsTree : Timber.Tree() {
    
    private val crashlytics = FirebaseCrashlytics.getInstance()
    
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // Only log WARN, ERROR, and WTF levels to Crashlytics
        if (priority < Log.WARN) {
            return
        }
        
        // Log the message to Crashlytics
        crashlytics.log("$tag: $message")
        
        // If there's an exception, record it as a non-fatal exception
        t?.let { throwable ->
            crashlytics.recordException(throwable)
        }
    }
}
