package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.Query
import java.util.*

/**
 * Base interface for query builders that create Firebase Firestore queries
 * with unified filter parameters and cursor handling.
 */
interface QueryBuilder<T> {
    /**
     * Builds a Firestore query with the specified filters and pagination parameters.
     *
     * @param filters The filter parameters to apply
     * @param cursor The pagination cursor parameters
     * @return A configured Firestore Query
     */
    fun build(filters: QueryFilters, cursor: CursorConfig): Query
}

/**
 * Common filter parameters that can be applied to queries.
 * 
 * This class provides a unified interface for filtering Firestore queries across different
 * entity types. It supports status filtering, date ranges, text search, and custom filters.
 * 
 * @property statusIndex Optional status filter index
 * @property dateRange Optional date range for filtering by creation or modification date
 * @property searchTerm Optional search term for text-based filtering
 * @property searchTokens Optional list of search tokens for array-contains-any queries
 * @property additionalFilters Map of additional field-specific filters
 */
data class QueryFilters(
    val statusIndex: Int? = null,
    val dateRange: DateRange? = null,
    val searchTerm: String? = null,
    val searchTokens: List<String>? = null, // For whereArrayContainsAny search
    val additionalFilters: Map<String, Any> = emptyMap()
)

/**
 * Date range filter for queries.
 * 
 * Used to specify a time period for filtering time-based queries.
 * 
 * @property startDate The start date of the range (inclusive)
 * @property endDate The end date of the range (inclusive)
 */
data class DateRange(
    val startDate: Date,
    val endDate: Date
)

/**
 * Cursor configuration for pagination.
 * 
 * This class encapsulates pagination parameters for Firestore queries, allowing
 * for consistent pagination implementation across different entity types.
 * 
 * @property startAfter Optional document snapshot to start after (for pagination)
 * @property limit Maximum number of results to return per page
 * @property orderBy Field to order results by
 * @property orderDirection Direction to order results (ascending or descending)
 */
data class CursorConfig(
    val startAfter: DocumentSnapshot? = null,
    val limit: Int = 20,
    val orderBy: String = "created",
    val orderDirection: Query.Direction = Query.Direction.DESCENDING
)

/**
 * Result of a query build operation, containing both the query and metadata.
 */
data class QueryResult(
    val query: Query,
    val hasFilters: Boolean,
    val appliedFilters: List<String>
)
