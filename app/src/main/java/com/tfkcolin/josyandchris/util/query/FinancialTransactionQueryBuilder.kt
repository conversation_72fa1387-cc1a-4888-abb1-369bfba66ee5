package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.FinancialTransaction

/**
 * QueryBuilder implementation for FinancialTransaction queries.
 * Handles filtering by transaction type, country, command ID, marked status, and date range.
 */
class FinancialTransactionQueryBuilder(
    private val firestore: FirebaseFirestore,
    private val collectionName: String = "financial_transactions"
) : QueryBuilder<FinancialTransaction> {

    override fun build(filters: QueryFilters, cursor: CursorConfig): Query {
        var query: Query = firestore.collection(collectionName)
        
        // Apply transaction type filter (using statusIndex for consistency)
        filters.statusIndex?.let { transactionTypeIndex ->
            query = query.whereEqualTo("transactionTypeIndex", transactionTypeIndex)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("created", dateRange.startDate)
                .whereLessThanOrEqualTo("created", dateRange.endDate)
        }
        
        // Apply search term filter (search in label)
        filters.searchTerm?.let { searchTerm ->
            if (searchTerm.isNotBlank()) {
                // For label search, we use a range query for prefix matching
                val searchEnd = searchTerm + "\uf8ff"
                query = query.whereGreaterThanOrEqualTo("label", searchTerm)
                    .whereLessThan("label", searchEnd)
            }
        }
        
        // Apply additional filters
        filters.additionalFilters.forEach { (field, value) ->
            query = query.whereEqualTo(field, value)
        }
        
        // Apply ordering
        query = query.orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            query = query.startAfter(startAfter)
        }
        
        // Apply limit
        return query.limit(cursor.limit.toLong())
    }
    
    /**
     * Builds a query specifically for transactions by command ID.
     */
    fun buildCommandFilter(commandId: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("commandId" to commandId)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for transactions by country.
     */
    fun buildCountryFilter(country: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("country" to country)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for transactions by type.
     */
    fun buildTransactionTypeFilter(transactionTypeIndex: Int, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            statusIndex = transactionTypeIndex
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for marked transactions.
     */
    fun buildMarkedFilter(marked: Boolean, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("marked" to marked)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for transactions with multiple filters.
     */
    fun buildMultipleFilters(
        transactionTypeIndex: Int? = null,
        country: String? = null,
        commandId: String? = null,
        marked: Boolean? = null,
        cursor: CursorConfig
    ): Query {
        val additionalFilters = mutableMapOf<String, Any>()
        country?.let { additionalFilters["country"] = it }
        commandId?.let { additionalFilters["commandId"] = it }
        marked?.let { additionalFilters["marked"] = it }
        
        val filters = QueryFilters(
            statusIndex = transactionTypeIndex,
            additionalFilters = additionalFilters
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for transactions with price range filter.
     */
    fun buildPriceRangeFilter(
        minPrice: Int,
        maxPrice: Int? = null,
        cursor: CursorConfig
    ): Query {
        var baseQuery: Query = firestore.collection(collectionName)
        
        baseQuery = baseQuery.whereGreaterThanOrEqualTo("price", minPrice)
        maxPrice?.let { max ->
            baseQuery = baseQuery.whereLessThanOrEqualTo("price", max)
        }
        
        baseQuery = baseQuery.orderBy("price", Query.Direction.DESCENDING)
            .orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            baseQuery = baseQuery.startAfter(startAfter)
        }
        
        return baseQuery.limit(cursor.limit.toLong())
    }
}
