package com.tfkcolin.josyandchris.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.graphics.ColorUtils

fun Color.toHsl(): FloatArray {
    val hsl = FloatArray(3)
    ColorUtils.colorToHSL(this.toArgb(), hsl)
    return hsl
}

fun FloatArray.toColor(): Color {
    val argb = ColorUtils.HSLToColor(this)
    return Color(argb)
}

fun Color.lighten(amount: Float): Color {
    val hsl = this.toHsl()
    hsl[2] += amount
    return hsl.toColor()
}