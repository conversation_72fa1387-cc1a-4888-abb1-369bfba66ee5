package com.tfkcolin.josyandchris.util

import java.text.Normalizer
import java.util.Locale

/**
 * Utility functions for generating full-text search index tokens with prefixes.
 * This enables efficient prefix search using Firestore's array-contains-any queries.
 */
object SearchIndexUtils {
    
    /**
     * Generates prefix tokens for full-text search from a given text.
     * 
     * Example: "Yaoundé" → ["y", "ya", "yao", "yaou", "yaoun", "yaound", "yaoundé"]
     * 
     * @param text The text to generate tokens from
     * @param minLength Minimum length of tokens to generate (default: 1)
     * @return List of lowercase prefix tokens
     */
    fun generatePrefixTokens(text: String, minLength: Int = 1): List<String> {
        if (text.isBlank()) return emptyList()
        
        val normalized = normalizeText(text)
        val tokens = mutableSetOf<String>()
        
        // Generate prefix tokens for each word
        normalized.split("\\s+".toRegex()).forEach { word ->
            if (word.length >= minLength) {
                // Generate all prefixes of the word
                for (i in minLength..word.length) {
                    tokens.add(word.substring(0, i))
                }
            }
        }
        
        return tokens.toList()
    }
    
    /**
     * Generates search index tokens from multiple text fields.
     * Combines prefix tokens from all provided texts.
     * 
     * @param texts List of texts to generate tokens from
     * @param minLength Minimum length of tokens to generate (default: 1)
     * @return List of unique lowercase prefix tokens
     */
    fun generateSearchIndex(texts: List<String>, minLength: Int = 1): List<String> {
        val allTokens = mutableSetOf<String>()
        
        texts.forEach { text ->
            if (text.isNotBlank()) {
                allTokens.addAll(generatePrefixTokens(text, minLength))
            }
        }
        
        return allTokens.toList()
    }
    
    /**
     * Normalizes text by removing accents, converting to lowercase, and trimming.
     * 
     * @param text The text to normalize
     * @return Normalized text
     */
    private fun normalizeText(text: String): String {
        return text.trim()
            .lowercase(Locale.getDefault())
            .let { removeAccents(it) }
    }
    
    /**
     * Removes accents from text using Unicode normalization.
     * 
     * @param text The text to process
     * @return Text without accents
     */
    private fun removeAccents(text: String): String {
        return Normalizer.normalize(text, Normalizer.Form.NFD)
            .replace("\\p{M}".toRegex(), "")
    }
    
    /**
     * Generates search tokens for a query string.
     * These tokens can be used with array-contains-any queries.
     * 
     * @param query The search query
     * @param minLength Minimum length of tokens to generate (default: 1)
     * @return List of normalized query tokens
     */
    fun generateQueryTokens(query: String, minLength: Int = 1): List<String> {
        if (query.isBlank()) return emptyList()
        
        val normalized = normalizeText(query)
        val tokens = mutableSetOf<String>()
        
        // Split query into words and add each word as a token
        normalized.split("\\s+".toRegex()).forEach { word ->
            if (word.length >= minLength) {
                tokens.add(word)
            }
        }
        
        return tokens.toList()
    }
}
