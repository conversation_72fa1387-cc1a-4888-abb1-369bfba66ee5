package com.tfkcolin.josyandchris.util

/*import android.content.pm.ApplicationInfo

fun isDebug(appInfo: ApplicationInfo) = 0 != appInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE

val loadLabels = listOf<String>(
    "Transfert Chine" +
            "Retrait colis bateau" +
            "Retrait colis Avion" +
            "Transport" +
            "Loyer" +
            "Achat TTC" +
            "Internet" +
            "Matériel de bureau" +
            "Salaire Personnel" +
            "Voyage Douala" +
            "Livraison Article" +
            "Impôts" +
            "Publicité" +
            "Repas" +
            "Imprévus" +
            "Autres" +
            "Electricité et Eau" +
            "Nettoyage" +
            "Prêt accordé" +
            "nettoya" +
            "Remboursement client HT" +
            "Remboursement client TTC" +
            "Achat Electronique" +
            "Remboursement dette" +
            "Créances accordée"
)

val inputLabels = listOf(
    "Vente TTC" +
            "Vente HT" +
            "Autre" +
            "Dette contractée" +
            "Vente électronique" +
            "Dette contractée" +
            "Créance remboursée"
)*/