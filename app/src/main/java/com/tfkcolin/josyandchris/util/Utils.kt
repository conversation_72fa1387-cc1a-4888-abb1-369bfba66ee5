package com.tfkcolin.josyandchris.util

import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import java.io.File

fun Int.formatToPrice(): String {
    var str = "$this"
    var out = ""
    while(str.length >= 4){
        out = str.substring(str.length - 3) + " $out"
        str = str.substring(0, str.length - 3)
    }
    out = "$str $out"
    return out
}

fun getPreferences(activity: Activity) = activity.getPreferences(Context.MODE_PRIVATE)

fun Context.getFilename(uri: Uri): String? = when(uri.scheme){
    ContentResolver.SCHEME_CONTENT -> getContentFilename(uri)
    else -> uri.path?.let(::File)?.name
}

private fun Context.getContentFilename(uri: Uri): String? = runCatching {
    contentResolver.query(uri, null, null, null, null)?.use { cursor ->
        cursor.moveToFirst()
        return@use cursor.getColumnIndexOrThrow(OpenableColumns.DISPLAY_NAME).let(cursor::getString)
    }
}.getOrNull()

val dashedBackground: Modifier = Modifier.composed {
    val color = MaterialTheme.colorScheme.tertiary
    Modifier.drawBehind {
        drawRoundRect(
            color = color,
            style = Stroke(
                width = 3f,
                cap = StrokeCap.Butt,
                join = StrokeJoin.Bevel,
                pathEffect = PathEffect.dashPathEffect(
                    floatArrayOf(
                        10f,
                        30f
                    ), phase = 0f
                )
            ),
            size = size
        )
    }
}