package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.Shipment
import kotlinx.coroutines.tasks.await

/**
 * Factory class for creating QueryBuilder instances.
 */
class QueryBuilderFactory(private val firestore: FirebaseFirestore) {
    
    /**
     * Creates a CommandQueryBuilder instance.
     */
    fun createCommandQueryBuilder(): CommandQueryBuilder {
        return CommandQueryBuilder(firestore)
    }
    
    /**
     * Creates a CargoQueryBuilder instance.
     */
    fun createCargoQueryBuilder(): CargoQueryBuilder {
        return CargoQueryBuilder(firestore)
    }
    
    /**
     * Creates a ShipmentQueryBuilder instance.
     */
    fun createShipmentQueryBuilder(): ShipmentQueryBuilder {
        return ShipmentQueryBuilder(firestore)
    }
    
    /**
     * Creates an ImageQueryBuilder instance.
     */
    fun createImageQueryBuilder(): ImageQueryBuilder {
        return ImageQueryBuilder(firestore)
    }
}
