package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.CountryData

/**
 * QueryBuilder implementation for Country queries.
 * Handles filtering by country name, devise, and search operations.
 */
class CountryQueryBuilder(
    private val firestore: FirebaseFirestore,
    private val collectionName: String = "countries"
) : QueryBuilder<CountryData> {

    override fun build(filters: QueryFilters, cursor: CursorConfig): Query {
        var query: Query = firestore.collection(collectionName)
        
        // Apply search term filter (search in name)
        filters.searchTerm?.let { searchTerm ->
            if (searchTerm.isNotBlank()) {
                // For name search, we use a range query for prefix matching
                val searchEnd = searchTerm + "\uf8ff"
                query = query.whereGreaterThanOrEqualTo("name", searchTerm)
                    .whereLess<PERSON>han("name", searchEnd)
            }
        }
        
        // Apply search tokens filter for array-based search
        filters.searchTokens?.let { tokens ->
            if (tokens.isNotEmpty()) {
                // Use whereArrayContainsAny if searchIndex is an array field
                query = query.whereArrayContainsAny("searchIndex", tokens)
            }
        }
        
        // Apply additional filters
        filters.additionalFilters.forEach { (field, value) ->
            query = query.whereEqualTo(field, value)
        }
        
        // Apply ordering
        query = query.orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            query = query.startAfter(startAfter)
        }
        
        // Apply limit
        return query.limit(cursor.limit.toLong())
    }
    
    /**
     * Builds a query specifically for countries by name.
     */
    fun buildNameFilter(name: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            searchTerm = name
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for countries by devise.
     */
    fun buildDeviseFilter(devise: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("devise" to devise)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for countries with search tokens.
     */
    fun buildSearchTokensFilter(tokens: List<String>, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            searchTokens = tokens
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for countries with exact name match.
     */
    fun buildExactNameFilter(name: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("name" to name)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for countries with multiple filters.
     */
    fun buildMultipleFilters(
        name: String? = null,
        devise: String? = null,
        searchTokens: List<String>? = null,
        cursor: CursorConfig
    ): Query {
        val additionalFilters = mutableMapOf<String, Any>()
        name?.let { additionalFilters["name"] = it }
        devise?.let { additionalFilters["devise"] = it }
        
        val filters = QueryFilters(
            searchTokens = searchTokens,
            additionalFilters = additionalFilters
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for countries ordered by name alphabetically.
     */
    fun buildAlphabeticalQuery(cursor: CursorConfig): Query {
        val alphabeticalCursor = cursor.copy(
            orderBy = "name",
            orderDirection = Query.Direction.ASCENDING
        )
        return build(QueryFilters(), alphabeticalCursor)
    }
}
