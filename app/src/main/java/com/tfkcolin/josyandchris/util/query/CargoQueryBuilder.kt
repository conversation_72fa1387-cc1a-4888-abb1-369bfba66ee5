package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.Cargo

/**
 * QueryBuilder implementation for Cargo queries.
 * Handles filtering by status, origin, destination, and date range.
 */
class CargoQueryBuilder(
    private val firestore: FirebaseFirestore,
    private val collectionName: String = "cargos"
) : QueryBuilder<Cargo> {

    override fun build(filters: QueryFilters, cursor: CursorConfig): Query {
        var query: Query = firestore.collection(collectionName)
        
        // Apply status filter
        filters.statusIndex?.let { statusIndex ->
            query = query.whereEqualTo("statusIndex", statusIndex)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("created", dateRange.startDate)
                .whereLessThanOrEqualTo("created", dateRange.endDate)
        }
        
        // Apply search term filter (search in searchIndex)
        filters.searchTerm?.let { searchTerm ->
            if (searchTerm.isNotBlank()) {
                // For search index
                query = query.whereGreaterThanOrEqualTo("searchIndex", searchTerm)
            }
        }
        
        // Apply additional filters
        filters.additionalFilters.forEach { (field, value) ->
            query = query.whereEqualTo(field, value)
        }
        
        // Apply ordering
        query = query.orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            query = query.startAfter(startAfter)
        }
        
        // Apply limit
        return query.limit(cursor.limit.toLong())
    }
    
    /**
     * Builds a query specifically for cargos by origin.
     */
    fun buildOriginFilter(origin: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("origin" to origin)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for cargos by destination.
     */
    fun buildDestinationFilter(destination: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("destination" to destination)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for cargos within a specific date range.
     */
    fun buildDateRangeFilter(
        dateRange: DateRange,
        cursor: CursorConfig
    ): Query {
        val filters = QueryFilters(
            dateRange = dateRange
        )
        return build(filters, cursor)
    }
}
