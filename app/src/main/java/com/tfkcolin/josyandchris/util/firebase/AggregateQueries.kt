package com.tfkcolin.josyandchris.util.firebase

import com.google.firebase.firestore.AggregateSource
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await
import timber.log.Timber

/**
 * Utility object for Firebase aggregate queries to reduce read counts
 */
object AggregateQueries {
    
    /**
     * Get count of documents matching a query
     * Uses Firebase aggregate queries to reduce read costs
     */
    suspend fun getCount(query: Query): Long {
        return try {
            val countQuery = query.count()
            val snapshot = countQuery.get(AggregateSource.SERVER).await()
            snapshot.count
        } catch (e: Exception) {
            Timber.e(e, "Error getting count from aggregate query")
            0L
        }
    }
    
    /**
     * Get count of documents in a collection
     */
    suspend fun getCollectionCount(
        firestore: FirebaseFirestore,
        collectionPath: String
    ): Long {
        return try {
            val query = firestore.collection(collectionPath)
            getCount(query)
        } catch (e: Exception) {
            Timber.e(e, "Error getting collection count for $collectionPath")
            0L
        }
    }
    
    /**
     * Get count of documents matching a field value
     */
    suspend fun getCountByField(
        firestore: FirebaseFirestore,
        collectionPath: String,
        fieldName: String,
        fieldValue: Any
    ): Long {
        return try {
            val query = firestore.collection(collectionPath)
                .whereEqualTo(fieldName, fieldValue)
            getCount(query)
        } catch (e: Exception) {
            Timber.e(e, "Error getting count by field $fieldName = $fieldValue")
            0L
        }
    }
    
    /**
     * Get multiple counts in a single batch for efficiency
     */
    suspend fun getMultipleCounts(queries: List<Query>): List<Long> {
        return queries.map { query ->
            getCount(query)
        }
    }
}
