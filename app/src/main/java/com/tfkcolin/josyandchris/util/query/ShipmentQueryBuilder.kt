package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.Shipment

/**
 * QueryBuilder implementation for Shipment queries.
 * Handles filtering by status, date range, search terms, and cargo information.
 */
class ShipmentQueryBuilder(
    private val firestore: FirebaseFirestore,
    private val collectionName: String = "shipments"
) : QueryBuilder<Shipment> {

    override fun build(filters: QueryFilters, cursor: CursorConfig): Query {
        var query: Query = firestore.collection(collectionName)
        
        // Apply status filter
        filters.statusIndex?.let { statusIndex ->
            query = query.whereEqualTo("statusIndex", statusIndex)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("created", dateRange.startDate)
                .whereLessThan("created", dateRange.endDate)
        }
        
        // Apply search term filter (search in searchIndex array)
        // Priority: searchTokens > searchTerm
        // Note: whereArrayContainsAny cannot be combined with range queries or other array-contains queries
        when {
            !filters.searchTokens.isNullOrEmpty() -> {
                query = query.whereArrayContainsAny("searchIndex", filters.searchTokens)
            }
            !filters.searchTerm.isNullOrBlank() -> {
                // Convert single search term to tokens (split by spaces, lowercase)
                val tokens = filters.searchTerm.lowercase().split(" ").filter { it.isNotBlank() }
                if (tokens.isNotEmpty()) {
                    query = query.whereArrayContainsAny("searchIndex", tokens)
                }
            }
        }
        
        // Apply additional filters
        filters.additionalFilters.forEach { (field, value) ->
            query = query.whereEqualTo(field, value)
        }
        
        // Apply ordering - must be on a field that's used in the query
        query = query.orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            query = query.startAfter(startAfter)
        }
        
        // Apply limit
        return query.limit(cursor.limit.toLong())
    }
    
    /**
     * Builds a query specifically for filtering shipments by cargo ID.
     */
    fun buildCargoFilter(cargoId: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("cargoId" to cargoId)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for filtering shipments by client name.
     */
    fun buildClientNameFilter(clientName: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("clientName" to clientName)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for filtering shipments by client phone.
     */
    fun buildClientPhoneFilter(clientPhone: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("clientPhone" to clientPhone)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query specifically for filtering shipments by status.
     */
    fun buildStatusFilter(statusIndex: Int, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            statusIndex = statusIndex
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for shipments with delivery proof uploaded.
     */
    fun buildDeliveryProofFilter(proofUploaded: Boolean, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("deliveryProofUploaded" to proofUploaded)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for shipments with payment status.
     */
    fun buildPaymentStatusFilter(isPaid: Boolean, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("isPaid" to isPaid)
        )
        return build(filters, cursor)
    }
    
    /**
     * Builds a query for shipments within a specific date range and status.
     */
    fun buildDateRangeAndStatusFilter(
        dateRange: DateRange,
        statusIndex: Int?,
        cursor: CursorConfig
    ): Query {
        val filters = QueryFilters(
            statusIndex = statusIndex,
            dateRange = dateRange
        )
        return build(filters, cursor)
    }
}
