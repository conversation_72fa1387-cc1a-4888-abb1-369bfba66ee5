package com.tfkcolin.josyandchris.util

import com.tfkcolin.josyandchris.domain.util.ErrorMapper.mapError
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Safely executes a suspend function on the IO dispatcher, catches any exceptions,
 * and returns a Result that has been processed through the ErrorMapper.
 *
 * @param context Optional context string for better error reporting
 * @param block The suspend function to execute
 * @return A Result that contains either the successful value or a mapped exception
 */
suspend inline fun <T> safeCall(
    context: String? = null,
    crossinline block: suspend () -> T
): Result<T> = withContext(Dispatchers.IO) {
    runCatching { block() }.mapError(context)
}
