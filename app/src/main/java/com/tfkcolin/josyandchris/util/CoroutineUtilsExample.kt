package com.tfkcolin.josyandchris.util

import com.tfkcolin.josyandchris.domain.exception.DomainException
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * This class demonstrates how to use the safeCall utility function in real-world scenarios.
 * It's for demonstration purposes only and not intended for production use.
 */
class CoroutineUtilsExample {

    /**
     * Example of fetching data from a remote source using the safeCall utility
     */
    suspend fun fetchUserData(userId: String): Result<UserData> {
        return safeCall("Fetching user data") {
            // Simulate network delay
            delay(1000)
            
            // Simulated API call that might throw exceptions
            if (userId.isBlank()) {
                throw IllegalArgumentException("User ID cannot be empty")
            }
            
            if (userId == "error") {
                throw DomainException.NetworkException("Network connection error")
            }
            
            // Return sample data on success
            UserData(userId, "User $userId", "user$<EMAIL>")
        }.onSuccess { userData ->
            // Log success
            Timber.d("Successfully fetched user data: $userData")
        }.onFailure { error ->
            // Log error
            Timber.e(error, "Failed to fetch user data")
        }
    }
    
    /**
     * Example of performing a database operation using the safeCall utility
     */
    suspend fun saveUserPreferences(userId: String, preferences: Map<String, String>): Result<Boolean> {
        return safeCall("Saving user preferences") {
            // Simulate database operation
            delay(500)
            
            // Validate input
            if (userId.isBlank()) {
                throw IllegalArgumentException("User ID cannot be empty")
            }
            
            if (preferences.isEmpty()) {
                throw IllegalArgumentException("Preferences cannot be empty")
            }
            
            // Simulate successful save
            true
        }
    }
    
    /**
     * Example of performing a file operation using the safeCall utility
     */
    suspend fun readUserFile(filePath: String): Result<String> {
        return safeCall("Reading user file") {
            // Simulate file IO
            delay(300)
            
            // Check if file exists (simulation)
            if (filePath.contains("nonexistent")) {
                throw java.io.FileNotFoundException("File not found: $filePath")
            }
            
            // Return simulated file content
            "Sample file content for $filePath"
        }
    }
    
    /**
     * Example of multiple safeCall operations used in sequence
     */
    suspend fun completeUserOnboarding(userId: String): Result<OnboardingResult> {
        // First operation
        val userData = safeCall("Fetching user data for onboarding") {
            delay(500)
            UserData(userId, "User $userId", "user$<EMAIL>")
        }.getOrNull() ?: return Result.failure(DomainException.DataException.NotFoundException(
            "User", userId, "User data not found"
        ))
        
        // Second operation
        val preferences = safeCall("Creating default preferences") {
            delay(300)
            mapOf("theme" to "default", "notifications" to "enabled")
        }.getOrNull() ?: return Result.failure(DomainException.UnknownException(
            "Failed to create default preferences"
        ))
        
        // Third operation, using result of previous operations
        return safeCall("Finalizing onboarding") {
            delay(700)
            OnboardingResult(userData, preferences, System.currentTimeMillis())
        }
    }
    
    // Sample data classes for the examples
    data class UserData(val id: String, val name: String, val email: String)
    data class OnboardingResult(val userData: UserData, val preferences: Map<String, String>, val timestamp: Long)
}
