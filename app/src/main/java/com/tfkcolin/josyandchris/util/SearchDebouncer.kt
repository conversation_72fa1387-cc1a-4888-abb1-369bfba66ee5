package com.tfkcolin.josyandchris.util

import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*

/**
 * Utility class for creating debounced search flows using callbackFlow
 * This helps limit repository calls and improve performance
 */
object SearchDebouncer {
    
    /**
     * Creates a debounced flow that emits search queries after a specified delay
     * Uses callbackFlow to handle search state and debouncing
     */
    @OptIn(FlowPreview::class)
    fun createDebouncedSearchFlow(
        initialQuery: String = "",
        debounceTimeMs: Long = 500L
    ): Flow<String> = callbackFlow {
        var currentQuery = initialQuery
        
        // Send initial query immediately
        trySend(currentQuery)
        
        // Handle updates through callback
        val callback: (String) -> Unit = { newQuery ->
            currentQuery = newQuery
            trySend(newQuery)
        }
        
        // Keep the flow alive until cancelled
        awaitClose {
            // Cleanup if needed
        }
    }.debounce(debounceTimeMs)
        .distinctUntilChanged()
    
    /**
     * Extension function to create debounced search for StateFlow
     */
    @OptIn(FlowPreview::class)
    fun <T> StateFlow<T>.debounceDistinct(timeMs: Long = 300L): Flow<T> =
        this.debounce(timeMs).distinctUntilChanged()
}

/**
 * Search state holder that manages debounced search queries
 */
class DebouncedSearchState(
    initialQuery: String = "",
    private val debounceTimeMs: Long = 500L
) {
    private val _searchQuery = MutableStateFlow(initialQuery)
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    @OptIn(FlowPreview::class)
    val debouncedSearchQuery: Flow<String> = _searchQuery
        .debounce(debounceTimeMs)
        .distinctUntilChanged()
        .filter { it.length >= 2 || it.isEmpty() } // Only search if 2+ chars or empty (clear search)
    
    fun updateQuery(query: String) {
        _searchQuery.value = query
    }
    
    fun clearQuery() {
        _searchQuery.value = ""
    }
}
