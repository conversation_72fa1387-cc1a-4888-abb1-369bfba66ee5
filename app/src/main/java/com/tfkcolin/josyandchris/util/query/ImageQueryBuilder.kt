package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query

/**
 * QueryBuilder implementation for Image queries.
 * Handles filtering by category, genre, upload status, date range, and search terms.
 * 
 * This specialized QueryBuilder constructs Firestore queries for the ImageData entity,
 * supporting various filtering options and pagination through CursorConfig.
 * 
 * @property firestore The Firestore instance to query against
 * @property collectionName The name of the Firestore collection (default: "images")
 */
class ImageQueryBuilder(
    private val firestore: FirebaseFirestore,
    private val collectionName: String = "images_data"
) : QueryBuilder<com.tfkcolin.josyandchris.data.ImageData> {

/**
     * Builds a Firestore query with the specified filters and pagination parameters.
     * 
     * @param filters Filter parameters to apply to the query
     * @param cursor Pagination parameters for the query
     * @return A configured Firestore Query with filters and pagination applied
     */
    override fun build(filters: QueryFilters, cursor: CursorConfig): Query {
        var query: Query = firestore.collection(collectionName)
        
        // Apply category filter (if available in additionalFilters)
        filters.additionalFilters["category"]?.let { category ->
            query = query.whereEqualTo("category", category)
        }
        
        // Apply genre filter (if available in additionalFilters)
        filters.additionalFilters["genre"]?.let { genre ->
            query = query.whereEqualTo("genre", genre)
        }
        
        // Apply upload status filter (if available in additionalFilters)
        filters.additionalFilters["upload"]?.let { uploadStatus ->
            query = query.whereEqualTo("upload", uploadStatus)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("created", dateRange.startDate)
                .whereLessThanOrEqualTo("created", dateRange.endDate)
        }
        
        // Apply search term filter (search in searchIndex)
        filters.searchTerm?.let { searchTerm ->
            if (searchTerm.isNotBlank()) {
                // For search index
                query = query.whereArrayContains("searchIndex", searchTerm)
            }
        }
        
        // Apply remaining additional filters (excluding ones we've already handled)
        val handledKeys = setOf("category", "genre", "upload")
        filters.additionalFilters
            .filterNot { (key, _) -> handledKeys.contains(key) }
            .forEach { (field, value) ->
                query = query.whereEqualTo(field, value)
            }
        
        // Apply ordering (default is "created" DESC as specified in CursorConfig)
        query = query.orderBy(cursor.orderBy, cursor.orderDirection)
        
        // Apply cursor pagination
        cursor.startAfter?.let { startAfter ->
            query = query.startAfter(startAfter)
        }
        
        // Apply limit
        return query.limit(cursor.limit.toLong())
    }
    
/**
     * Builds a query specifically for images by category.
     * 
     * @param category The category to filter by
     * @param cursor The pagination configuration
     * @return A Firestore query filtered by the specified category
     */
    fun buildCategoryFilter(category: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("category" to category)
        )
        return build(filters, cursor)
    }
    
/**
     * Builds a query specifically for images by genre.
     * 
     * @param genre The genre to filter by
     * @param cursor The pagination configuration
     * @return A Firestore query filtered by the specified genre
     */
    fun buildGenreFilter(genre: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("genre" to genre)
        )
        return build(filters, cursor)
    }
    
/**
     * Builds a query for images by upload status.
     * 
     * @param isUploaded Boolean flag indicating upload status to filter by
     * @param cursor The pagination configuration
     * @return A Firestore query filtered by the specified upload status
     */
    fun buildUploadStatusFilter(isUploaded: Boolean, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            additionalFilters = mapOf("upload" to isUploaded)
        )
        return build(filters, cursor)
    }
    
/**
     * Builds a query for images within a specific date range.
     * 
     * @param dateRange The date range to filter by (start and end dates)
     * @param cursor The pagination configuration
     * @return A Firestore query filtered by the specified date range
     */
    fun buildDateRangeFilter(
        dateRange: DateRange,
        cursor: CursorConfig
    ): Query {
        val filters = QueryFilters(
            dateRange = dateRange
        )
        return build(filters, cursor)
    }
    
/**
     * Builds a query for images matching a search term.
     * 
     * @param searchTerm The search term to match against image metadata
     * @param cursor The pagination configuration
     * @return A Firestore query filtered by the specified search term
     */
    fun buildSearchFilter(searchTerm: String, cursor: CursorConfig): Query {
        val filters = QueryFilters(
            searchTerm = searchTerm
        )
        return build(filters, cursor)
    }
}
