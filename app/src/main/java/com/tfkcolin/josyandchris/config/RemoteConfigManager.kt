package com.tfkcolin.josyandchris.config

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours

/**
 * Remote config manager for gradual feature rollout
 * Manages feature flags and configuration values from Firebase Remote Config
 */
@Singleton
class RemoteConfigManager @Inject constructor() {

    private val remoteConfig: FirebaseRemoteConfig = FirebaseRemoteConfig.getInstance()

    companion object {
        // Feature flags for gradual rollout
        const val ENABLE_ADVANCED_FILTERS = "enable_advanced_filters"
        const val ENABLE_MODERN_COMMAND_CARDS = "enable_modern_command_cards"
        const val ENABLE_OPTIMIZED_PAGINATION = "enable_optimized_pagination"
        const val ENABLE_CACHE_LAYER = "enable_cache_layer"
        const val ENABLE_PERFORMANCE_MONITORING = "enable_performance_monitoring"
        
        // Rollout percentages
        const val ADVANCED_FILTERS_ROLLOUT_PERCENTAGE = "advanced_filters_rollout_percentage"
        const val MODERN_CARDS_ROLLOUT_PERCENTAGE = "modern_cards_rollout_percentage"
        const val OPTIMIZED_PAGINATION_ROLLOUT_PERCENTAGE = "optimized_pagination_rollout_percentage"
        
        // Performance thresholds
        const val MAX_QUERY_TIME_MS = "max_query_time_ms"
        const val MAX_CACHE_SIZE_MB = "max_cache_size_mb"
        const val ENABLE_CRASH_REPORTING = "enable_crash_reporting"
        
        // UI configuration
        const val FILTER_PANEL_ANIMATION_DURATION = "filter_panel_animation_duration"
        const val CARD_ANIMATION_DELAY = "card_animation_delay"
        const val PAGINATION_PAGE_SIZE = "pagination_page_size"
        
        // Default values
        private val DEFAULT_VALUES = mapOf(
            ENABLE_ADVANCED_FILTERS to false,
            ENABLE_MODERN_COMMAND_CARDS to false,
            ENABLE_OPTIMIZED_PAGINATION to false,
            ENABLE_CACHE_LAYER to false,
            ENABLE_PERFORMANCE_MONITORING to true,
            
            ADVANCED_FILTERS_ROLLOUT_PERCENTAGE to 0L,
            MODERN_CARDS_ROLLOUT_PERCENTAGE to 0L,
            OPTIMIZED_PAGINATION_ROLLOUT_PERCENTAGE to 0L,
            
            MAX_QUERY_TIME_MS to 5000L,
            MAX_CACHE_SIZE_MB to 50L,
            ENABLE_CRASH_REPORTING to true,
            
            FILTER_PANEL_ANIMATION_DURATION to 300L,
            CARD_ANIMATION_DELAY to 100L,
            PAGINATION_PAGE_SIZE to 20L
        )
    }

    init {
        setupRemoteConfig()
    }

    private fun setupRemoteConfig() {
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = if (com.tfkcolin.josyandchris.BuildConfig.DEBUG) {
                0 // Allow immediate fetch in debug mode
            } else {
                12.hours.inWholeSeconds // 12 hours in production
            }
        }
        
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(DEFAULT_VALUES)
    }

    /**
     * Fetch and activate remote config values
     */
    suspend fun fetchAndActivate(): Boolean {
        return try {
            remoteConfig.fetch()
            val activateResult = remoteConfig.activate().result
            
            Timber.d("Remote config fetch successful, activation: $activateResult")
            activateResult
        } catch (e: Exception) {
            Timber.e(e, "Failed to fetch remote config")
            false
        }
    }

    /**
     * Check if advanced filters feature is enabled for this user
     */
    fun isAdvancedFiltersEnabled(userId: String): Boolean {
        val featureEnabled = remoteConfig.getBoolean(ENABLE_ADVANCED_FILTERS)
        val rolloutPercentage = remoteConfig.getLong(ADVANCED_FILTERS_ROLLOUT_PERCENTAGE)
        
        return featureEnabled && isUserInRollout(userId, rolloutPercentage)
    }

    /**
     * Check if modern command cards feature is enabled for this user
     */
    fun isModernCommandCardsEnabled(userId: String): Boolean {
        val featureEnabled = remoteConfig.getBoolean(ENABLE_MODERN_COMMAND_CARDS)
        val rolloutPercentage = remoteConfig.getLong(MODERN_CARDS_ROLLOUT_PERCENTAGE)
        
        return featureEnabled && isUserInRollout(userId, rolloutPercentage)
    }

    /**
     * Check if optimized pagination is enabled for this user
     */
    fun isOptimizedPaginationEnabled(userId: String): Boolean {
        val featureEnabled = remoteConfig.getBoolean(ENABLE_OPTIMIZED_PAGINATION)
        val rolloutPercentage = remoteConfig.getLong(OPTIMIZED_PAGINATION_ROLLOUT_PERCENTAGE)
        
        return featureEnabled && isUserInRollout(userId, rolloutPercentage)
    }

    /**
     * Check if cache layer is enabled
     */
    fun isCacheLayerEnabled(): Boolean {
        return remoteConfig.getBoolean(ENABLE_CACHE_LAYER)
    }

    /**
     * Check if performance monitoring is enabled
     */
    fun isPerformanceMonitoringEnabled(): Boolean {
        return remoteConfig.getBoolean(ENABLE_PERFORMANCE_MONITORING)
    }

    /**
     * Get maximum query time in milliseconds
     */
    fun getMaxQueryTimeMs(): Long {
        return remoteConfig.getLong(MAX_QUERY_TIME_MS)
    }

    /**
     * Get maximum cache size in MB
     */
    fun getMaxCacheSizeMb(): Long {
        return remoteConfig.getLong(MAX_CACHE_SIZE_MB)
    }

    /**
     * Check if crash reporting is enabled
     */
    fun isCrashReportingEnabled(): Boolean {
        return remoteConfig.getBoolean(ENABLE_CRASH_REPORTING)
    }

    /**
     * Get filter panel animation duration in milliseconds
     */
    fun getFilterPanelAnimationDuration(): Long {
        return remoteConfig.getLong(FILTER_PANEL_ANIMATION_DURATION)
    }

    /**
     * Get card animation delay in milliseconds
     */
    fun getCardAnimationDelay(): Long {
        return remoteConfig.getLong(CARD_ANIMATION_DELAY)
    }

    /**
     * Get pagination page size
     */
    fun getPaginationPageSize(): Int {
        return remoteConfig.getLong(PAGINATION_PAGE_SIZE).toInt()
    }

    /**
     * Get all current config values (for debugging)
     */
    fun getAllConfigValues(): Map<String, Any> {
        return remoteConfig.all.mapValues { (_, configValue) ->
            when (configValue.source) {
                com.google.firebase.remoteconfig.FirebaseRemoteConfig.VALUE_SOURCE_REMOTE -> {
                    "REMOTE: ${configValue.asString()}"
                }
                com.google.firebase.remoteconfig.FirebaseRemoteConfig.VALUE_SOURCE_DEFAULT -> {
                    "DEFAULT: ${configValue.asString()}"
                }
                else -> {
                    "STATIC: ${configValue.asString()}"
                }
            }
        }
    }

    /**
     * Determine if a user should be included in a rollout based on percentage
     * Uses consistent hashing to ensure same user gets same result
     */
    private fun isUserInRollout(userId: String, rolloutPercentage: Long): Boolean {
        if (rolloutPercentage >= 100) return true
        if (rolloutPercentage <= 0) return false
        
        // Use consistent hashing to determine rollout inclusion
        val hash = userId.hashCode()
        val normalizedHash = kotlin.math.abs(hash % 100)
        
        return normalizedHash < rolloutPercentage
    }

    /**
     * Force refresh config (mainly for debugging/testing)
     */
    suspend fun forceRefresh(): Boolean {
        return try {
            remoteConfig.reset()
            setupRemoteConfig()
            fetchAndActivate()
        } catch (e: Exception) {
            Timber.e(e, "Failed to force refresh remote config")
            false
        }
    }

    /**
     * Get feature rollout status for debugging
     */
    fun getFeatureRolloutStatus(userId: String): Map<String, Boolean> {
        return mapOf(
            "advanced_filters" to isAdvancedFiltersEnabled(userId),
            "modern_command_cards" to isModernCommandCardsEnabled(userId),
            "optimized_pagination" to isOptimizedPaginationEnabled(userId),
            "cache_layer" to isCacheLayerEnabled(),
            "performance_monitoring" to isPerformanceMonitoringEnabled()
        )
    }

    /**
     * Check if we're in a testing environment
     */
    fun isTestingMode(): Boolean {
        return remoteConfig.getBoolean("enable_testing_mode")
    }

    /**
     * Get config value by key with type safety
     */
    fun <T> getConfigValue(key: String, clazz: Class<T>): T? {
        return try {
            when (clazz) {
                Boolean::class.java -> remoteConfig.getBoolean(key) as T
                Long::class.java -> remoteConfig.getLong(key) as T
                String::class.java -> remoteConfig.getString(key) as T
                Double::class.java -> remoteConfig.getDouble(key) as T
                else -> null
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get config value for key: $key")
            null
        }
    }
}
