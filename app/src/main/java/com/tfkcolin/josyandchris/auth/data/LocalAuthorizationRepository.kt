package com.tfkcolin.josyandchris.auth.data

import android.content.Context
import com.tfkcolin.josyandchris.auth.data.local.AuthRoleDataStore
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.auth.domain.model.UserPermissions
import com.tfkcolin.josyandchris.auth.domain.model.hasPermission
import com.tfkcolin.josyandchris.auth.domain.repository.AuthorizationRepository
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Local implementation of the AuthorizationRepository that stores data securely using DataStore
 */
@Singleton
class LocalAuthorizationRepository @Inject constructor(
    private val context: Context,
    @Named("AUTH_ROLES") private val authRoleDataStore: AuthRoleDataStore
) : AuthorizationRepository {

    override suspend fun fetchUserRole(userId: String): AuthResult<UserRole> {
        return try {
            // Try to migrate from SharedPreferences first
            authRoleDataStore.migrateFromSharedPreferences(userId)
            
            val cachedRole = getCachedUserRole(userId)
            if (cachedRole != null) {
                AuthResult.Success(cachedRole)
            } else {
                // For now, default to EMPLOYEE role if no role is cached
                val defaultRole = UserRole.EMPLOYEE
                saveUserRole(userId, defaultRole)
                AuthResult.Success(defaultRole)
            }
        } catch (e: Exception) {
            AuthResult.Error(e)
        }
    }

    override suspend fun saveUserRole(userId: String, role: UserRole) {
        authRoleDataStore.saveUserRole(userId, role)
    }

    override suspend fun getCachedUserRole(userId: String): UserRole? {
        return authRoleDataStore.getUserRole(userId)
    }

    override suspend fun clearCachedUserRole(userId: String) {
        authRoleDataStore.clearUserRole(userId)
    }

    override suspend fun getUserPermissions(userId: String): UserPermissions? {
        val role = getCachedUserRole(userId)
        return role?.let { UserPermissions.fromRole(it) }
    }

    override suspend fun hasPermission(userId: String, permission: String): Boolean {
        val permissions = getUserPermissions(userId)
        return permissions?.hasPermission(permission) ?: false
    }

}
