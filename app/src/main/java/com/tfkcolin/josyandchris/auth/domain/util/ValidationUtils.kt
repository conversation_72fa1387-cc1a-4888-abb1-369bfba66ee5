package com.tfkcolin.josyandchris.auth.domain.util

import android.util.Patterns

/**
 * Utility class for common validation operations used in authentication
 */
object ValidationUtils {
    
    /**
     * Validates if the email format is correct
     */
    fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {
        return email.isNotBlank() && Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
    
    /**
     * Validates if the password meets minimum requirements
     */
    fun isValidPassword(password: String): <PERSON><PERSON><PERSON> {
        return password.length >= 6
    }
    
    /**
     * Validates if the password is strong (contains letters, numbers, and special characters)
     */
    fun isStrongPassword(password: String): <PERSON><PERSON><PERSON> {
        val hasLetter = password.any { it.isLetter() }
        val hasDigit = password.any { it.isDigit() }
        val hasSpecial = password.any { !it.isLetterOrDigit() }
        return password.length >= 8 && hasLetter && hasDigit && hasSpecial
    }
    
    /**
     * Gets password strength score (0-4)
     * 0: Very weak
     * 1: Weak
     * 2: Fair
     * 3: Good
     * 4: Strong
     */
    fun getPasswordStrength(password: String): Int {
        var score = 0
        
        if (password.length >= 6) score++
        if (password.length >= 8) score++
        if (password.any { it.isLetter() }) score++
        if (password.any { it.isDigit() }) score++
        if (password.any { !it.isLetterOrDigit() }) score++
        
        return minOf(score, 4)
    }
    
    /**
     * Validates and returns error message if validation fails
     */
    fun validateEmailAndPassword(email: String, password: String): String? {
        return when {
            email.isBlank() -> "Email cannot be empty"
            !isValidEmail(email) -> "Invalid email format"
            password.isBlank() -> "Password cannot be empty"
            !isValidPassword(password) -> "Password must be at least 6 characters"
            else -> null
        }
    }
}
