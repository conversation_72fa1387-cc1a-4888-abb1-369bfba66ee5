package com.tfkcolin.josyandchris.auth.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore-based implementation for managing authentication preferences.
 * Uses Jetpack DataStore (Preferences) for secure and efficient data storage.
 */
@Singleton
class AuthPreferencesDataStore @Inject constructor(
    private val context: Context
) {

    companion object {
        // DataStore instance name
        private const val AUTH_PREFERENCES_NAME = "auth_preferences"
        
        // Preference keys
        val KEY_SAVED_EMAIL = stringPreferencesKey("saved_email")
        val KEY_REMEMBER_ME = booleanPreferencesKey("remember_me")
        val KEY_BIOMETRIC_ENABLED = booleanPreferencesKey("biometric_enabled")
        
        // DataStore instance
        private val Context.authDataStore: DataStore<Preferences> by preferencesDataStore(
            name = AUTH_PREFERENCES_NAME
        )
    }

    private val dataStore: DataStore<Preferences> = context.authDataStore

    /**
     * Saves the user's email address
     */
    suspend fun saveEmail(email: String) {
        dataStore.edit { preferences ->
            preferences[KEY_SAVED_EMAIL] = email
        }
    }

    /**
     * Gets the saved email address
     */
    suspend fun getSavedEmail(): String {
        return dataStore.data.first()[KEY_SAVED_EMAIL] ?: ""
    }

    /**
     * Observes the saved email address as a Flow
     */
    fun getSavedEmailFlow(): Flow<String> {
        return dataStore.data.map { preferences ->
            preferences[KEY_SAVED_EMAIL] ?: ""
        }
    }

    /**
     * Sets the remember me preference
     */
    suspend fun setRememberMe(remember: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_REMEMBER_ME] = remember
        }
    }

    /**
     * Gets the remember me preference
     */
    suspend fun getRememberMe(): Boolean {
        return dataStore.data.first()[KEY_REMEMBER_ME] ?: false
    }

    /**
     * Observes the remember me preference as a Flow
     */
    fun getRememberMeFlow(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[KEY_REMEMBER_ME] ?: false
        }
    }

    /**
     * Saves both email and remember me preference together
     */
    suspend fun saveCredentials(email: String, rememberMe: Boolean) {
        dataStore.edit { preferences ->
            if (rememberMe) {
                preferences[KEY_SAVED_EMAIL] = email
            } else {
                preferences.remove(KEY_SAVED_EMAIL)
            }
            preferences[KEY_REMEMBER_ME] = rememberMe
        }
    }

    /**
     * Gets both saved email and remember me preference
     */
    suspend fun getSavedCredentials(): Pair<String, Boolean> {
        val preferences = dataStore.data.first()
        val email = preferences[KEY_SAVED_EMAIL] ?: ""
        val rememberMe = preferences[KEY_REMEMBER_ME] ?: false
        return Pair(email, rememberMe)
    }

    /**
     * Clears all saved credentials
     */
    suspend fun clearSavedCredentials() {
        dataStore.edit { preferences ->
            preferences.remove(KEY_SAVED_EMAIL)
            preferences.remove(KEY_REMEMBER_ME)
        }
    }

    /**
     * Sets the biometric authentication preference
     */
    suspend fun setBiometricEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_BIOMETRIC_ENABLED] = enabled
        }
    }

    /**
     * Gets the biometric authentication preference
     */
    suspend fun getBiometricEnabled(): Boolean {
        return dataStore.data.first()[KEY_BIOMETRIC_ENABLED] ?: false
    }

    /**
     * Observes the biometric authentication preference as a Flow
     */
    fun getBiometricEnabledFlow(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[KEY_BIOMETRIC_ENABLED] ?: false
        }
    }

    /**
     * Clears all authentication preferences
     */
    suspend fun clearAllPreferences() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * Migrates data from SharedPreferences to DataStore
     */
    suspend fun migrateFromSharedPreferences() {
        try {
            // Try to read from old SharedPreferences
            val oldPrefs = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
            
            // Migrate saved email
            val oldEmail = oldPrefs.getString("saved_email", null)
            if (oldEmail != null && getSavedEmail().isEmpty()) {
                saveEmail(oldEmail)
            }
            
            // Migrate remember me preference
            val oldRememberMe = oldPrefs.getBoolean("remember_credentials", false)
            if (oldRememberMe && !getRememberMe()) {
                setRememberMe(oldRememberMe)
            }
            
            // Migrate biometric preference
            val oldBiometric = oldPrefs.getBoolean("biometric_enabled", false)
            if (oldBiometric && !getBiometricEnabled()) {
                setBiometricEnabled(oldBiometric)
            }
            
            // Clear old preferences after successful migration
            if (oldEmail != null || oldRememberMe || oldBiometric) {
                oldPrefs.edit()
                    .remove("saved_email")
                    .remove("remember_credentials")
                    .remove("biometric_enabled")
                    .apply()
            }
        } catch (e: Exception) {
            // Migration failed, but don't crash the app
            // Log the error in production
        }
    }
}
