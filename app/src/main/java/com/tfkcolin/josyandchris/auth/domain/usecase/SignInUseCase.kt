package com.tfkcolin.josyandchris.auth.domain.usecase

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for signing in with email and password
 */
class SignInUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String, password: String): AuthResult<AuthUser> {
        Timber.d("SignInUseCase: Validating sign in input")
        
        // Validate input
        if (email.isBlank()) {
            Timber.w("SignInUseCase: Email is blank")
            return AuthResult.Error(IllegalArgumentException("Email cannot be empty"))
        }
        if (password.isBlank()) {
            Timber.w("SignInUseCase: Password is blank")
            return AuthResult.Error(IllegalArgumentException("Password cannot be empty"))
        }
        if (!isValidEmail(email)) {
            Timber.w("SignInUseCase: Invalid email format")
            return AuthResult.Error(IllegalArgumentException("Invalid email format"))
        }
        if (password.length < 6) {
            Timber.w("SignInUseCase: Password too short")
            return AuthResult.Error(IllegalArgumentException("Password must be at least 6 characters"))
        }
        
        Timber.d("SignInUseCase: Input validation passed, proceeding with sign in")
        return authRepository.signInWithEmailAndPassword(email.trim(), password)
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
