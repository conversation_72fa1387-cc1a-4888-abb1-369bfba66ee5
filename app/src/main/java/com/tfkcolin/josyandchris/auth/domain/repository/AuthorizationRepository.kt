package com.tfkcolin.josyandchris.auth.domain.repository

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.auth.domain.model.UserPermissions

/**
 * Repository interface for authorization operations
 */
interface AuthorizationRepository {
    
    /**
     * Fetches user role from the server
     */
    suspend fun fetchUserRole(userId: String): AuthResult<UserRole>
    
    /**
     * Saves user role locally
     */
    suspend fun saveUserRole(userId: String, role: UserRole)
    
    /**
     * Gets cached user role
     */
    suspend fun getCachedUserRole(userId: String): UserRole?
    
    /**
     * Clears cached user role
     */
    suspend fun clearCachedUserRole(userId: String)
    
    /**
     * Gets user permissions based on role
     */
    suspend fun getUserPermissions(userId: String): UserPermissions?
    
    /**
     * Checks if user has specific permission
     */
    suspend fun hasPermission(userId: String, permission: String): <PERSON><PERSON><PERSON>
}
