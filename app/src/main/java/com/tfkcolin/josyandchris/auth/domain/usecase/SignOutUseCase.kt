package com.tfkcolin.josyandchris.auth.domain.usecase

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for signing out the current user
 */
class SignOutUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(): AuthResult<Unit> {
        Timber.d("SignOutUseCase: Signing out user")
        return authRepository.signOut()
    }
}
