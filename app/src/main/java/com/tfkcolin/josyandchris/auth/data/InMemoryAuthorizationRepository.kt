package com.tfkcolin.josyandchris.auth.data

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.auth.domain.model.UserPermissions
import com.tfkcolin.josyandchris.auth.domain.model.hasPermission
import com.tfkcolin.josyandchris.auth.domain.repository.AuthorizationRepository

/**
 * Implementation of the AuthorizationRepository interface
 */
object InMemoryAuthorizationRepository : AuthorizationRepository {

    private val roleCache = mutableMapOf<String, UserRole>()

    override suspend fun fetchUserRole(userId: String): AuthResult<UserRole> {
        // TODO: Implement server-side fetching
        return AuthResult.Error(Exception("Method not implemented"))
    }

    override suspend fun saveUserRole(userId: String, role: UserRole) {
        roleCache[userId] = role
    }

    override suspend fun getCachedUserRole(userId: String): UserRole? {
        return roleCache[userId]
    }

    override suspend fun clearCachedUserRole(userId: String) {
        roleCache.remove(userId)
    }

    override suspend fun getUserPermissions(userId: String): UserPermissions? {
        return roleCache[userId]?.let { UserPermissions.fromRole(it) }
    }

    override suspend fun hasPermission(userId: String, permission: String): Boolean {
        return getUserPermissions(userId)?.hasPermission(permission) ?: false
    }
}
