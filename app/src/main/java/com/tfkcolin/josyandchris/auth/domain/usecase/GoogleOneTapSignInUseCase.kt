package com.tfkcolin.josyandchris.auth.domain.usecase

import android.content.Intent
import android.content.IntentSender
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for Google One Tap Sign In
 */
class GoogleOneTapSignInUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend fun beginSignIn(): AuthResult<IntentSender> {
        return try {
            Timber.d("GoogleOneTapSignInUseCase: Beginning Google One Tap sign in")
            authRepository.beginGoogleOneTapSignIn()
        } catch (e: Exception) {
            Timber.e(e, "GoogleOneTapSignInUseCase: Failed to begin Google One Tap sign in")
            AuthResult.Error(Exception("Failed to begin Google One Tap sign in: ${e.message}", e))
        }
    }
    
    suspend fun signInWithIntent(data: Intent?): AuthResult<AuthUser> {
        return try {
            Timber.d("GoogleOneTapSignInUseCase: Signing in with Google One Tap intent")
            if (data == null) {
                Timber.w("GoogleOneTapSignInUseCase: Intent data is null")
                AuthResult.Error(IllegalArgumentException("Intent data cannot be null"))
            } else {
                authRepository.signInWithGoogleOneTap(data)
            }
        } catch (e: Exception) {
            Timber.e(e, "GoogleOneTapSignInUseCase: Failed to sign in with Google One Tap")
            AuthResult.Error(Exception("Failed to sign in with Google: ${e.message}", e))
        }
    }
}
