package com.tfkcolin.josyandchris.auth.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.tfkcolin.josyandchris.auth.data.FirebaseAuthRepository
import com.tfkcolin.josyandchris.auth.data.LocalAuthorizationRepository
import com.tfkcolin.josyandchris.auth.data.local.AuthPreferencesDataStore
import com.tfkcolin.josyandchris.auth.data.local.AuthRoleDataStore
import com.tfkcolin.josyandchris.data.local.ClientPreferencesDataStore
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import com.tfkcolin.josyandchris.auth.domain.repository.AuthorizationRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

/**
 * Dagger Hilt module for authentication dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AuthModule {
    
    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth {
        return Firebase.auth
    }
    
    @Provides
    @Singleton
    fun provideAuthRepository(
        @ApplicationContext context: Context,
        firebaseAuth: FirebaseAuth,
        @Named("AUTH_PREFS") authPreferencesDataStore: AuthPreferencesDataStore
    ): AuthRepository {
        return FirebaseAuthRepository(context, firebaseAuth, authPreferencesDataStore)
    }
    
    
    @Provides
    @Singleton
    fun provideAuthorizationRepository(
        @ApplicationContext context: Context,
        @Named("AUTH_ROLES") authRoleDataStore: AuthRoleDataStore
    ): AuthorizationRepository {
        return LocalAuthorizationRepository(context, authRoleDataStore)
    }

    @Provides
    @Singleton
    @Named("AUTH_PREFS")
    fun provideAuthPreferencesDataStore(
        @ApplicationContext context: Context
    ): AuthPreferencesDataStore {
        return AuthPreferencesDataStore(context)
    }

    @Provides
    @Singleton
    @Named("AUTH_ROLES")
    fun provideAuthRoleDataStore(
        @ApplicationContext context: Context
    ): AuthRoleDataStore {
        return AuthRoleDataStore(context)
    }
    
    @Provides
    @Singleton
    fun provideClientPreferencesDataStore(
        @ApplicationContext context: Context
    ): ClientPreferencesDataStore {
        return ClientPreferencesDataStore(context)
    }
}
