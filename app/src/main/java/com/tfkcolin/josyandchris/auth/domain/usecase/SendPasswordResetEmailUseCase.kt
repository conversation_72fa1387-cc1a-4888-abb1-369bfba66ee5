package com.tfkcolin.josyandchris.auth.domain.usecase

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for sending a password reset email
 */
class SendPasswordResetEmailUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String): AuthResult<Unit> {
        Timber.d("SendPasswordResetEmailUseCase: Validating password reset email input")
        
        // Validate input
        if (email.isBlank()) {
            Timber.w("SendPasswordResetEmailUseCase: Email is blank")
            return AuthResult.Error(IllegalArgumentException("Email cannot be empty"))
        }
        if (!isValidEmail(email)) {
            Timber.w("SendPasswordResetEmailUseCase: Invalid email format")
            return AuthResult.Error(IllegalArgumentException("Invalid email format"))
        }
        
        Timber.d("SendPasswordResetEmailUseCase: Input validation passed, proceeding with password reset email")
        return authRepository.sendPasswordResetEmail(email.trim())
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
