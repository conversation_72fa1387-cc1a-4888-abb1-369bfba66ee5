package com.tfkcolin.josyandchris.auth

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.auth.domain.model.UserPermissions
import com.tfkcolin.josyandchris.auth.domain.model.hasPermission
import com.tfkcolin.josyandchris.auth.domain.repository.AuthorizationRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Authorization Manager - Single source of truth for all authorization operations
 */
@Singleton
class AuthorizationManager @Inject constructor(
    private val authorizationRepository: AuthorizationRepository
) {
    
    private val _currentUserRole = MutableStateFlow<UserRole?>(null)
    val currentUserRole: StateFlow<UserRole?> = _currentUserRole.asStateFlow()
    
    private val _currentUserPermissions = MutableStateFlow<UserPermissions?>(null)
    val currentUserPermissions: StateFlow<UserPermissions?> = _currentUserPermissions.asStateFlow()
    
    /**
     * Load user role and permissions
     */
    suspend fun loadUserRole(userId: String): AuthResult<UserRole> {
        return when (val result = authorizationRepository.fetchUserRole(userId)) {
            is AuthResult.Success -> {
                _currentUserRole.value = result.data
                _currentUserPermissions.value = UserPermissions.fromRole(result.data)
                result
            }
            is AuthResult.Error -> result
            else -> AuthResult.Error(Exception("Unknown error"))
        }
    }
    
    /**
     * Set user role manually
     */
    suspend fun setUserRole(userId: String, role: UserRole) {
        authorizationRepository.saveUserRole(userId, role)
        _currentUserRole.value = role
        _currentUserPermissions.value = UserPermissions.fromRole(role)
    }
    
    /**
     * Get current user role
     */
    fun getCurrentUserRole(): UserRole? = _currentUserRole.value
    
    /**
     * Get current user permissions
     */
    fun getCurrentUserPermissions(): UserPermissions? = _currentUserPermissions.value
    
    /**
     * Check if current user has permission
     */
    fun hasPermission(permission: String): Boolean {
        return _currentUserPermissions.value?.hasPermission(permission) ?: false
    }
    
    /**
     * Check if current user is admin
     */
    fun isAdmin(): Boolean = _currentUserRole.value == UserRole.ADMIN
    
    /**
     * Check if current user is employee
     */
    fun isEmployee(): Boolean = _currentUserRole.value == UserRole.EMPLOYEE
    
    /**
     * Check if current user is deliverer
     */
    fun isDeliverer(): Boolean = _currentUserRole.value == UserRole.DELIVERER
    
    /**
     * Check if current user can create commands
     */
    fun canCreateCommands(): Boolean = hasPermission("CREATE_COMMANDS")
    
    /**
     * Check if current user can manage transactions
     */
    fun canManageTransactions(): Boolean = hasPermission("MANAGE_TRANSACTIONS")
    
    /**
     * Check if current user can manage countries
     */
    fun canManageCountries(): Boolean = hasPermission("MANAGE_COUNTRIES")
    
    /**
     * Check if current user can manage cargos
     */
    fun canManageCargos(): Boolean = hasPermission("MANAGE_CARGOS")
    
    /**
     * Clear user role (for logout)
     */
    suspend fun clearUserRole(userId: String) {
        authorizationRepository.clearCachedUserRole(userId)
        _currentUserRole.value = null
        _currentUserPermissions.value = null
    }
}
