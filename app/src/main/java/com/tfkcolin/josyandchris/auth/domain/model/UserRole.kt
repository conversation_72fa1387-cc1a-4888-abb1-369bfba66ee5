package com.tfkcolin.josyandchris.auth.domain.model

/**
 * Enum class representing different user roles in the application
 */
enum class UserRole(val role: String) {
    ADMIN("admin"),
    EMPLOYEE("employee"),
    DELIVERER("deliverer");

    companion object {
        /**
         * Convert string role to UserRole enum
         */
        fun fromString(role: String?): UserRole? {
            return entries.find { it.role.equals(role, ignoreCase = true) }
        }
    }
}

/**
 * Data class representing user permissions based on role
 */
data class UserPermissions(
    val canCreateCommands: <PERSON><PERSON>an,
    val canDeleteCommands: <PERSON><PERSON><PERSON>,
    val canEditCommands: <PERSON><PERSON><PERSON>,
    val canViewCommands: <PERSON><PERSON><PERSON>,
    val canManageTransactions: <PERSON><PERSON>an,
    val canManageCountries: <PERSON><PERSON>an,
    val canManageCargos: Boolean,
    val canManageShipments: Boolean,
    val canViewReports: <PERSON><PERSON><PERSON>,
    val canManageUsers: Boolean
) {
    companion object {
        /**
         * Get permissions based on user role
         */
        fun fromRole(role: UserRole): UserPermissions {
            return when (role) {
                UserRole.ADMIN -> UserPermissions(
                    canCreateCommands = true,
                    canDeleteCommands = true,
                    canEditCommands = true,
                    canViewCommands = true,
                    canManageTransactions = true,
                    canManageCountries = true,
                    canManageCargos = true,
                    canManageShipments = true,
                    canViewReports = true,
                    canManageUsers = true
                )
                UserRole.EMPLOYEE -> UserPermissions(
                    canCreateCommands = true,
                    canDeleteCommands = false,
                    canEditCommands = true,
                    canViewCommands = true,
                    canManageTransactions = false,
                    canManageCountries = false,
                    canManageCargos = true,
                    canManageShipments = true,
                    canViewReports = false,
                    canManageUsers = false
                )
                UserRole.DELIVERER -> UserPermissions(
                    canCreateCommands = false,
                    canDeleteCommands = false,
                    canEditCommands = false,
                    canViewCommands = true,
                    canManageTransactions = false,
                    canManageCountries = false,
                    canManageCargos = false,
                    canManageShipments = true,
                    canViewReports = false,
                    canManageUsers = false
                )
            }
        }
    }
}

/**
 * Extension function to check if user has permission
 */
fun UserPermissions.hasPermission(permission: String): Boolean {
    return when (permission) {
        "CREATE_COMMANDS" -> canCreateCommands
        "DELETE_COMMANDS" -> canDeleteCommands
        "EDIT_COMMANDS" -> canEditCommands
        "VIEW_COMMANDS" -> canViewCommands
        "MANAGE_TRANSACTIONS" -> canManageTransactions
        "MANAGE_COUNTRIES" -> canManageCountries
        "MANAGE_CARGOS" -> canManageCargos
        "MANAGE_SHIPMENTS" -> canManageShipments
        "VIEW_REPORTS" -> canViewReports
        "MANAGE_USERS" -> canManageUsers
        else -> false
    }
}
