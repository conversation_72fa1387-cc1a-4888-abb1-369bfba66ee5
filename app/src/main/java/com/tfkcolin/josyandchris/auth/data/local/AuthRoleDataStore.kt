package com.tfkcolin.josyandchris.auth.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore-based implementation for managing user roles.
 * Uses Jetpack DataStore (Preferences) for secure and efficient role storage.
 */
@Singleton
class AuthRoleDataStore @Inject constructor(
    private val context: Context
) {

    companion object {
        // DataStore instance name
        private const val ROLE_PREFERENCES_NAME = "auth_roles"
        
        // Preference key for user roles (using user ID as suffix)
        private fun getUserRoleKey(userId: String) = stringPreferencesKey("user_role_$userId")
        
        // DataStore instance
        private val Context.roleDataStore: DataStore<Preferences> by preferencesDataStore(
            name = ROLE_PREFERENCES_NAME
        )
    }

    private val dataStore: DataStore<Preferences> = context.roleDataStore

    /**
     * Saves the user's role
     */
    suspend fun saveUserRole(userId: String, role: UserRole) {
        dataStore.edit { preferences ->
            preferences[getUserRoleKey(userId)] = role.role
        }
    }

    /**
     * Gets the user's role
     */
    suspend fun getUserRole(userId: String): UserRole? {
        val roleString = dataStore.data.first()[getUserRoleKey(userId)]
        return UserRole.fromString(roleString)
    }

    /**
     * Observes the user's role as a Flow
     */
    fun getUserRoleFlow(userId: String): Flow<UserRole?> {
        return dataStore.data.map { preferences ->
            val roleString = preferences[getUserRoleKey(userId)]
            UserRole.fromString(roleString)
        }
    }

    /**
     * Clears the user's role
     */
    suspend fun clearUserRole(userId: String) {
        dataStore.edit { preferences ->
            preferences.remove(getUserRoleKey(userId))
        }
    }

    /**
     * Clears all user roles
     */
    suspend fun clearAllRoles() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * Migrates data from SharedPreferences to DataStore
     */
    suspend fun migrateFromSharedPreferences(userId: String) {
        try {
            // Try to read from old SharedPreferences
            val oldPrefs = context.getSharedPreferences("auth_roles_prefs", Context.MODE_PRIVATE)
            val oldRoleString = oldPrefs.getString("user_role_$userId", null)
            
            if (oldRoleString != null) {
                // Save to DataStore
                val role = UserRole.fromString(oldRoleString)
                if (role != null) {
                    saveUserRole(userId, role)
                }
                
                // Clear from old SharedPreferences
                oldPrefs.edit().remove("user_role_$userId").apply()
            }
        } catch (e: Exception) {
            // Migration failed, but don't crash the app
            // Log the error in production
        }
    }
}
