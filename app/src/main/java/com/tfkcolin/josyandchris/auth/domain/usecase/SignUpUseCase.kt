package com.tfkcolin.josyandchris.auth.domain.usecase

import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for signing up with email and password
 */
class SignUpUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String, password: String): AuthResult<AuthUser> {
        Timber.d("SignUpUseCase: Validating sign up input")
        
        // Validate input
        if (email.isBlank()) {
            Timber.w("SignUpUseCase: Email is blank")
            return AuthResult.Error(IllegalArgumentException("Email cannot be empty"))
        }
        if (password.isBlank()) {
            Timber.w("SignUpUseCase: Password is blank")
            return AuthResult.Error(IllegalArgumentException("Password cannot be empty"))
        }
        if (!isValidEmail(email)) {
            Timber.w("SignUpUseCase: Invalid email format")
            return AuthResult.Error(IllegalArgumentException("Invalid email format"))
        }
        if (password.length < 6) {
            Timber.w("SignUpUseCase: Password too short")
            return AuthResult.Error(IllegalArgumentException("Password must be at least 6 characters"))
        }
        
        Timber.d("SignUpUseCase: Input validation passed, proceeding with sign up")
        return authRepository.signUpWithEmailAndPassword(email.trim(), password)
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
