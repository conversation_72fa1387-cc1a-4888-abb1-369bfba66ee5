package com.tfkcolin.josyandchris.auth.domain.model

import com.tfkcolin.josyandchris.auth.domain.util.AuthErrorMapper

/**
 * Result wrapper for authentication operations
 */
sealed class AuthResult<out T> {
    data class Success<T>(val data: T) : AuthResult<T>()
    data class Error(
        val exception: Exception,
        val message: String = AuthErrorMapper.mapToUserFriendlyMessage(exception)
    ) : AuthResult<Nothing>()
    object Loading : AuthResult<Nothing>()
}

/**
 * Extension function to check if result is successful
 */
fun <T> AuthResult<T>.isSuccess(): Boolean = this is AuthResult.Success

/**
 * Extension function to get data from successful result
 */
fun <T> AuthResult<T>.getDataOrNull(): T? = if (this is AuthResult.Success) data else null

/**
 * Extension function to get error from failed result
 */
fun <T> AuthResult<T>.getErrorOrNull(): Exception? = if (this is AuthResult.Error) exception else null

/**
 * Extension function to get user-friendly error message from failed result
 */
fun <T> AuthResult<T>.getErrorMessageOrNull(): String? = if (this is AuthResult.Error) message else null
