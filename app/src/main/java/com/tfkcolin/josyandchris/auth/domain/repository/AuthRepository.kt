package com.tfkcolin.josyandchris.auth.domain.repository

import android.content.Intent
import android.content.IntentSender
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthState
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for authentication operations
 * This serves as the contract between the domain and data layers
 */
interface AuthRepository {
    
    /**
     * Observes the current authentication state
     */
    val authState: Flow<AuthState>
    
    /**
     * Gets the current authenticated user
     */
    val currentUser: AuthUser?
    
    /**
     * Signs in with email and password
     */
    suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser>
    
    /**
     * Signs up with email and password
     */
    suspend fun signUpWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser>
    
    /**
     * Signs out the current user
     */
    suspend fun signOut(): AuthResult<Unit>
    
    /**
     * Sends password reset email
     */
    suspend fun sendPasswordResetEmail(email: String): AuthResult<Unit>
    
    /**
     * Signs in anonymously
     */
    suspend fun signInAnonymously(): AuthResult<AuthUser>
    
    /**
     * Initiates Google One Tap sign in flow
     */
    suspend fun beginGoogleOneTapSignIn(): AuthResult<IntentSender>
    
    /**
     * Completes Google One Tap sign in with the result intent
     */
    suspend fun signInWithGoogleOneTap(data: Intent?): AuthResult<AuthUser>
    
    /**
     * Checks if user credentials should be remembered
     */
    suspend fun shouldRememberCredentials(): Boolean
    
    /**
     * Saves user credentials for future use
     */
    suspend fun saveCredentials(email: String, rememberMe: Boolean)
    
    /**
     * Gets saved user credentials
     */
    suspend fun getSavedCredentials(): Pair<String, Boolean>
    
    /**
     * Clears saved credentials
     */
    suspend fun clearSavedCredentials()
    
    /**
     * Checks if biometric authentication is available
     */
    suspend fun isBiometricAvailable(): Boolean
    
    /**
     * Checks if biometric authentication is enabled for the user
     */
    suspend fun isBiometricEnabled(): Boolean
    
    /**
     * Enables or disables biometric authentication
     */
    suspend fun setBiometricEnabled(enabled: Boolean)
}
