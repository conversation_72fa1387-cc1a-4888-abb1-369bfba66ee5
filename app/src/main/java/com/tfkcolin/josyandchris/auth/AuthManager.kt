package com.tfkcolin.josyandchris.auth

import android.content.Intent
import android.content.IntentSender
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthState
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import com.tfkcolin.josyandchris.auth.domain.usecase.*
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Main Authentication Manager - Single source of truth for all authentication operations
 * This is the primary interface that UI and business layers should interact with
 */
@Singleton
class AuthManager @Inject constructor(
    private val authRepository: AuthRepository,
    private val signInUseCase: SignInUseCase,
    private val signUpUseCase: SignUpUseCase,
    private val signOutUseCase: SignOutUseCase,
    private val sendPasswordResetEmailUseCase: SendPasswordResetEmailUseCase,
    private val googleOneTapSignInUseCase: GoogleOneTapSignInUseCase
) {
    
    /**
     * Observes the current authentication state
     */
    val authState: Flow<AuthState> = authRepository.authState
    
    /**
     * Gets the current authenticated user
     */
    val currentUser: AuthUser?
        get() = authRepository.currentUser
    
    /**
     * Checks if user is currently authenticated
     */
    val isAuthenticated: Boolean
        get() = currentUser != null
    
    /**
     * Signs in with email and password
     */
    suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser> {
        return signInUseCase(email, password)
    }
    
    /**
     * Signs up with email and password
     */
    suspend fun signUpWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser> {
        return signUpUseCase(email, password)
    }
    
    /**
     * Signs out the current user
     */
    suspend fun signOut(): AuthResult<Unit> {
        return signOutUseCase()
    }
    
    /**
     * Sends password reset email
     */
    suspend fun sendPasswordResetEmail(email: String): AuthResult<Unit> {
        return sendPasswordResetEmailUseCase(email)
    }
    
    /**
     * Signs in anonymously
     */
    suspend fun signInAnonymously(): AuthResult<AuthUser> {
        return authRepository.signInAnonymously()
    }
    
    /**
     * Initiates Google One Tap sign in flow
     */
    suspend fun beginGoogleOneTapSignIn(): AuthResult<IntentSender> {
        return googleOneTapSignInUseCase.beginSignIn()
    }
    
    /**
     * Completes Google One Tap sign in with result intent
     */
    suspend fun signInWithGoogleOneTap(data: Intent?): AuthResult<AuthUser> {
        return googleOneTapSignInUseCase.signInWithIntent(data)
    }
    
    /**
     * Saves user credentials for future use
     */
    suspend fun saveCredentials(email: String, rememberMe: Boolean) {
        authRepository.saveCredentials(email, rememberMe)
    }
    
    /**
     * Gets saved user credentials
     */
    suspend fun getSavedCredentials(): Pair<String, Boolean> {
        return authRepository.getSavedCredentials()
    }
    
    /**
     * Clears saved credentials
     */
    suspend fun clearSavedCredentials() {
        authRepository.clearSavedCredentials()
    }
    
    /**
     * Checks if biometric authentication is available
     */
    suspend fun isBiometricAvailable(): Boolean {
        return authRepository.isBiometricAvailable()
    }
    
    /**
     * Checks if biometric authentication is enabled
     */
    suspend fun isBiometricEnabled(): Boolean {
        return authRepository.isBiometricEnabled()
    }
    
    /**
     * Enables or disables biometric authentication
     */
    suspend fun setBiometricEnabled(enabled: Boolean) {
        authRepository.setBiometricEnabled(enabled)
    }
}
