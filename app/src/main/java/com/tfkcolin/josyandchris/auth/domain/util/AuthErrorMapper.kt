package com.tfkcolin.josyandchris.auth.domain.util

import com.google.firebase.auth.FirebaseAuthException
import timber.log.Timber

/**
 * Utility class for mapping Firebase Auth exceptions to user-friendly error messages
 */
object AuthErrorMapper {

    /**
     * Maps Firebase Auth exceptions to user-friendly error messages
     * @param exception The exception to map
     * @return A user-friendly error message
     */
    fun mapToUserFriendlyMessage(exception: Exception): String {
        Timber.d("Mapping auth exception: ${exception.javaClass.simpleName} - ${exception.message}")
        
        return when (exception) {
            is FirebaseAuthException -> mapFirebaseAuthException(exception)
            else -> mapGenericException(exception)
        }
    }

    /**
     * Maps Firebase Auth exceptions to user-friendly messages
     */
    private fun mapFirebaseAuthException(exception: FirebaseAuthException): String {
        val errorCode = exception.errorCode
        Timber.d("Mapping Firebase auth error code: $errorCode")
        
        return when (errorCode) {
            // Authentication errors
            "ERROR_INVALID_CUSTOM_TOKEN" -> "Authentication failed due to invalid custom token"
            "ERROR_CUSTOM_TOKEN_MISMATCH" -> "Authentication failed due to token mismatch"
            "ERROR_INVALID_CREDENTIAL" -> "Invalid email or password. Please check your credentials and try again"
            "ERROR_USER_DISABLED" -> "Your account has been disabled. Please contact support"
            "ERROR_ACCOUNT_EXISTS_WITH_DIFFERENT_CREDENTIAL" -> "An account already exists with the same email but different sign-in credentials"
            
            // Email/Password errors
            "ERROR_EMAIL_ALREADY_IN_USE" -> "This email is already registered. Please use a different email or sign in"
            "ERROR_CREDENTIAL_ALREADY_IN_USE" -> "This credential is already associated with a different user account"
            "ERROR_OPERATION_NOT_ALLOWED" -> "This sign-in method is not allowed. Please contact support"
            "ERROR_WEAK_PASSWORD" -> "Your password is too weak. Please use at least 6 characters with a mix of letters and numbers"
            "ERROR_USER_NOT_FOUND" -> "No account found with this email. Please check your email or sign up"
            "ERROR_WRONG_PASSWORD" -> "Incorrect password. Please check your password and try again"
            "ERROR_INVALID_EMAIL" -> "Invalid email address. Please enter a valid email"
            
            // Account management errors
            "ERROR_USER_MISMATCH" -> "User credentials do not match the current user"
            "ERROR_REQUIRES_RECENT_LOGIN" -> "For security reasons, please sign in again to continue"
            "ERROR_PROVIDER_ALREADY_LINKED" -> "This account is already linked to another provider"
            "ERROR_NO_SUCH_PROVIDER" -> "This provider is not associated with your account"
            "ERROR_INVALID_USER_TOKEN" -> "Your session has expired. Please sign in again"
            "ERROR_USER_TOKEN_EXPIRED" -> "Your session has expired. Please sign in again"
            
            // Network and service errors
            "ERROR_NETWORK_REQUEST_FAILED" -> "Network error. Please check your internet connection and try again"
            "ERROR_TOO_MANY_REQUESTS" -> "Too many requests. Please wait a moment and try again"
            "ERROR_TIMEOUT" -> "Request timed out. Please try again"
            "ERROR_INTERNAL_ERROR" -> "An internal error occurred. Please try again later"
            
            // App verification errors
            "ERROR_APP_NOT_AUTHORIZED" -> "This app is not authorized to use Firebase Authentication"
            "ERROR_KEYCHAIN_ERROR" -> "An error occurred accessing the keychain"
            "ERROR_MISSING_APP_CREDENTIAL" -> "Missing app credential"
            "ERROR_INVALID_APP_CREDENTIAL" -> "Invalid app credential"
            
            // Multi-factor authentication errors
            "ERROR_MULTI_FACTOR_INFO_NOT_FOUND" -> "Multi-factor authentication info not found"
            "ERROR_MULTI_FACTOR_AUTH_REQUIRED" -> "Multi-factor authentication required"
            "ERROR_INVALID_MULTI_FACTOR_SESSION" -> "Invalid multi-factor authentication session"
            
            // Phone authentication errors
            "ERROR_INVALID_PHONE_NUMBER" -> "Invalid phone number format"
            "ERROR_MISSING_PHONE_NUMBER" -> "Phone number is required"
            "ERROR_INVALID_VERIFICATION_CODE" -> "Invalid verification code"
            "ERROR_INVALID_VERIFICATION_ID" -> "Invalid verification ID"
            "ERROR_MISSING_VERIFICATION_CODE" -> "Verification code is required"
            "ERROR_MISSING_VERIFICATION_ID" -> "Verification ID is required"
            "ERROR_QUOTA_EXCEEDED" -> "SMS quota exceeded. Please try again later"
            
            // Session management errors
            "ERROR_SESSION_EXPIRED" -> "Your session has expired. Please sign in again"
            "ERROR_INVALID_API_KEY" -> "Invalid API key"
            "ERROR_APP_NOT_VERIFIED" -> "App not verified"
            
            // Generic or unknown errors
            else -> {
                Timber.w("Unknown Firebase auth error code: $errorCode")
                "Authentication failed: ${exception.message ?: "Unknown error"}"
            }
        }
    }

    /**
     * Maps generic exceptions to user-friendly messages
     */
    private fun mapGenericException(exception: Exception): String {
        Timber.d("Mapping generic exception: ${exception.javaClass.simpleName}")
        
        return when (exception) {
            is IllegalArgumentException -> "Invalid input provided. Please check your information and try again"
            is SecurityException -> "Security error occurred. Please try again"
            is RuntimeException -> "An unexpected error occurred. Please try again"
            else -> exception.message?.takeIf { it.isNotBlank() } ?: "An unknown error occurred. Please try again"
        }
    }

    /**
     * Creates a comprehensive error message with debugging information
     * This should be used for logging purposes only
     */
    fun createDebugErrorMessage(exception: Exception, operation: String): String {
        val userMessage = mapToUserFriendlyMessage(exception)
        val debugInfo = buildString {
            append("Operation: $operation")
            append(", Exception: ${exception.javaClass.simpleName}")
            append(", Message: ${exception.message}")
            if (exception is FirebaseAuthException) {
                append(", Error Code: ${exception.errorCode}")
            }
        }
        
        Timber.e(exception, "Auth operation failed - $debugInfo")
        return userMessage
    }
}
