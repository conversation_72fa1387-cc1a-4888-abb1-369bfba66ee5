package com.tfkcolin.josyandchris.auth.data

import android.content.Context
import android.content.Intent
import android.content.IntentSender
import androidx.biometric.BiometricManager
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import com.google.firebase.auth.GoogleAuthProvider
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.auth.data.local.AuthPreferencesDataStore
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import com.tfkcolin.josyandchris.auth.domain.model.AuthState
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.repository.AuthRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import com.tfkcolin.josyandchris.auth.domain.util.AuthErrorMapper
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

@Singleton
class FirebaseAuthRepository @Inject constructor(
    private val context: Context,
    private val auth: FirebaseAuth,
    @Named("AUTH_PREFS") private val authPreferencesDataStore: AuthPreferencesDataStore
) : AuthRepository {

    private val signInClient: SignInClient = Identity.getSignInClient(context)
    private val _authState = MutableStateFlow<AuthState>(AuthState.Unauthenticated)
    
    private val signInRequest = BeginSignInRequest.builder()
        .setGoogleIdTokenRequestOptions(
            BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                .setSupported(true)
                .setServerClientId(context.getString(R.string.default_web_client_id))
                .setFilterByAuthorizedAccounts(true)
                .build()
        )
        .setAutoSelectEnabled(true)
        .build()
    
    private val signUpRequest = BeginSignInRequest.builder()
        .setGoogleIdTokenRequestOptions(
            BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                .setSupported(true)
                .setServerClientId(context.getString(R.string.default_web_client_id))
                .setFilterByAuthorizedAccounts(false)
                .build()
        )
        .setAutoSelectEnabled(true)
        .build()
    
    init {
        // Initialize auth state based on current user
        auth.addAuthStateListener { firebaseAuth ->
            val user = firebaseAuth.currentUser?.toAuthUser()
            _authState.value = if (user != null) {
                AuthState.Authenticated(user)
            } else {
                AuthState.Unauthenticated
            }
        }
        
        // Trigger migration from SharedPreferences to DataStore
        kotlinx.coroutines.GlobalScope.launch {
            authPreferencesDataStore.migrateFromSharedPreferences()
        }
    }

    override val authState: Flow<AuthState> get() = _authState

    override val currentUser: AuthUser?
        get() = auth.currentUser?.toAuthUser()

    override suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser> {
        return try {
            Timber.d("Attempting to sign in with email: $email")
            val result = auth.signInWithEmailAndPassword(email, password).await()
            val user = result.user?.toAuthUser()
            if (user != null) {
                _authState.value = AuthState.Authenticated(user)
                Timber.d("Successfully signed in user: ${user.id}")
                AuthResult.Success(user)
            } else {
                Timber.e("Sign in failed: User is null")
                AuthResult.Error(Exception("Failed to sign in"))
            }
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Sign in failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun signUpWithEmailAndPassword(email: String, password: String): AuthResult<AuthUser> {
        return try {
            Timber.d("Attempting to sign up with email: $email")
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            val user = result.user?.toAuthUser()
            if (user != null) {
                _authState.value = AuthState.Authenticated(user)
                Timber.d("Successfully signed up user: ${user.id}")
                AuthResult.Success(user)
            } else {
                Timber.e("Sign up failed: User is null")
                AuthResult.Error(Exception("Failed to sign up"))
            }
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Sign up failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun signOut(): AuthResult<Unit> {
        return try {
            Timber.d("Attempting to sign out user")
            auth.signOut()
            _authState.value = AuthState.Unauthenticated
            Timber.d("Successfully signed out user")
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Sign out failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun sendPasswordResetEmail(email: String): AuthResult<Unit> {
        return try {
            Timber.d("Attempting to send password reset email to: $email")
            auth.sendPasswordResetEmail(email).await()
            Timber.d("Successfully sent password reset email")
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Password reset email failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun signInAnonymously(): AuthResult<AuthUser> {
        return try {
            Timber.d("Attempting to sign in anonymously")
            val result = auth.signInAnonymously().await()
            val user = result.user?.toAuthUser()
            if (user != null) {
                _authState.value = AuthState.Authenticated(user)
                Timber.d("Successfully signed in anonymously: ${user.id}")
                AuthResult.Success(user)
            } else {
                Timber.e("Anonymous sign in failed: User is null")
                AuthResult.Error(Exception("Failed to sign in anonymously"))
            }
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Anonymous sign in failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun beginGoogleOneTapSignIn(): AuthResult<IntentSender> {
        return try {
            Timber.d("Attempting to begin Google One Tap sign in")
            val result = signInClient.beginSignIn(signInRequest).await()
            Timber.d("Successfully began Google One Tap sign in")
            AuthResult.Success(result.pendingIntent.intentSender)
        } catch (e: Exception) {
            Timber.d("Google One Tap sign in failed, trying sign up flow")
            try {
                val result = signInClient.beginSignIn(signUpRequest).await()
                Timber.d("Successfully began Google One Tap sign up")
                AuthResult.Success(result.pendingIntent.intentSender)
            } catch (e: Exception) {
                val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
                Timber.e(e, "Google One Tap sign in/up failed: $userFriendlyMessage")
                AuthResult.Error(e)
            }
        }
    }

    override suspend fun signInWithGoogleOneTap(data: Intent?): AuthResult<AuthUser> {
        return try {
            Timber.d("Attempting to sign in with Google One Tap")
            val credential = signInClient.getSignInCredentialFromIntent(data)
            val googleIdToken = credential.googleIdToken
            val googleCredentials = GoogleAuthProvider.getCredential(googleIdToken, null)
            val result = auth.signInWithCredential(googleCredentials).await()
            val user = result.user?.toAuthUser()
            if (user != null) {
                _authState.value = AuthState.Authenticated(user)
                Timber.d("Successfully signed in with Google One Tap: ${user.id}")
                AuthResult.Success(user)
            } else {
                Timber.e("Google One Tap sign in failed: User is null")
                AuthResult.Error(Exception("Failed to sign in with Google"))
            }
        } catch (e: Exception) {
            val userFriendlyMessage = AuthErrorMapper.mapToUserFriendlyMessage(e)
            Timber.e(e, "Google One Tap sign in failed: $userFriendlyMessage")
            AuthResult.Error(e)
        }
    }

    override suspend fun shouldRememberCredentials(): Boolean {
        return try {
            Timber.d("Checking if credentials should be remembered")
            val result = authPreferencesDataStore.getRememberMe()
            Timber.d("Should remember credentials: $result")
            result
        } catch (e: Exception) {
            Timber.e(e, "Failed to check remember credentials flag")
            false
        }
    }

    override suspend fun saveCredentials(email: String, rememberMe: Boolean) {
        try {
            Timber.d("Saving credentials for email: $email, rememberMe: $rememberMe")
            authPreferencesDataStore.saveCredentials(email, rememberMe)
            Timber.d("Successfully saved credentials")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save credentials")
            throw e
        }
    }

    override suspend fun getSavedCredentials(): Pair<String, Boolean> {
        return try {
            Timber.d("Retrieving saved credentials")
            val result = authPreferencesDataStore.getSavedCredentials()
            Timber.d("Retrieved saved credentials: email=${result.first}, rememberMe=${result.second}")
            result
        } catch (e: Exception) {
            Timber.e(e, "Failed to retrieve saved credentials")
            Pair("", false)
        }
    }

    override suspend fun clearSavedCredentials() {
        try {
            Timber.d("Clearing saved credentials")
            authPreferencesDataStore.clearSavedCredentials()
            Timber.d("Successfully cleared saved credentials")
        } catch (e: Exception) {
            Timber.e(e, "Failed to clear saved credentials")
            throw e
        }
    }

    override suspend fun isBiometricAvailable(): Boolean {
        return try {
            Timber.d("Checking biometric availability")
            val biometricManager = BiometricManager.from(context)
            val result = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)
            val isAvailable = result == BiometricManager.BIOMETRIC_SUCCESS
            Timber.d("Biometric availability: $isAvailable (result code: $result)")
            isAvailable
        } catch (e: Exception) {
            Timber.e(e, "Failed to check biometric availability")
            false
        }
    }

    override suspend fun isBiometricEnabled(): Boolean {
        return try {
            Timber.d("Checking if biometric is enabled")
            val result = authPreferencesDataStore.getBiometricEnabled()
            Timber.d("Biometric enabled: $result")
            result
        } catch (e: Exception) {
            Timber.e(e, "Failed to check biometric enabled status")
            false
        }
    }

    override suspend fun setBiometricEnabled(enabled: Boolean) {
        try {
            Timber.d("Setting biometric enabled: $enabled")
            authPreferencesDataStore.setBiometricEnabled(enabled)
            Timber.d("Successfully set biometric enabled to: $enabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to set biometric enabled status")
            throw e
        }
    }


    private fun com.google.firebase.auth.FirebaseUser.toAuthUser(): AuthUser {
        return AuthUser(
            id = this.uid,
            email = this.email ?: "",
            displayName = this.displayName,
            photoUrl = this.photoUrl?.toString(),
            isEmailVerified = this.isEmailVerified
        )
    }
}
