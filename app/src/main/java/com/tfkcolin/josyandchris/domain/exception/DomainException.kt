package com.tfkcolin.josyandchris.domain.exception

/**
 * Base exception class for all domain-specific exceptions.
 * These exceptions represent business logic errors that should be handled by the UI layer.
 */
sealed class DomainException(
    override val message: String,
    override val cause: Throwable? = null
) : Exception(message, cause) {
    
    /**
     * Network-related exceptions
     */
    data class NetworkException(
        override val message: String = "Network error occurred",
        override val cause: Throwable? = null
    ) : DomainException(message, cause)
    
    /**
     * Authentication exceptions
     */
    sealed class AuthException(
        override val message: String,
        override val cause: Throwable? = null
    ) : DomainException(message, cause) {
        
        data class InvalidCredentialsException(
            override val message: String = "Invalid email or password"
        ) : AuthException(message)
        
        data class UserNotFoundException(
            override val message: String = "User not found"
        ) : AuthException(message)
        
        data class UnauthorizedException(
            override val message: String = "You are not authorized to perform this action"
        ) : AuthException(message)
        
        data class SessionExpiredException(
            override val message: String = "Your session has expired. Please login again"
        ) : AuthException(message)
    }
    
    /**
     * Data-related exceptions
     */
    sealed class DataException(
        override val message: String,
        override val cause: Throwable? = null
    ) : DomainException(message, cause) {
        
        data class NotFoundException(
            val entityType: String,
            val id: String,
            override val message: String = "$entityType with id $id not found"
        ) : DataException(message)
        
        data class DuplicateException(
            val entityType: String,
            override val message: String = "$entityType already exists"
        ) : DataException(message)
        
        data class ValidationException(
            override val message: String = "Validation failed"
        ) : DataException(message)
        
        data class DeleteException(
            override val message: String = "Failed to delete item"
        ) : DataException(message)
        
        data class UpdateException(
            override val message: String = "Failed to update item"
        ) : DataException(message)
        
        data class CreateException(
            override val message: String = "Failed to create item"
        ) : DataException(message)
    }
    
    /**
     * Storage-related exceptions
     */
    sealed class StorageException(
        override val message: String,
        override val cause: Throwable? = null
    ) : DomainException(message, cause) {
        
        data class UploadException(
            override val message: String = "Failed to upload file"
        ) : StorageException(message)
        
        data class DownloadException(
            override val message: String = "Failed to download file"
        ) : StorageException(message)
        
        data class QuotaExceededException(
            override val message: String = "Storage quota exceeded"
        ) : StorageException(message)
        
        data class FileNotFoundException(
            val fileName: String,
            override val message: String = "File $fileName not found"
        ) : StorageException(message)
    }
    
    /**
     * Business logic exceptions
     */
    sealed class BusinessException(
        override val message: String,
        override val cause: Throwable? = null
    ) : DomainException(message, cause) {
        
        data class InsufficientPermissionsException(
            override val message: String = "You don't have permission to perform this action"
        ) : BusinessException(message)
        
        data class InvalidOperationException(
            override val message: String = "This operation is not allowed"
        ) : BusinessException(message)
        
        data class ResourceBusyException(
            override val message: String = "Resource is currently busy. Please try again later"
        ) : BusinessException(message)
    }
    
    /**
     * Unknown exception for unexpected errors
     */
    data class UnknownException(
        override val message: String = "An unexpected error occurred",
        override val cause: Throwable? = null
    ) : DomainException(message, cause)
}
