package com.tfkcolin.josyandchris.domain.util

import com.google.firebase.firestore.FirebaseFirestoreException
import com.google.firebase.storage.StorageException
import com.tfkcolin.josyandchris.domain.exception.DomainException
import timber.log.Timber
import java.io.IOException
import java.net.UnknownHostException
import java.util.concurrent.CancellationException
import java.util.concurrent.TimeoutException

/**
 * Maps various exceptions to domain-specific exceptions for consistent error handling
 */
object ErrorMapper {
    
    /**
     * Maps a throwable to a domain exception
     */
    fun mapToDomainException(throwable: Throwable, context: String? = null): DomainException {
        // Log the original exception for debugging
        Timber.e(throwable, "Error in context: ${context ?: "Unknown"}")
        
        return when (throwable) {
            // Network exceptions
            is UnknownHostException,
            is IOException -> DomainException.NetworkException(
                message = "Network connection error. Please check your internet connection.",
                cause = throwable
            )
            
            is TimeoutException -> DomainException.NetworkException(
                message = "Request timed out. Please try again.",
                cause = throwable
            )
            
            // Firebase Storage exceptions
            is StorageException -> mapStorageException(throwable)
            
            // Firestore exceptions
            is FirebaseFirestoreException -> mapFirestoreException(throwable)
            
            // Cancellation is usually intentional, so we don't want to show error
            is CancellationException -> DomainException.UnknownException(
                message = "Operation was cancelled",
                cause = throwable
            )
            
            // Domain exceptions should be passed through
            is DomainException -> throwable
            
            // Default case
            else -> DomainException.UnknownException(
                message = "An unexpected error occurred: ${throwable.message}",
                cause = throwable
            )
        }
    }
    
    /**
     * Maps Firebase Storage exceptions to domain exceptions
     */
    private fun mapStorageException(exception: StorageException): DomainException {
        return when (exception.errorCode) {
            StorageException.ERROR_OBJECT_NOT_FOUND -> 
                DomainException.StorageException.FileNotFoundException(
                    fileName = "requested file",
                    message = "The requested file was not found"
                )
            
            StorageException.ERROR_QUOTA_EXCEEDED -> 
                DomainException.StorageException.QuotaExceededException()
            
            StorageException.ERROR_NOT_AUTHENTICATED -> 
                DomainException.AuthException.UnauthorizedException(
                    message = "You must be logged in to perform this action"
                )
            
            StorageException.ERROR_NOT_AUTHORIZED -> 
                DomainException.AuthException.UnauthorizedException(
                    message = "You don't have permission to access this file"
                )
            
            StorageException.ERROR_RETRY_LIMIT_EXCEEDED,
            StorageException.ERROR_INVALID_CHECKSUM -> 
                DomainException.StorageException.UploadException(
                    message = "Upload failed. Please try again."
                )
            
            else -> DomainException.StorageException.UploadException(
                message = "Storage operation failed: ${exception.message}"
            )
        }
    }
    
    /**
     * Maps Firestore exceptions to domain exceptions
     */
    private fun mapFirestoreException(exception: FirebaseFirestoreException): DomainException {
        return when (exception.code) {
            FirebaseFirestoreException.Code.NOT_FOUND -> 
                DomainException.DataException.NotFoundException(
                    entityType = "Document",
                    id = "unknown",
                    message = "The requested data was not found"
                )
            
            FirebaseFirestoreException.Code.ALREADY_EXISTS -> 
                DomainException.DataException.DuplicateException(
                    entityType = "Document",
                    message = "This item already exists"
                )
            
            FirebaseFirestoreException.Code.PERMISSION_DENIED,
            FirebaseFirestoreException.Code.UNAUTHENTICATED -> 
                DomainException.AuthException.UnauthorizedException(
                    message = "You don't have permission to perform this action"
                )
            
            FirebaseFirestoreException.Code.UNAVAILABLE,
            FirebaseFirestoreException.Code.DEADLINE_EXCEEDED -> 
                DomainException.NetworkException(
                    message = "Service is currently unavailable. Please try again later."
                )
            
            FirebaseFirestoreException.Code.INVALID_ARGUMENT -> 
                DomainException.DataException.ValidationException(
                    message = "Invalid data provided"
                )
            
            FirebaseFirestoreException.Code.RESOURCE_EXHAUSTED -> 
                DomainException.BusinessException.ResourceBusyException(
                    message = "Too many requests. Please slow down."
                )
            
            else -> DomainException.DataException.UpdateException(
                message = "Database operation failed: ${exception.message}"
            )
        }
    }
    
    /**
     * Extension function to convert Result failures to domain exceptions
     */
    fun <T> Result<T>.mapError(context: String? = null): Result<T> {
        return this.mapCatching { it }
            .recoverCatching { throw mapToDomainException(it, context) }
    }
}
