package com.tfkcolin.josyandchris.ui.components.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.josyandchris.ui.components.foundation.ButtonWithLoading
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten

data class ConfirmationState(
    val action: () -> Unit,
    val title: String,
    val content: String,
    val cancelText: String = "Non",
    val confirmText: String = "Oui",
    val confirmColor: Color = Color.White,
    val cancelColor: Color = Color.White,
    val confirmBackground: Color = Color.Green.lighten(.2f),
    val cancelBackground: Color = Color.Gray.lighten(.2f),
)
@Composable
fun ConfirmationDialog(
    state: ConfirmationState,
    onDismissRequest: () -> Unit,
){
    Dialog(
        onDismissRequest = onDismissRequest,
    ) {
        DialogContent(state, onDismissRequest)
    }
}
@Composable
private fun DialogContent(
    state: ConfirmationState,
    onDismissRequest: () -> Unit,
){
    Surface(shape = RoundedCornerShape(15.dp)) {
        Column(modifier = Modifier.padding(15.dp)) {
            Text(
                modifier = Modifier.padding(bottom = 10.dp),
                text = state.title,
                fontWeight = FontWeight.Bold,
                fontSize = 23.sp,
                color = Color.Black.lighten(.2f)
            )
            Divider(
                modifier = Modifier.padding(bottom = 5.dp)
            )
            Text(
                text = state.content,
                fontSize = 14.sp,
                color = Color.Black.lighten(.4f)
            )
            Row(
                modifier = Modifier
                    .padding(top = 5.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                ButtonWithLoading(
                    modifier = Modifier.padding(horizontal = 10.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = state.confirmBackground,
                        contentColor = state.confirmColor
                    ),
                    onClick = {
                        state.action()
                        onDismissRequest()
                    }
                ) {
                    Text(text = state.confirmText)
                }
                ButtonWithLoading(
                    colors = ButtonDefaults.buttonColors(
                        containerColor = state.cancelBackground,
                        contentColor = state.cancelColor
                    ),
                    onClick = onDismissRequest
                ) {
                    Text(text = state.cancelText)
                }
            }
        }
    }
}

@Preview
@Composable
private fun ConfirmationPreview(){
    JACTheme {
        ConfirmationDialog(
            state = ConfirmationState(
                action = {},
                title = "Supprimer le pays",
                content = "Voulez vous vraiment supprimer ce pays? cette action est irreversible mais ne supprimera pas les transactions qui lui sont associées."
            ),
            onDismissRequest = {}
        )
    }
}