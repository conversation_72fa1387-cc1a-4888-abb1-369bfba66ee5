package com.tfkcolin.josyandchris.ui.screens.accounting

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.repository.FinancialTransactionRepository
import com.tfkcolin.josyandchris.ui.data.TransactionUiFilterState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.ExperimentalCoroutinesApi

@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class AccountingScreenViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val transactionRepository: FinancialTransactionRepository
): ViewModel() {
    val currentCountryName = CountryArgs(savedStateHandle).countryName
    
    // UI filter state management
    private val _uiFilterState = MutableStateFlow(TransactionUiFilterState())
    val uiFilterState: StateFlow<TransactionUiFilterState> = _uiFilterState.asStateFlow()
    
    // Refresh trigger for forcing pagination refresh after CRUD operations
    private val _refreshTrigger = MutableSharedFlow<Unit>(replay = 1).apply {
        tryEmit(Unit) // Initial emit
    }
    
    // Pagination data - this is the main source of truth for paginated transactions
    val transactionsPagingData: Flow<PagingData<FinancialTransaction>> = combine(
        _uiFilterState,
        _refreshTrigger
    ) { filterState, _ ->
        filterState
    }.flatMapLatest { filterState ->
        transactionRepository.getTransactionsPagingDataWithFilters(filterState.toQueryFilters())
    }.cachedIn(viewModelScope)
    
    // Total calculations state - maintained separately for proper unidirectional data flow
    private val _inputTotal = MutableStateFlow(0.0)
    val inputTotal: StateFlow<Double> = _inputTotal.asStateFlow()
    
    private val _outputTotal = MutableStateFlow(0.0)
    val outputTotal: StateFlow<Double> = _outputTotal.asStateFlow()
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: TransactionUiFilterState) {
        _uiFilterState.value = filterState
    }
    
    /**
     * Force refresh the pagination data
     */
    private fun refreshTransactions() {
        viewModelScope.launch {
            _refreshTrigger.emit(Unit)
        }
    }

    /**
     * Create a new transaction using repository pattern
     */
    fun createTransaction(
        transaction: FinancialTransaction,
        onSuccess: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            transactionRepository.createTransaction(transaction)
                .onSuccess { transactionId ->
                    // Trigger refresh to update the list
                    refreshTransactions()
                    onSuccess(transactionId)
                }
                .onFailure { exception ->
                    onError(exception.message ?: "Failed to create transaction")
                }
        }
    }
    
    /**
     * Update an existing transaction
     */
    fun updateTransaction(
        transaction: FinancialTransaction,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            transactionRepository.updateTransaction(transaction)
                .onSuccess {
                    // Trigger refresh to update the list
                    refreshTransactions()
                    onSuccess()
                }
                .onFailure { exception ->
                    onError(exception.message ?: "Failed to update transaction")
                }
        }
    }
    
    /**
     * Delete a transaction
     */
    fun deleteTransaction(
        transactionId: String,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            transactionRepository.deleteTransaction(transactionId)
                .onSuccess {
                    // Trigger refresh to update the list
                    refreshTransactions()
                    onSuccess()
                }
                .onFailure { exception ->
                    onError(exception.message ?: "Failed to delete transaction")
                }
        }
    }
    /*fun getTotalTransactionPrice(
        transactions: List<FinancialTransaction>,
        country: String? = null,
        startDate: Date? = null,
        endDate: Date? = null
    ) = TransactionLogic.getTotalTransactionPrice(
        transactions = transactions,
        country = country,
        startDate = startDate,
        endDate = endDate
    )

    fun getTotalCommandPrice(
        commands: List<Command>,
        clientName: String? = null,
        country: String? = null,
        startDate: Date? = null,
        endDate: Date? = null
    ) = CommandLogic.getTotalCommandPrice(
        commands = commands,
        clientName = clientName,
        country = country,
        startDate = startDate,
        endDate = endDate
    )*/
}