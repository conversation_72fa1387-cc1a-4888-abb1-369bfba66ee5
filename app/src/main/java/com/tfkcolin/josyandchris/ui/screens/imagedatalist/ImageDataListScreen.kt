package com.tfkcolin.josyandchris.ui.screens.imagedatalist

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.LoadState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.components.JACImageDataItem
import com.tfkcolin.josyandchris.ui.components.LoadingItem
import com.tfkcolin.josyandchris.ui.components.ErrorItem
import com.tfkcolin.josyandchris.ui.components.EmptyStateItem
import com.tfkcolin.josyandchris.ui.components.FullScreenLoadingItem
import com.tfkcolin.josyandchris.ui.components.FullScreenErrorItem
import com.tfkcolin.josyandchris.ui.data.ImageDataUiFilterState

private fun LazyListScope.handleImageDataPagingStates(pager: androidx.paging.compose.LazyPagingItems<ImageData>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    FullScreenLoadingItem("Loading images...")
                }
            }
            loadState.append is LoadState.Loading -> {
                item {
                    LoadingItem(message = "Loading more images...")
                }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    FullScreenErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item {
                    EmptyStateItem("No images found")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageDataListScreen(
    modifier: Modifier = Modifier,
    onNavigateToImageDetail: (String) -> Unit
) {
    val viewModel: ImageDataListViewModel = hiltViewModel()
    ImageDataListScreen(
        modifier = modifier,
        viewModel = viewModel,
        onNavigateToImageDetail = onNavigateToImageDetail
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageDataListScreen(
    modifier: Modifier = Modifier,
    viewModel: ImageDataListViewModel,
    onNavigateToImageDetail: (String) -> Unit
) {
    var uiFilterState by remember { mutableStateOf(ImageDataUiFilterState()) }

    // Collect paging data
    val pager = viewModel.imageDataPagingData.collectAsLazyPagingItems()

    // Update filters when UI state changes
    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }

    val isLiveMode by viewModel.isLiveMode.collectAsState()

    Column(modifier = modifier.fillMaxSize()) {
        // Top app bar
        TopAppBar(
            title = { Text("Image Data List") },
            actions = {
                Switch(
                    checked = isLiveMode,
                    onCheckedChange = { viewModel.toggleLiveMode() }
                )
                if (isLiveMode) {
                    Text("LIVE", color = MaterialTheme.colorScheme.primary, modifier = Modifier.padding(start = 8.dp))
                }
            }
        )

        // Search bar
        OutlinedTextField(
            value = uiFilterState.searchTerm,
            onValueChange = { uiFilterState = uiFilterState.copy(searchTerm = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            placeholder = { Text("Search images...") },
            singleLine = true,
            leadingIcon = {
                Icon(Icons.Default.Search, contentDescription = "Search")
            },
            trailingIcon = {
                if (uiFilterState.searchTerm.isNotEmpty()) {
                    IconButton(onClick = {
                        uiFilterState = uiFilterState.copy(searchTerm = "")
                    }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear search")
                    }
                }
            }
        )

        // Category filter
        OutlinedTextField(
            value = uiFilterState.category,
            onValueChange = { uiFilterState = uiFilterState.copy(category = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            placeholder = { Text("Filter by category...") },
            singleLine = true,
            trailingIcon = {
                if (uiFilterState.category.isNotEmpty()) {
                    IconButton(onClick = {
                        uiFilterState = uiFilterState.copy(category = "")
                    }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear category filter")
                    }
                }
            }
        )

        // Genre filter
        OutlinedTextField(
            value = uiFilterState.genre,
            onValueChange = { uiFilterState = uiFilterState.copy(genre = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            placeholder = { Text("Filter by genre...") },
            singleLine = true,
            trailingIcon = {
                if (uiFilterState.genre.isNotEmpty()) {
                    IconButton(onClick = {
                        uiFilterState = uiFilterState.copy(genre = "")
                    }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear genre filter")
                    }
                }
            }
        )

        // Upload status filter
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                selected = uiFilterState.uploaded == true,
                onClick = {
                    uiFilterState = uiFilterState.copy(
                        uploaded = if (uiFilterState.uploaded == true) null else true
                    )
                },
                label = { Text("Uploaded") }
            )
            FilterChip(
                selected = uiFilterState.uploaded == false,
                onClick = {
                    uiFilterState = uiFilterState.copy(
                        uploaded = if (uiFilterState.uploaded == false) null else false
                    )
                },
                label = { Text("Pending") }
            )
        }

        // Image list with Paging 3
        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(pager.itemCount) { index ->
                val imageData = pager[index]
                imageData?.let {
                    JACImageDataItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp, vertical = 4.dp),
                        imageData = it,
                        onClick = { onNavigateToImageDetail(it.id) }
                    )
                }
            }

            // Handle paging states
            handleImageDataPagingStates(pager)
        }
    }
}
