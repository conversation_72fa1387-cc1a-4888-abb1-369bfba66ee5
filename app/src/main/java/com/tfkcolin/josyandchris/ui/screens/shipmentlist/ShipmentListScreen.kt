package com.tfkcolin.josyandchris.ui.screens.shipmentlist

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.LoadState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.ui.components.JACShipmentItem
import com.tfkcolin.josyandchris.ui.components.LoadingItem
import com.tfkcolin.josyandchris.ui.components.ErrorItem
import com.tfkcolin.josyandchris.ui.components.EmptyStateItem
import com.tfkcolin.josyandchris.ui.components.FullScreenLoadingItem
import com.tfkcolin.josyandchris.ui.components.FullScreenErrorItem
import com.tfkcolin.josyandchris.ui.data.ShipmentUiFilterState

private fun LazyListScope.handleShipmentPagingStates(pager: androidx.paging.compose.LazyPagingItems<Shipment>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    FullScreenLoadingItem("Loading shipments...")
                }
            }
            loadState.append is LoadState.Loading -> {
                item {
                    LoadingItem(message = "Loading more shipments...")
                }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    FullScreenErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item {
                    EmptyStateItem("No shipments found")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShipmentListScreen(
    modifier: Modifier = Modifier,
    viewModel: ShipmentListViewModel = hiltViewModel(),
    onNavigateToShipmentDetail: (String) -> Unit
) {
    // UI filter state management
    var uiFilterState by remember { mutableStateOf(ShipmentUiFilterState()) }

    // Collect paging data
    val pager = viewModel.shipmentsPagingData.collectAsLazyPagingItems()

    // Update filters when UI state changes
    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Top app bar
        TopAppBar(
            title = { Text("Shipment List") },
            actions = {
                val isLiveMode by viewModel.isLiveMode.collectAsState()
                Switch(
                    checked = isLiveMode,
                    onCheckedChange = { viewModel.toggleLiveMode() }
                )
                if (isLiveMode) {
                    Text("LIVE", color = MaterialTheme.colorScheme.primary, modifier = Modifier.padding(start = 8.dp))
                }
            }
        )

        // Search bar
        OutlinedTextField(
            value = uiFilterState.searchTerm,
            onValueChange = { uiFilterState = uiFilterState.copy(searchTerm = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            placeholder = { Text("Search shipments...") },
            singleLine = true,
            leadingIcon = {
                Icon(Icons.Default.Search, contentDescription = "Search")
            },
            trailingIcon = {
                if (uiFilterState.searchTerm.isNotEmpty()) {
                    IconButton(onClick = { uiFilterState = uiFilterState.copy(searchTerm = "") }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear search")
                    }
                }
            }
        )

        // Client Name filter
        OutlinedTextField(
            value = uiFilterState.clientName,
            onValueChange = { uiFilterState = uiFilterState.copy(clientName = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            placeholder = { Text("Filter by client name...") },
            singleLine = true,
            trailingIcon = {
                if (uiFilterState.clientName.isNotEmpty()) {
                    IconButton(onClick = { uiFilterState = uiFilterState.copy(clientName = "") }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear client name filter")
                    }
                }
            }
        )

        // Client Phone filter
        OutlinedTextField(
            value = uiFilterState.clientPhone,
            onValueChange = { uiFilterState = uiFilterState.copy(clientPhone = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            placeholder = { Text("Filter by client phone...") },
            singleLine = true,
            trailingIcon = {
                if (uiFilterState.clientPhone.isNotEmpty()) {
                    IconButton(onClick = { uiFilterState = uiFilterState.copy(clientPhone = "") }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear client phone filter")
                    }
                }
            }
        )

        // Shipment list with Paging 3
        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(pager.itemCount) { index ->
                val shipment = pager[index]
                shipment?.let {
                    JACShipmentItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp, vertical = 4.dp),
                        shipment = it,
                        onClick = { onNavigateToShipmentDetail(it.id) }
                    )
                }
            }

            // Handle paging states
            handleShipmentPagingStates(pager)
        }
    }
}
