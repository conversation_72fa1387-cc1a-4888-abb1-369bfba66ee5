package com.tfkcolin.josyandchris.ui.components.foundation

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun ImagePickerIconButton(
    modifier: Modifier = Modifier,
    icon: @Composable () -> Unit = {
        Icon(
            painter = painterResource(id = R.drawable.baseline_image_search_24),
            contentDescription = null
        )
    },
    onPickImage: (Uri) -> Unit
) {
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent(),
        onResult = { uri -> uri?.let { onPickImage(uri) } }
    )
    Box(modifier = modifier) {
        IconButton(
            modifier = Modifier.fillMaxSize(),
            onClick = { imagePicker.launch("image/*") },
        ) {
            icon()
        }
    }
}

@Preview
@Composable
private fun ImagePickerIconButtonPreview(){
    JACTheme {
        ImagePickerIconButton {}
    }
}