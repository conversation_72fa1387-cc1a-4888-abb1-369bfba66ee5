package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.components.foundation.DateRangePresets
import com.tfkcolin.josyandchris.ui.data.CommandUiFilterState
import com.tfkcolin.josyandchris.ui.theme.JACTheme

/**
 * Demo screen showing how to integrate and use the FilterBottomSheet component
 * with proper state management using rememberSaveable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterBottomSheetDemo() {
    // Single rememberSaveable state holder for filter state
    var filterState by rememberSaveable { 
        mutableStateOf(
            CommandUiFilterState(
                // Pre-populate with some sample recent chips
                recentClientChips = listOf(
                    "John Doe",
                    "Jane Smith", 
                    "+1234567890",
                    "Robert Wilson",
                    "+0987654321"
                ),
                recentLocationChips = listOf(
                    "New York",
                    "Paris", 
                    "London",
                    "Tokyo",
                    "USA",
                    "France"
                )
            )
        )
    }
    
    var showFilterSheet by rememberSaveable { mutableStateOf(false) }
    var appliedFilters by remember { mutableStateOf<CommandUiFilterState?>(null) }
    
    // Custom preset filters for demo
    val presetFilters = mapOf(
        "Recent Orders" to CommandUiFilterState(
            dateRange = DateRangePresets.lastDays(30),
            recentClientChips = filterState.recentClientChips,
            recentLocationChips = filterState.recentLocationChips
        ),
        "Pending Payment" to CommandUiFilterState(
            paymentStatus = false,
            recentClientChips = filterState.recentClientChips,
            recentLocationChips = filterState.recentLocationChips
        ),
        "Completed Orders" to CommandUiFilterState(
            selectedStep = CommandStep.OK,
            paymentStatus = true,
            recentClientChips = filterState.recentClientChips,
            recentLocationChips = filterState.recentLocationChips
        ),
        "This Month" to CommandUiFilterState(
            dateRange = DateRangePresets.thisMonth(),
            recentClientChips = filterState.recentClientChips,
            recentLocationChips = filterState.recentLocationChips
        ),
        "USA Clients" to CommandUiFilterState(
            clientCountry = "USA",
            recentClientChips = filterState.recentClientChips,
            recentLocationChips = filterState.recentLocationChips
        )
    )
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Filter Demo") },
                actions = {
                    // Filter button with badge if filters are applied
                    BadgedBox(
                        badge = {
                            if (appliedFilters != null && hasActiveFilters(appliedFilters!!)) {
                                Badge {
                                    Text(countActiveFilters(appliedFilters!!).toString())
                                }
                            }
                        }
                    ) {
                        IconButton(onClick = { showFilterSheet = true }) {
                            Icon(
                                imageVector = Icons.Filled.FilterList,
                                contentDescription = "Filter"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Applied filters summary
            if (appliedFilters != null && hasActiveFilters(appliedFilters!!)) {
                AppliedFiltersCard(
                    filterState = appliedFilters!!,
                    onClearFilters = {
                        appliedFilters = null
                        filterState = CommandUiFilterState(
                            recentClientChips = filterState.recentClientChips,
                            recentLocationChips = filterState.recentLocationChips
                        )
                    }
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Demo content list
            Text(
                text = "Filtered Results",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Simulated results based on filters
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(getFilteredResults(appliedFilters)) { result ->
                    DemoResultCard(result = result)
                }
            }
        }
    }
    
    // Filter Bottom Sheet
    if (showFilterSheet) {
        FilterBottomSheet(
            filterState = filterState,
            presetFilters = presetFilters,
            onFilterStateChange = { newState ->
                filterState = newState
            },
            onApply = { finalState ->
                appliedFilters = finalState
                filterState = finalState
                showFilterSheet = false
            },
            onClear = {
                val clearedState = CommandUiFilterState(
                    recentClientChips = filterState.recentClientChips,
                    recentLocationChips = filterState.recentLocationChips
                )
                filterState = clearedState
                appliedFilters = null
            },
            onDismiss = {
                // Reset filter state to applied filters when dismissing without applying
                appliedFilters?.let { applied ->
                    filterState = applied
                }
                showFilterSheet = false
            }
        )
    }
}

@Composable
private fun AppliedFiltersCard(
    filterState: CommandUiFilterState,
    onClearFilters: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Active Filters",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                TextButton(onClick = onClearFilters) {
                    Text("Clear All")
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Display active filter summary
            getActiveFiltersSummary(filterState).forEach { summary ->
                Text(
                    text = "• $summary",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun DemoResultCard(result: String) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = result,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = "Sample order details here...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

// Helper functions for demo logic
private fun hasActiveFilters(filterState: CommandUiFilterState): Boolean {
    return filterState.selectedStep != null ||
            filterState.searchTerm.isNotBlank() ||
            filterState.proofUploaded != null ||
            filterState.dateRange != null ||
            filterState.clientName.isNotBlank() ||
            filterState.clientPhone.isNotBlank() ||
            filterState.clientCountry.isNotBlank() ||
            filterState.clientCity.isNotBlank() ||
            filterState.paymentStatus != null
}

private fun countActiveFilters(filterState: CommandUiFilterState): Int {
    var count = 0
    if (filterState.selectedStep != null) count++
    if (filterState.searchTerm.isNotBlank()) count++
    if (filterState.proofUploaded != null) count++
    if (filterState.dateRange != null) count++
    if (filterState.clientName.isNotBlank()) count++
    if (filterState.clientPhone.isNotBlank()) count++
    if (filterState.clientCountry.isNotBlank()) count++
    if (filterState.clientCity.isNotBlank()) count++
    if (filterState.paymentStatus != null) count++
    return count
}

private fun getActiveFiltersSummary(filterState: CommandUiFilterState): List<String> {
    val summaries = mutableListOf<String>()
    
    filterState.selectedStep?.let { step ->
        summaries.add("Status: ${step.step}")
    }
    
    if (filterState.searchTerm.isNotBlank()) {
        summaries.add("Search: \"${filterState.searchTerm}\"")
    }
    
    filterState.paymentStatus?.let { paid ->
        summaries.add("Payment: ${if (paid) "Paid" else "Unpaid"}")
    }
    
    if (filterState.clientName.isNotBlank()) {
        summaries.add("Client: ${filterState.clientName}")
    }
    
    if (filterState.clientPhone.isNotBlank()) {
        summaries.add("Phone: ${filterState.clientPhone}")
    }
    
    if (filterState.clientCountry.isNotBlank()) {
        summaries.add("Country: ${filterState.clientCountry}")
    }
    
    if (filterState.clientCity.isNotBlank()) {
        summaries.add("City: ${filterState.clientCity}")
    }
    
    filterState.dateRange?.let { range ->
        summaries.add("Date Range: Selected")
    }
    
    return summaries
}

private fun getFilteredResults(appliedFilters: CommandUiFilterState?): List<String> {
    return if (appliedFilters == null || !hasActiveFilters(appliedFilters)) {
        // Return all results when no filters are applied
        listOf(
            "Order #001 - John Doe",
            "Order #002 - Jane Smith", 
            "Order #003 - Robert Wilson",
            "Order #004 - Mary Johnson",
            "Order #005 - David Brown"
        )
    } else {
        // Return filtered results based on applied filters
        val baseResults = mutableListOf<String>()
        
        if (appliedFilters.clientName.isNotBlank()) {
            baseResults.add("Order #001 - ${appliedFilters.clientName}")
        }
        
        if (appliedFilters.paymentStatus == false) {
            baseResults.add("Order #002 - Unpaid Order")
            baseResults.add("Order #006 - Pending Payment")
        }
        
        if (appliedFilters.selectedStep == CommandStep.OK) {
            baseResults.add("Order #003 - Completed Order")
            baseResults.add("Order #004 - Delivered Successfully")
        }
        
        if (appliedFilters.clientCountry.isNotBlank()) {
            baseResults.add("Order #005 - Client from ${appliedFilters.clientCountry}")
        }
        
        if (baseResults.isEmpty()) {
            baseResults.add("Order #007 - Filtered Result")
            baseResults.add("Order #008 - Matching Criteria")
        }
        
        baseResults
    }
}

@Preview(showBackground = true)
@Composable
private fun FilterBottomSheetDemoPreview() {
    JACTheme {
        FilterBottomSheetDemo()
    }
}
