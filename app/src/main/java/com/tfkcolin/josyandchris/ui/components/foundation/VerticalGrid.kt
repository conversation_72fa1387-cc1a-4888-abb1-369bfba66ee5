package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun VerticalGrid(
    modifier: Modifier = Modifier,
    columns: Int = 2,
    children: @Composable () -> Unit
){
    Layout(modifier = modifier, content = children){ measurables, constraints ->
        val itemWidth = constraints.maxWidth / columns
        val itemConstraints = constraints.copy(minWidth = itemWidth, maxWidth = itemWidth)
        val placeables = measurables.map { it.measure(itemConstraints) }
        val columnHeights = Array(columns) { 0 }
        placeables.forEachIndexed { index, placeable ->
            val column = index % columns
            columnHeights[column] += placeable.height
        }
        val height = (columnHeights.maxOrNull() ?: constraints.minHeight)
            .coerceAtMost(constraints.maxHeight)
        layout(width = constraints.maxWidth, height = height){
            val columnY = Array(columns) { 0 }
            placeables.forEachIndexed { index, placeable ->
                val column = index % columns
                placeable.placeRelative(x = column * itemWidth, y = columnY[column])
                columnY[column] += placeable.height
            }
        }
    }
}

@Preview
@Composable
private fun VerticalGridPreview(){
    JACTheme {
        Surface(color = MaterialTheme.colorScheme.background) {
            VerticalGrid {
                Text(text = "salut")
                Text(text = "salut 2")
                Text(text = "salut 3")
                Text(text = "salut 4")
            }
        }
    }
}