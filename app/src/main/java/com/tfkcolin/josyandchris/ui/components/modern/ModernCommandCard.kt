package com.tfkcolin.josyandchris.ui.components.modern

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.josyandchris.data.ClientData
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.MiniProduct
import com.tfkcolin.josyandchris.ui.components.foundation.JACAvatar
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.delay
import java.text.DateFormat

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModernCommandCard(
    modifier: Modifier = Modifier,
    command: Command,
    onClick: () -> Unit,
    onViewDetails: (() -> Unit)? = null,
    onEdit: (() -> Unit)? = null,
    animationDelay: Int = 0
) {
    var isVisible by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.96f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "card_scale"
    )
    
    val currentStep = CommandStep.values()[command.commandStepIndex]
    val progress = (command.commandStepIndex + 1) / CommandStep.values().size.toFloat()
    
    val created = remember {
        val format = DateFormat.getDateInstance(DateFormat.SHORT)
        format.format(command.created)
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(
            animationSpec = tween(durationMillis = 400)
        ) + slideInVertically(
            initialOffsetY = { it / 3 },
            animationSpec = tween(durationMillis = 400)
        ),
        modifier = modifier
    ) {
        Card(
            onClick = onClick,
            modifier = Modifier
                .fillMaxWidth()
                .scale(animatedScale)
                .animateContentSize(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp,
                pressedElevation = 4.dp
            ),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            interactionSource = interactionSource
        ) {
            Column {
                // Header with gradient background
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(72.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.9f), // Increased alpha
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f) // Increased alpha
                                )
                            )
                        )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.weight(1f)
                        ) {
                            // Large client avatar
                            JACAvatar(
                                modifier = Modifier.size(48.dp),
                                image = null // ClientData doesn't have image field
                            )
                            
                            Spacer(modifier = Modifier.width(12.dp))
                            
                            Column(
                                modifier = Modifier.weight(1f)
                            ) {
                                // Client name
                                Text(
                                    text = command.client.name,
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                                
                                // Client location
                                Text(
                                    text = "${command.client.city}, ${command.client.country}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.9f), // Increased alpha
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                        }
                        
                        // Status badge
                        Surface(
                            shape = RoundedCornerShape(20.dp),
                            color = getStatusColor(currentStep),
                            modifier = Modifier.padding(start = 8.dp)
                        ) {
                            Text(
                                text = currentStep.step,
                                style = MaterialTheme.typography.labelSmall,
                                fontWeight = FontWeight.Medium,
                                color = Color.White,
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                }
                
                // Content area
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    // Progress section
                    Column(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Progress",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "${(progress * 100).toInt()}%",
                                style = MaterialTheme.typography.labelMedium,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // Animated progress bar
                        val animatedProgress by animateFloatAsState(
                            targetValue = progress,
                            animationSpec = tween(
                                durationMillis = 1000,
                                delayMillis = animationDelay + 200
                            ),
                            label = "progress"
                        )
                        
                        LinearProgressIndicator(
                            progress = { animatedProgress },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(6.dp)
                                .clip(RoundedCornerShape(3.dp)),
                            color = getStatusColor(currentStep),
                            trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.4f) // Increased alpha
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Additional info
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // Products count
                        Column {
                            Text(
                                text = "Products",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "${command.products.size}",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                        
                        // Date
                        Column(
                            horizontalAlignment = Alignment.End
                        ) {
                            Text(
                                text = "Created",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = created,
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                        
                        // Payment status
                        Column(
                            horizontalAlignment = Alignment.End
                        ) {
                            Text(
                                text = "Payment",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = if (command.paymentProofImageUrl != null) "✓ Verified" else "Pending",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium,
                                color = if (command.paymentProofImageUrl != null) 
                                    Color(0xFF4CAF50) else MaterialTheme.colorScheme.error,
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Quick actions
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // View details action
                        OutlinedButton(
                            onClick = { onViewDetails?.invoke() ?: onClick() },
                            modifier = Modifier.weight(1f),
                            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Visibility,
                                contentDescription = "View details",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "View",
                                style = MaterialTheme.typography.labelMedium
                            )
                        }
                        
                        // Edit action
                        if (onEdit != null) {
                            Button(
                                onClick = onEdit,
                                modifier = Modifier.weight(1f),
                                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "Edit command",
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Edit",
                                    style = MaterialTheme.typography.labelMedium
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun getStatusColor(step: CommandStep): Color {
    return when (step.ordinal) {
        0 -> Color(0xFFF44336) // Red - Initial
        1 -> Color(0xFFFF9800) // Orange - Processing
        2 -> Color(0xFF2196F3) // Blue - In transit
        3 -> Color(0xFF9C27B0) // Purple - Arrived
        4 -> Color(0xFF4CAF50) // Green - Ready
        5 -> Color(0xFF4CAF50) // Green - Completed
        else -> Color(0xFF757575) // Grey - Default
    }
}

@Preview(showBackground = true)
@Composable
private fun ModernCommandCardPreview() {
    JACTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ModernCommandCard(
                command = Command(
                    id = "1",
                    client = ClientData(
                        name = "Jean Tambo",
                        tel = "656728667",
                        country = "Cameroun",
                        city = "Yaoundé"
                    ),
                    products = listOf(
                        MiniProduct(name = "Sac", productImage = ""),
                        MiniProduct(name = "Pantalon", productImage = ""),
                        MiniProduct(name = "Chaussures", productImage = "")
                    ),
                    commandStepIndex = 2,
                    paymentProofImageUrl = "proof.jpg"
                ),
                onClick = {},
                onViewDetails = {},
                onEdit = {}
            )
            
            ModernCommandCard(
                command = Command(
                    id = "2",
                    client = ClientData(
                        name = "Marie Nguyen",
                        tel = "677123456",
                        country = "Cameroun",
                        city = "Douala"
                    ),
                    products = listOf(
                        MiniProduct(name = "Robe", productImage = "")
                    ),
                    commandStepIndex = 0,
                    paymentProofImageUrl = null
                ),
                onClick = {},
                onViewDetails = {}
            )
        }
    }
}
