package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ClientData
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten

@Composable
fun JACEditClientData(
    modifier: Modifier = Modifier,
    clientData: ClientData,
    onClientDataChange: (ClientData) -> Unit,
    editable: Boolean = true
){
    val focusManager = LocalFocusManager.current
    Box(modifier = modifier){
        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Title(
                text = "Client",
                color = Color.Black.lighten(.23f)
            )
            Icon(
                modifier = Modifier
                    .size(50.dp)
                    .padding(bottom = 4.dp),
                tint = MaterialTheme.colorScheme.primaryContainer,
                imageVector = Icons.Default.Person, contentDescription = null)

            OutlinedTextField(
                modifier = Modifier.padding(bottom = 4.dp),
                value = clientData.name,
                onValueChange = { onClientDataChange(clientData.copy(name = it)) },
                label = { Text(text = "Nom") },
                enabled = editable,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Next,
                    capitalization = KeyboardCapitalization.Words
                ),
                keyboardActions = KeyboardActions(onNext = { focusManager.moveFocus(FocusDirection.Down) })
            )
            OutlinedTextField(
                modifier = Modifier.padding(bottom = 4.dp),
                value = clientData.tel,
                onValueChange = { onClientDataChange(clientData.copy(tel = it)) },
                label = { Text(text = "Tel") },
                enabled = editable,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Next,
                    keyboardType = KeyboardType.Phone
                ),
                keyboardActions = KeyboardActions(onNext = { focusManager.moveFocus(FocusDirection.Down) })
            )
            OutlinedTextField(
                modifier = Modifier.padding(bottom = 4.dp),
                value = clientData.country,
                onValueChange = { onClientDataChange(clientData.copy(country = it)) },
                label = { Text(text = "Pays") },
                enabled = editable,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Next,
                    capitalization = KeyboardCapitalization.Sentences
                ),
                keyboardActions = KeyboardActions(onNext = { focusManager.moveFocus(FocusDirection.Down) })
            )
            OutlinedTextField(
                modifier = Modifier.padding(bottom = 4.dp),
                value = clientData.city,
                onValueChange = { onClientDataChange(clientData.copy(city = it)) },
                label = { Text(text = "Ville") },
                enabled = editable,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Done,
                    capitalization = KeyboardCapitalization.Sentences
                ),
                keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() })
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun JACEditClientDataPreview(){
    JACTheme {
        JACEditClientData(
            modifier = Modifier.fillMaxWidth(),
            clientData = ClientData(),
            onClientDataChange = {},
        )
    }
}