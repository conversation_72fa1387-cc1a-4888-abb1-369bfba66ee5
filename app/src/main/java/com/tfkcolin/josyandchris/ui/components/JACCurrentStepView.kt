package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Divider
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACCurrentStepView(
    modifier: Modifier = Modifier,
    step: CommandStep
){
    Box(modifier = modifier){
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Divider(modifier = Modifier.width(15.dp), color = MaterialTheme.colorScheme.tertiary)
                Text(
                    modifier = Modifier.padding(3.dp),
                    text = step.step
                )
                Divider(modifier = Modifier.width(15.dp), color = MaterialTheme.colorScheme.tertiary)
            }
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth(),
                progress = step.ordinal.toFloat() / (CommandStep.values().size - 1),
                color = MaterialTheme.colorScheme.tertiary,
                trackColor = MaterialTheme.colorScheme.tertiaryContainer
            )
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
fun CurrentStepViewPreview(){
    JACTheme {
        Column {
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.RECORD)
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.BUYING)
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.RECEIVED)
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.DELIVERED)
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.READY)
            JACCurrentStepView(modifier = Modifier.padding(bottom = 2.dp), step = CommandStep.OK)
        }
    }
}