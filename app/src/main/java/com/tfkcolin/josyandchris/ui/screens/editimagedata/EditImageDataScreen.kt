package com.tfkcolin.josyandchris.ui.screens.editimagedata

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.ThumbUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.components.JACImageDataCreator
import com.tfkcolin.josyandchris.ui.components.JACTopAppBar
import com.tfkcolin.josyandchris.ui.components.Title
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditImageDataScreen(
    modifier: Modifier = Modifier,
    imageData: ImageData,
    onImageDataChanged: (ImageData) -> Unit,
    onUpdate: () -> Unit,
    onDelete: () -> Unit,
    errorMessage: String?,
    onDismissError: () -> Unit,
    isLoading: Boolean
) {
    var showDeleteDialog by remember { mutableStateOf(false) }

    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                actions = {
                    IconButton(onClick = { showDeleteDialog = true }) {
                        Icon(imageVector = Icons.Default.Delete, contentDescription = "Delete")
                    }
                },
                title = { Text(text = "Modifier l'image") }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            LazyColumn(
                modifier = Modifier
                    .align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                item {
                    Title(
                        text = imageData.category.ifEmpty { "Modifier" },
                        color = MaterialTheme.colorScheme.primary
                    )
                    JACImageDataCreator(
                        modifier = Modifier
                            .padding(4.dp)
                            .fillMaxWidth(),
                        imageData = imageData,
                        onImageDataChange = onImageDataChanged,
                        enablePickImage = false,
                        errorMessage = errorMessage,
                        onDismissError = onDismissError
                    )
                    Button(
                        onClick = { onUpdate() },
                        enabled = !isLoading
                    ) {
                        Icon(
                            modifier = Modifier.padding(end = 5.dp),
                            imageVector = Icons.Default.ThumbUp,
                            contentDescription = null
                        )
                        Text(text = "Sauvegarder les modifications")
                    }
                }
            }
            if (isLoading) {
                CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
            }
            if (showDeleteDialog) {
                AlertDialog(
                    onDismissRequest = { showDeleteDialog = false },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                onDelete()
                                showDeleteDialog = false
                            }
                        ) {
                            Text(text = "Oui")
                        }
                    },
                    dismissButton = {
                        TextButton(onClick = { showDeleteDialog = false }) {
                            Text(text = "Non")
                        }
                    },
                    title = { Text(text = "Supprimer l'image") },
                    text = { Text(text = "Voulez-vous vraiment supprimer cette image? Cette action est irréversible.") }
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun EditImageDataScreenPreview(){
    JACTheme {
        EditImageDataScreen(
            imageData = ImageData(),
            onImageDataChanged = {},
            onUpdate = {},
            onDelete = {},
            errorMessage = null,
            onDismissError = {},
            isLoading = false
        )
    }
}
