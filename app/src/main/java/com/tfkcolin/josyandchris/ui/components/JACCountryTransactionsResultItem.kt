package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.formatToPrice
import com.tfkcolin.josyandchris.util.lighten

/**
 * transaction should be filtered to only those with the selected country name
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCountryTransactionsResultItem(
    modifier: Modifier = Modifier,
    countryData: CountryData,
    transactions: List<FinancialTransaction>,
    onClick: () -> Unit,
    onRemove: () -> Unit
){
    val diff = remember(transactions) {
        transactions
            .filter { it.transactionTypeIndex == TransactionType.INPUT.ordinal }
            .sumOf { it.price } - transactions
            .filter { it.transactionTypeIndex == TransactionType.OUTPUT.ordinal }
            .sumOf { it.price }
    }
    Card(
        modifier = modifier,
        onClick = onClick,
        shape = RectangleShape,
        elevation = CardDefaults.cardElevation(
            defaultElevation = 5.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        0f to MaterialTheme.colorScheme.primary.copy(alpha = .1f),
                        .5f to Color.White,
                        1f to MaterialTheme.colorScheme.secondary.copy(alpha = .1f)
                    )
                )
                .padding(5.dp)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            )  {
                Text(
                    text = countryData.name,
                    style = MaterialTheme.typography.headlineMedium,
                    color = Color.Black.lighten(.26f),
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.weight(1f))
                OutlinedIconButton(
                    onClick = onRemove,
                    colors = IconButtonDefaults.outlinedIconButtonColors(
                        contentColor = Color.Red.lighten(.25f),
                    ),
                    border = BorderStroke(1.dp, Color.Red.lighten(.25f))
                ) {
                    Icon(imageVector = Icons.Sharp.Delete, contentDescription = null)
                }
            }
            Divider(modifier = Modifier.padding(vertical = 10.dp))
            Row(
                modifier = Modifier.padding(vertical = 15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${diff.formatToPrice()} FCFA",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black.lighten(.3f)
                )
                Spacer(modifier = Modifier.weight(1f))
                Icon(
                    modifier = Modifier.sizeIn(minHeight = 30.dp, minWidth = 50.dp),
                    painter = painterResource(
                        id = when  {
                            diff < 0 -> R.drawable.ic_baseline_call_received_24
                            else -> R.drawable.ic_baseline_call_made_24
                        }
                    ),
                    contentDescription = null,
                    tint = when {
                        diff < 0 -> Color.Red.lighten(.2f)
                        else -> Color.Green.lighten(.2f)
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACCountryTransactionsResultItemPreview(){
    JACTheme {
        JACCountryTransactionsResultItem(
            modifier = Modifier
                .padding(5.dp)
                .wrapContentHeight(),
            countryData = CountryData(name = "Cameroun", devise = "fcfa"),
            transactions = listOf(
                FinancialTransaction(
                    id = "0",
                    label = "Achat de marchandise",
                    price = 50000,
                    country = "cameroun",
                    marked = false,
                    transactionTypeIndex = TransactionType.INPUT.ordinal
                ),
                FinancialTransaction(
                    id = "1",
                    label = "Paiement du loyer de la boutique",
                    price = 10000,
                    country = "cameroun",
                    marked = true,
                    transactionTypeIndex = TransactionType.OUTPUT.ordinal
                ),
            ),
            onClick = {},
            onRemove = {}
        )
    }
}