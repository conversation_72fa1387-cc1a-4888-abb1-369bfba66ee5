package com.tfkcolin.josyandchris.ui.screens.cargodetails

import androidx.compose.runtime.*
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.tfkcolin.josyandchris.ui.base.BaseViewModel
import com.google.android.gms.tasks.Tasks
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.repository.CargoRepository
import com.tfkcolin.josyandchris.repository.ShipmentRepository
import timber.log.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class CargoDetailsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @Named("CARGO_DB") private val cargoDB: CollectionReference,
    @Named("SHIPMENT_DB") private val shipmentDB: CollectionReference
) : BaseViewModel() {
    val cargoId: String = CargoArgs(savedStateHandle).id
    val isCreate = CargoArgs(savedStateHandle).isCreate

    private val cargoRef: MutableState<DocumentReference> = mutableStateOf(
        if (!isCreate) cargoDB.document(cargoId)
        else cargoDB.document()
    )

    private val _changeMade = mutableStateOf(false)
    val changeMade = _changeMade as State<Boolean>
    
    // Real-time listeners state for selective listening
    private val _currentCargo = MutableStateFlow<Cargo?>(null)
    val currentCargo: StateFlow<Cargo?> = _currentCargo.asStateFlow()
    
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null

    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
        Timber.i("CargoDetailsViewModel cleared")
    }

    fun setChangeMade(value: Boolean) {
        _changeMade.value = value
    }

    fun saveCargo(cargo: Cargo) {
        launchWithErrorHandling {
            // Create a search index for better filtering and searching
            val searchIndex = listOf(
                cargo.origin.lowercase(),
                cargo.destination.lowercase(),
                CargoStatus.entries[cargo.statusIndex].label.lowercase()
            )
            val updatedCargo = cargo.copy(searchIndex = searchIndex)

            executeSafely(
                block = {
                    Tasks.await(cargoRef.value.set(updatedCargo), 15, TimeUnit.MINUTES)
                    if (isCreate) {
                        cargoRef.value = cargoDB.document()
                    }
                    Result.success(Unit)
                },
                onSuccess = {
                    showSuccess(if (isCreate) "Cargo created successfully" else "Cargo updated successfully")
                    setChangeMade(false)
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to save cargo")
                    handleError(exception)
                }
            )
        }
    }

    fun addShipment(shipment: Shipment) {
        launchWithErrorHandling {
            executeSafely(
                block = {
                    val ref = shipmentDB.document()
                    Tasks.await(ref.set(shipment.copy(id = ref.id)), 15, TimeUnit.MINUTES)
                    Result.success(Unit)
                },
                onSuccess = {
                    showSuccess("Shipment added successfully")
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to add shipment")
                    handleError(exception)
                }
            )
        }
    }

    fun updateShipment(shipment: Shipment) {
        launchWithErrorHandling {
            executeSafely(
                block = {
                    Tasks.await(shipmentDB.document(shipment.id).set(shipment), 15, TimeUnit.MINUTES)
                    Result.success(Unit)
                },
                onSuccess = {
                    showSuccess("Shipment updated successfully")
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to update shipment")
                    handleError(exception)
                }
            )
        }
    }

    fun deleteShipment(shipmentId: String) {
        launchWithErrorHandling {
            executeSafely(
                block = {
                    Tasks.await(shipmentDB.document(shipmentId).delete(), 15, TimeUnit.MINUTES)
                    Result.success(Unit)
                },
                onSuccess = {
                    showSuccess("Shipment deleted successfully")
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to delete shipment")
                    handleError(exception)
                }
            )
        }
    }

    fun deleteCargo() {
        launchWithErrorHandling {
            executeSafely(
                block = {
                    // First, get all shipments for this cargo
                    val shipmentSnapshot = Tasks.await(shipmentDB.whereEqualTo("cargoId", cargoId).get())

                    // Delete all shipments first
                    for (doc in shipmentSnapshot.documents) {
                        Tasks.await(shipmentDB.document(doc.id).delete())
                    }

                    // Then delete the cargo itself
                    Tasks.await(cargoRef.value.delete())
                    Result.success(Unit)
                },
                onSuccess = {
                    showSuccess("Cargo and all associated shipments deleted successfully")
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to delete cargo")
                    handleError(exception)
                }
            )
        }
    }
    
    // ===== SELECTIVE REAL-TIME LISTENERS =====
    
    /**
     * Start listening for real-time updates to this specific cargo
     * This should be called when user opens the detail screen
     */
    fun startLiveUpdates() {
        if (_isLiveMode.value || isCreate) return // Already listening or creating new cargo
        
        _isLiveMode.value = true
        
        // Stop any existing live updates job
        liveUpdatesJob?.cancel()
        
        // Start listening for updates to this specific cargo document
        liveUpdatesJob = viewModelScope.launch {
            try {
                cargoRef.value.addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Timber.e(error, "Error listening for cargo updates")
                        handleError(error)
                        return@addSnapshotListener
                    }
                    
                    snapshot?.let { doc ->
                        if (doc.exists()) {
                            val cargo = doc.toObject(Cargo::class.java)?.copy(id = doc.id)
                            _currentCargo.value = cargo
                        } else {
                            _currentCargo.value = null
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to start listening for cargo updates")
                handleError(e)
            }
        }
    }
    
    /**
     * Stop listening for real-time updates
     * This should be called when user leaves detail screen
     */
    fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
    }
    
    /**
     * Toggle live mode on/off
     */
    fun toggleLiveMode() {
        if (_isLiveMode.value) {
            stopLiveUpdates()
        } else {
            startLiveUpdates()
        }
    }
    
    /**
     * Check if live mode is enabled
     */
    fun isLiveModeEnabled(): Boolean = _isLiveMode.value
    
    /**
     * Get the current cargo from real-time updates or fetch once
     */
    suspend fun getCurrentCargo(): Cargo? {
        return if (_isLiveMode.value) {
            _currentCargo.value
        } else {
            var cargo: Cargo? = null
            executeSafely(
                block = {
                    val snapshot = Tasks.await(cargoRef.value.get(), 15, TimeUnit.SECONDS)
                    cargo = snapshot.toObject(Cargo::class.java)?.copy(id = snapshot.id)
                    Result.success(Unit)
                },
                onError = { exception ->
                    Timber.e(exception, "Failed to fetch cargo")
                    handleError(exception)
                }
            )
            cargo
        }
    }
}

class CargoArgs(savedStateHandle: SavedStateHandle) {
    val id: String = savedStateHandle[cargoIdArgs] ?: ""
    val isCreate: Boolean = savedStateHandle[isCreateArgs] ?: true
}
