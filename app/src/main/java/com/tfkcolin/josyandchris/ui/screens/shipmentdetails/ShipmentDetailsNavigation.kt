package com.tfkcolin.josyandchris.ui.screens.shipmentdetails

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState

const val shipmentIdArgs = "shipmentId"
const val cargoIdArgs = "cargoId"

fun NavGraphBuilder.shipmentDetailsScreen(
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onTitleDescriptionChange: (String) -> Unit
) {
    composable(
        route = "${Screen.ShipmentDetailsScreen.route}?$shipmentIdArgs={$shipmentIdArgs}&$cargoIdArgs={$cargoIdArgs}",
        arguments = listOf(
            navArgument(shipmentIdArgs) {
                nullable = true
                defaultValue = null
                type = NavType.StringType
            },
            navArgument(cargoIdArgs) {
                type = NavType.StringType
            }
        )
    ) {
        ShipmentDetailsScreen(
            setHideAppbar = setHideAppbar,
            role = role,
            trySuspendFunction = trySuspendFunction,
            onShowSnackbar = onShowSnackbar,
            onTitleDescriptionChange = onTitleDescriptionChange
        )
    }
}

fun NavController.navigateToShipmentDetailsScreen(
    id: String? = null,
    cargoId: String,
    navOptions: NavOptionsBuilder.() -> Unit = {}
) {
    this.navigate(
        "${Screen.ShipmentDetailsScreen.route}?$shipmentIdArgs=$id&$cargoIdArgs=$cargoId",
        navOptions
    )
}
