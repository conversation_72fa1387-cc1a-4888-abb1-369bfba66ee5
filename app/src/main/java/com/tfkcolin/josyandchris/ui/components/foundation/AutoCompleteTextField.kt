package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.window.PopupProperties
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*

@OptIn(FlowPreview::class)
@Composable
fun AutoCompleteTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    suggestions: List<String>,
    onSuggestionSelected: (String) -> Unit,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    enabled: Boolean = true,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    keyboardOptions: KeyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    debounceTime: Long = 300L
) {
    var textFieldSize by remember { mutableStateOf(Size.Zero) }
    var expanded by remember { mutableStateOf(false) }
    var filteredSuggestions by remember { mutableStateOf(suggestions) }
    
    // Debounced text input using optimized search state
    val searchState = remember { 
        com.tfkcolin.josyandchris.util.DebouncedSearchState(
            initialQuery = value, 
            debounceTimeMs = debounceTime
        )
    }
    
    // Update search state when value changes
    LaunchedEffect(value) {
        if (searchState.searchQuery.value != value) {
            searchState.updateQuery(value)
        }
    }
    
    val debouncedValue by searchState.debouncedSearchQuery.collectAsState(initial = value)
    
    // Filter suggestions based on debounced input
    LaunchedEffect(debouncedValue, suggestions) {
        val searchTerm = debouncedValue.lowercase()
        filteredSuggestions = if (searchTerm.isEmpty()) {
            suggestions
        } else {
            suggestions.filter { it.lowercase().contains(searchTerm) }
        }
        expanded = filteredSuggestions.isNotEmpty() && debouncedValue.isNotEmpty()
    }
    
    Box(modifier = modifier) {
        OutlinedTextField(
            modifier = Modifier
                .fillMaxWidth()
                .onGloballyPositioned { coordinates ->
                    textFieldSize = coordinates.size.toSize()
                },
            value = value,
            onValueChange = { newValue ->
                onValueChange(newValue)
                expanded = newValue.isNotEmpty()
            },
            label = label,
            placeholder = placeholder,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            isError = isError,
            enabled = enabled,
            singleLine = singleLine,
            maxLines = maxLines,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions
        )
        
        // Dropdown suggestions
        if (expanded && filteredSuggestions.isNotEmpty()) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                properties = PopupProperties(focusable = false),
                modifier = Modifier
                    .width(with(LocalDensity.current) { textFieldSize.width.toDp() })
                    .heightIn(max = 200.dp)
            ) {
                LazyColumn {
                    items(filteredSuggestions) { suggestion ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = suggestion,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            },
                            onClick = {
                                onSuggestionSelected(suggestion)
                                onValueChange(suggestion)
                                expanded = false
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AutoCompleteTextFieldPreview() {
    var text by remember { mutableStateOf("") }
    val suggestions = listOf("John Doe", "Jane Smith", "Alice Johnson", "Bob Wilson")
    
    JACTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            AutoCompleteTextField(
                value = text,
                onValueChange = { text = it },
                suggestions = suggestions,
                onSuggestionSelected = { text = it },
                label = { Text("Client Name") },
                placeholder = { Text("Enter client name...") }
            )
        }
    }
}
