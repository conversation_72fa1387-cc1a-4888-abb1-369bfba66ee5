package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACAvatar(
    modifier: Modifier = Modifier,
    image: String?
) {
    Box(modifier = modifier){
        Surface(
            modifier = Modifier.fillMaxSize(),
            shape = CircleShape,
            color = MaterialTheme.colorScheme.background,
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
        ) {
            AsyncImage(
                model = ImageRequest
                    .Builder(LocalContext.current)
                    .data(image)
                    .error(R.drawable.ic_baseline_error_24)
                    .placeholder(R.drawable.ic_launcher_foreground)
                    .build(),
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
private fun JACAvatarPreview(){
    JACTheme {
        JACAvatar(
            modifier = Modifier.size(30.dp),
            image = ""
        )
    }
}