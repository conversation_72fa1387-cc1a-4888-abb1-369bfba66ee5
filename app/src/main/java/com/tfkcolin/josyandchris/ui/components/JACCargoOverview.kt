package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCargoOverview(
    modifier: Modifier = Modifier,
    cargoCountsByStatus: Map<CargoStatus, Int>,
    onCargoStatusClicked: (Int) -> Unit
) {
    val cargoMap = remember(cargoCountsByStatus) {
        cargoCountsByStatus
    }
    
    Box(modifier = modifier) {
        Column(modifier = Modifier.align(Alignment.Center)) {
            Title(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 5.dp),
                text = "Résumé des cargos",
                color = Color.Black.lighten(.2f),
            )
            VerticalGrid(modifier = Modifier.padding(top = 10.dp)) {
                CargoStatus.values().forEach { status ->
                    OutlinedCard(
                        modifier = Modifier
                            .padding(2.dp),
                        onClick = { onCargoStatusClicked(status.ordinal) },
                        shape = RectangleShape,
                        elevation = CardDefaults.outlinedCardElevation(
                            defaultElevation = 5.dp
                        ),
                        colors = CardDefaults.outlinedCardColors(
                            containerColor = Color.White
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(5.dp)
                                .fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Display status icon and label
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(
                                    painter = painterResource(id = status.iconResId),
                                    contentDescription = status.label,
                                    tint = MaterialTheme.colorScheme.primary
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = status.label,
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                            // Display cargo count with proper pluralization
                            val count = cargoMap[status] ?: 0
                            Text(
                                text = "$count cargo${if (count > 1) "s" else ""}",
                                style = MaterialTheme.typography.headlineMedium,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun CargoOverviewPreview() {
    JACTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            JACCargoOverview(
                Modifier
                    .fillMaxHeight(.5f)
                    .align(Alignment.Center),
                cargoCountsByStatus = mapOf(
                    CargoStatus.LOADING to 1,
                    CargoStatus.IN_TRANSIT to 1,
                    CargoStatus.ARRIVED to 1,
                    CargoStatus.UNLOADING to 2,
                    CargoStatus.COMPLETED to 2
                ),
                onCargoStatusClicked = {}
            )
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun CargoOverviewSamplePreview() {
    JACTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            JACCargoOverview(
                Modifier
                    .fillMaxHeight(.5f)
                    .align(Alignment.Center),
                cargoCountsByStatus = mapOf(
                    CargoStatus.LOADING to 3,     // 0→3
                    CargoStatus.IN_TRANSIT to 5,  // 1→5
                    CargoStatus.ARRIVED to 2,     // 2→2
                    CargoStatus.UNLOADING to 1,   // 3→1
                    CargoStatus.COMPLETED to 4    // 4→4
                ),
                onCargoStatusClicked = {}
            )
        }
    }
}
