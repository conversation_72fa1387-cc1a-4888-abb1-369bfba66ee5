package com.tfkcolin.josyandchris.ui.screens.commandlist

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.cache.database.CacheManager
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.repository.CommandRepository
import com.tfkcolin.josyandchris.ui.data.CommandUiFilterState
import com.tfkcolin.josyandchris.util.DebouncedSearchState
import com.tfkcolin.josyandchris.util.query.DateRange
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*
import javax.inject.Inject

/**
 * Optimized CommandListViewModel with debounced search and caching
 * Reduces repository calls and improves performance with intelligent caching
 */
@OptIn(FlowPreview::class)
@HiltViewModel
class CommandListViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val commandRepository: CommandRepository,
    private val cacheManager: CacheManager
) : ViewModel() {
    
    // Use the new CommandArgs from CommandListNavigation.kt
    private val commandArgs = CommandArgs(savedStateHandle)
    private val initialStep = commandArgs.preAppliedStepFilter
    
    // Debounced search state for efficient searching
    private val searchState = DebouncedSearchState(
        initialQuery = commandArgs.clientFilter ?: "", // Initialize with client filter if present
        debounceTimeMs = 500L // Wait 500ms after user stops typing
    )
    
    // UI filter state management - using CommandUiFilterState
    private val _uiFilterState = MutableStateFlow(CommandUiFilterState(selectedStep = initialStep))
    val uiFilterState: StateFlow<CommandUiFilterState> = _uiFilterState.asStateFlow()
    
    // Expose search query for UI binding
    val searchQuery: StateFlow<String> = searchState.searchQuery
    
    // Combine debounced search with other filters
    private val combinedFilters = combine(
        searchState.debouncedSearchQuery,
        _uiFilterState
    ) { debouncedQuery, filterState ->
        Timber.d("Search debounced: '$debouncedQuery', Filters: $filterState")
        
        // Update filter state with debounced search query
        val updatedFilterState = filterState.copy(searchTerm = debouncedQuery)
        
        // Cache the search term if it's valid
        if (debouncedQuery.isNotEmpty() && debouncedQuery.length >= 2) {
            viewModelScope.launch {
                try {
                    cacheManager.searchHistoryDao.insertOrUpdateSearch(
                        com.tfkcolin.josyandchris.cache.database.entity.CachedSearchHistory(
                            searchTerm = debouncedQuery,
                            searchType = "command"
                        )
                    )
                } catch (e: Exception) {
                    Timber.e(e, "Error caching search term: $debouncedQuery")
                }
            }
        }
        
        updatedFilterState.toQueryFilters()
    }
    .distinctUntilChanged()
    
    // Pagination data with debounced search and caching
    val commandsPagingData: Flow<PagingData<Command>> = combinedFilters
        .flatMapLatest { filters ->
            Timber.d("Fetching commands with filters: $filters")
            commandRepository.getCommandsPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)
    
    // Search suggestions flow from cache
    val searchSuggestions: Flow<List<String>> = searchQuery
        .debounce(300L)
        .filter { it.isNotEmpty() && it.length >= 2 }
        .distinctUntilChanged()
        .flatMapLatest { query ->
            flow {
                try {
                    val suggestions = cacheManager.searchHistoryDao.getSuggestedSearchTerms("command", query, 5)
                    emit(suggestions)
                } catch (e: Exception) {
                    Timber.e(e, "Error fetching search suggestions")
                    emit(emptyList<String>())
                }
            }
        }
        .catch { 
            Timber.e(it, "Error in search suggestions flow")
            emit(emptyList()) 
        }
    
    /**
     * Update search query with debouncing
     */
    fun updateSearchQuery(query: String) {
        Timber.d("Search query updated: '$query'")
        searchState.updateQuery(query)
    }
    
    /**
     * Clear search query
     */
    fun clearSearch() {
        searchState.clearQuery()
    }
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: CommandUiFilterState) {
        _uiFilterState.value = filterState
    }
    
    /**
     * Update selected step filter
     */
    fun updateSelectedStep(step: CommandStep?) {
        val currentState = _uiFilterState.value
        _uiFilterState.value = currentState.copy(selectedStep = step)
    }
    
    /**
     * Update filters with date range
     */
    fun updateFiltersWithDateRange(startDate: Date, endDate: Date) {
        val currentState = _uiFilterState.value
        _uiFilterState.value = currentState.copy(dateRange = DateRange(startDate, endDate))
    }
    
    /**
     * Update filters with payment proof status
     */
    fun updateFiltersWithPaymentProof(proofUploaded: Boolean) {
        val currentState = _uiFilterState.value
        _uiFilterState.value = currentState.copy(proofUploaded = proofUploaded)
    }
    
    /**
     * Get commands by date range using QueryBuilder
     */
    fun getCommandsByDateRange(startDate: Date, endDate: Date) {
        updateFiltersWithDateRange(startDate, endDate)
    }
    
    /**
     * Get today's commands
     */
    fun getTodaysCommands() {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.time
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val startOfNextDay = calendar.time
        
        getCommandsByDateRange(startOfDay, startOfNextDay)
    }
    
    /**
     * Get commands with payment proof filter using QueryBuilder
     */
    fun getCommandsWithPaymentProof(proofUploaded: Boolean) {
        updateFiltersWithPaymentProof(proofUploaded)
    }
    
    /**
     * Perform cache cleanup periodically
     */
    fun performCacheCleanup() {
        viewModelScope.launch {
            try {
                cacheManager.performCacheCleanup()
                Timber.d("Cache cleanup completed successfully")
            } catch (e: Exception) {
                Timber.e(e, "Error during cache cleanup")
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        Timber.d("OptimizedCommandListViewModel cleared")
    }
}
