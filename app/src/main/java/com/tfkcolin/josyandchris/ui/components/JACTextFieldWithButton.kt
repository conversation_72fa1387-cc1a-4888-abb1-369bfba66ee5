package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACTextFieldWithButton(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    onClick: () -> Unit
) {
    Box(modifier = modifier){
        TextField(
            modifier = Modifier
                .heightIn(min = TextFieldDefaults.MinHeight * 3),
            value = value,
            onValueChange = onValueChange,
            trailingIcon = {
                FilledTonalIconButton(onClick = onClick) {
                    Icon(imageVector = Icons.Filled.AddCircle, contentDescription = null)
                }
            }
        )
    }
}

@Preview
@Composable
private fun JACTextFieldWithButtonPreview(){
    JACTheme {
        Surface(color = MaterialTheme.colorScheme.background) {
            JACTextFieldWithButton(
                value = "",
                onValueChange = {},
                onClick = {}
            )
        }
    }
}