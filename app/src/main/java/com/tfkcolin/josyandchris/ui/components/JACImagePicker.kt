package com.tfkcolin.josyandchris.ui.components

import android.net.Uri
import androidx.compose.foundation.layout.*
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.ui.components.foundation.ImagePickerButton

@Composable
fun JACImagePicker(
    modifier: Modifier = Modifier,
    imageUrl: String?,
    onPickImage: (Uri) -> Unit
){
    Column(
        modifier = modifier
            .sizeIn(minWidth = 100.dp, minHeight = 100.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        OutlinedCard(
            modifier = Modifier
                .padding(bottom = 4.dp)
                .fillMaxWidth(.8f),
        ) {
            JACImage(
                modifier = Modifier.sizeIn(minHeight = 100.dp, minWidth = 100.dp),
                data = imageUrl
            )
        }
        ImagePickerButton(onPickImage = onPickImage)
    }
}