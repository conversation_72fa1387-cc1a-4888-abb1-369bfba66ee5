package com.tfkcolin.josyandchris.ui.screens.loadingscreen

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.tfkcolin.josyandchris.ui.data.Screen

fun NavGraphBuilder.loadingScreen() {
    composable(route = Screen.LoadingScreen.route) {
        JACLoadingScreen(modifier = Modifier.fillMaxSize())
    }
}
