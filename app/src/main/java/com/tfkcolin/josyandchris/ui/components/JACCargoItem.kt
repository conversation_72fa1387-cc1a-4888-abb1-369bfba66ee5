package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import java.text.DateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCargoItem(
    modifier: Modifier = Modifier,
    cargo: Cargo,
    onClick: () -> Unit
) {
    val created = remember {
        val format = DateFormat.getInstance()
        format.format(cargo.created)
    }

    Box(modifier = modifier) {
        OutlinedCard(
            modifier = Modifier.fillMaxSize(),
            onClick = onClick,
            shape = RoundedCornerShape(8.dp),
            border = BorderStroke(
                width = 1.dp,
                color = MaterialTheme.colorScheme.primary
            ),
            elevation = CardDefaults.outlinedCardElevation(
                defaultElevation = 2.dp
            ),
            colors = CardDefaults.outlinedCardColors(
                containerColor = when(cargo.statusIndex) {
                    CargoStatus.LOADING.ordinal -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                    CargoStatus.IN_TRANSIT.ordinal -> MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                    CargoStatus.ARRIVED.ordinal -> MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.3f)
                    CargoStatus.UNLOADING.ordinal -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                    CargoStatus.COMPLETED.ordinal -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    else -> MaterialTheme.colorScheme.surface
                }
            )
        ) {
            Column(
                modifier = Modifier.padding(4.dp)
            ) {
                // Status indicator at the top
                JACCargoStatusView(
                    modifier = Modifier
                        .align(Alignment.End)
                        .padding(4.dp),
                    status = CargoStatus.values()[cargo.statusIndex]
                )

                Row(
                    modifier = Modifier.padding(start = 8.dp, end = 8.dp, top = 8.dp, bottom = 4.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Origin: ${cargo.origin}",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "Destination: ${cargo.destination}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    Text(
                        modifier = Modifier.padding(start = 8.dp),
                        text = created,
                        style = MaterialTheme.typography.labelMedium,
                        textAlign = TextAlign.End,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .6f)
                    )
                }
                // Divider
                Divider(modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp))

                // Display dates in a more compact format
                Row(modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)) {
                    Column(modifier = Modifier.weight(1f)) {
                        cargo.departureDate?.let {
                            val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM)
                            Text(
                                text = "Departure: ${dateFormat.format(it)}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }

                    Column(modifier = Modifier.weight(1f)) {
                        cargo.estimatedArrivalDate?.let {
                            val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM)
                            Text(
                                text = "Est. Arrival: ${dateFormat.format(it)}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }

                        cargo.actualArrivalDate?.let {
                            val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM)
                            Text(
                                text = "Actual: ${dateFormat.format(it)}",
                                style = MaterialTheme.typography.bodySmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun JACCargoItemPreview() {
    JACTheme {
        JACCargoItem(
            cargo = Cargo(
                origin = "Yaoundé",
                destination = "Paris",
                statusIndex = CargoStatus.IN_TRANSIT.ordinal,
                departureDate = Calendar.getInstance().time,
                estimatedArrivalDate = Calendar.getInstance().apply { add(Calendar.DAY_OF_MONTH, 14) }.time
            ),
            onClick = {}
        )
    }
}
