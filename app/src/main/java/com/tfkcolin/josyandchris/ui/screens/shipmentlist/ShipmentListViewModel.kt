package com.tfkcolin.josyandchris.ui.screens.shipmentlist

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.repository.ShipmentRepository
import com.tfkcolin.josyandchris.ui.data.ShipmentUiFilterState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ShipmentListViewModel @Inject constructor(
    private val shipmentRepository: ShipmentRepository
): ViewModel() {
    
    // UI filter state management
    private val _uiFilterState = MutableStateFlow(ShipmentUiFilterState())
    val uiFilterState: StateFlow<ShipmentUiFilterState> = _uiFilterState.asStateFlow()
    
    // Real-time listeners state for selective listening
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null
    
    // Pagination data - this is the main source of truth for paginated shipments
    val shipmentsPagingData: Flow<PagingData<Shipment>> = _uiFilterState
        .flatMapLatest { filterState ->
            shipmentRepository.getShipmentsPagingDataWithFilters(filterState.toQueryFilters())
        }
        .cachedIn(viewModelScope)
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: ShipmentUiFilterState) {
        _uiFilterState.value = filterState
    }
    
    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
        Log.i("ShipmentListVM", "ShipmentListViewModel cleared")
    }
    
    /**
     * Start listening for real-time updates
     */
    private fun startLiveUpdates() {
        if (_isLiveMode.value) return // Already listening
        
        _isLiveMode.value = true
        Log.i("ShipmentListVM", "Starting live updates for shipment list")
        
        // Cancel any existing live updates job
        liveUpdatesJob?.cancel()
        
        // Here you would implement real-time listening logic
        // For now, we'll just log the state change
        liveUpdatesJob = viewModelScope.launch {
            Log.i("ShipmentListVM", "Live updates started for shipment list")
        }
    }
    
    /**
     * Stop listening for real-time updates
     */
    private fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
        Log.i("ShipmentListVM", "Live updates stopped for shipment list")
    }
    
    /**
     * Toggle live mode on/off
     */
    fun toggleLiveMode() {
        if (_isLiveMode.value) {
            stopLiveUpdates()
        } else {
            startLiveUpdates()
        }
    }
}
