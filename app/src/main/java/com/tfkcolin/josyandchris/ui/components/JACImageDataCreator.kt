package com.tfkcolin.josyandchris.ui.components

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.insets.imePadding
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.dashedBackground
import com.tfkcolin.josyandchris.util.getFilename
import android.net.Uri

@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class)
@Composable
fun JACImageDataCreator(
    modifier: Modifier = Modifier,
    imageData: ImageData,
    enablePickImage: Boolean = true,
    onImageDataChange: (ImageData) -> Unit,
    onImageReplace: ((Uri) -> Unit)? = null,
    errorMessage: String?,
    onDismissError: () -> Unit
) {
    val context = LocalContext.current
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent(),
        onResult = { uri ->
            uri?.let {
                onImageDataChange(
                    imageData
                        .copy(
                            url = uri.toString(),
                            name = context.getFilename(uri)
                        )
                )
            }
        }
    )
    val genreFocus = remember { FocusRequester() }
    val keyboard = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Box(
            modifier = Modifier
                .padding(2.dp)
                .fillMaxWidth(.8f)
                .heightIn(max = 400.dp)
        ) {
            OutlinedCard(
                modifier = Modifier
                    .padding(bottom = 4.dp)
                    .fillMaxWidth()
                    .then(dashedBackground),
                onClick = { 
                    if (onImageReplace != null && imageData.url != null) {
                        // If we have an existing image and a replace handler, use that
                        imagePicker.launch("image/*")
                    } else {
                        imagePicker.launch("image/*")
                    }
                },
                border = BorderStroke(0.dp, Color.Transparent),
                enabled = enablePickImage
            ) {
                Crossfade(targetState = imageData.url != null) {
                    if(it){
                        JACImage(
                            data = imageData.url
                        )
                    } else {
                        Icon(
                            modifier = Modifier.fillMaxSize(),
                            painter = painterResource(id = R.drawable.baseline_image_200),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primaryContainer
                        )
                    }
                }
            }
            Icon(
                modifier = Modifier
                    .padding(5.dp)
                    .align(Alignment.BottomEnd)
                    .size(50.dp),
                imageVector = Icons.Default.Add,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
        }
        Text(
            modifier = Modifier.padding(vertical = 5.dp),
            text = "Information sur l'image",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            //color = MaterialTheme.colorScheme.tertiary
        )
        AnimatedVisibility(visible = errorMessage != null) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .background(MaterialTheme.colorScheme.errorContainer, shape = MaterialTheme.shapes.medium)
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = errorMessage ?: "",
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.weight(1f)
                )
                IconButton(onClick = onDismissError) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Dismiss error",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
        OutlinedTextField(
            modifier = Modifier
                .padding(bottom = 4.dp)
                .imePadding(),
            value = imageData.category,
            onValueChange = {
                onImageDataChange(
                    imageData.copy(
                        category = it
                    )
                )
            },
            prefix = {
                Text(
                    modifier = Modifier.padding(end = 5.dp),
                    text = "Categorie",
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            },
            keyboardOptions = KeyboardOptions(
                capitalization = KeyboardCapitalization.Sentences,
                imeAction = ImeAction.Next
            ),
            keyboardActions = KeyboardActions(
                onNext = { genreFocus.requestFocus() }
            )
        )
        OutlinedTextField(
            modifier = Modifier
                .focusRequester(genreFocus)
                .imePadding(),
            value = imageData.genre,
            onValueChange = {
                onImageDataChange(
                    imageData.copy(
                        genre = it
                    )
                )
            },
            prefix = {
                Text(
                    modifier = Modifier.padding(end = 5.dp),
                    text = "Genre",
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            },
            keyboardOptions = KeyboardOptions(
                capitalization = KeyboardCapitalization.Sentences,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(onDone = { keyboard?.hide() })
        )
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACImageDataCreatorPreview(){
    JACTheme {
        Scaffold(
            topBar = {
                JACTopAppBar(onIconClick = { /*TODO*/ }, isLogIn = true) {}
            }
        ){
            JACImageDataCreator(
                modifier = Modifier
                    .padding(it)
                    .fillMaxWidth()
                    .padding(start = 5.dp, end = 5.dp, top = 5.dp),
                imageData = ImageData(
                    category = "Sac",
                    genre = "Homme"
                ),
                onImageDataChange = {},
                errorMessage = "This is a preview error message.",
                onDismissError = {}
            )
        }
    }
}
