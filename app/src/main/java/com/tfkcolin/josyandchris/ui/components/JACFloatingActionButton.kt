package com.tfkcolin.josyandchris.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACFloatingActionButton(
    modifier: Modifier = Modifier,
    icon: ImageVector = Icons.Filled.Add,
    onClick: () -> Unit ,
    text: String?
){
    ExtendedFloatingActionButton(
        modifier = modifier,
        text = { Text(text = text ?: "") },
        icon = {
            Icon(
                imageVector = icon,
                contentDescription = "click to add a new country to your accounting")
        },
        onClick = onClick,
        expanded = text != null
    )
}

@Preview(showBackground = true)
@Composable
fun JACFloatingActionButtonPreview() {
    JACTheme {
        JACFloatingActionButton(
            onClick = {},
            text = ""
        )
    }
}