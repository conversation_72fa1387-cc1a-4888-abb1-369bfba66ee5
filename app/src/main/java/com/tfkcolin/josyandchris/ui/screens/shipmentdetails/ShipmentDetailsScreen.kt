package com.tfkcolin.josyandchris.ui.screens.shipmentdetails

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.ShipmentProduct
import com.tfkcolin.josyandchris.data.ShipmentStatus
import com.tfkcolin.josyandchris.data.UserRole
import com.tfkcolin.josyandchris.ui.components.Title
import com.tfkcolin.josyandchris.ui.components.foundation.BoxWithLoading
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import kotlinx.coroutines.launch
import java.text.DateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShipmentDetailsScreen(
    modifier: Modifier = Modifier,
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onTitleDescriptionChange: (String) -> Unit
) {
    val viewModel: ShipmentDetailsViewModel = hiltViewModel()
    val scope = rememberCoroutineScope()
    
    // Load shipment from ViewModel
    val shipmentState by viewModel.shipmentState.collectAsState()
    val shipment = shipmentState ?: Shipment(cargoId = viewModel.cargoId)

    var editedShipment by remember(shipment) { mutableStateOf(shipment) }
    var loading by remember { mutableStateOf(false) }
    var showAddProductDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    LaunchedEffect(shipment) {
        onTitleDescriptionChange(
            if (viewModel.isCreate) "Nouvelle expédition" else "Expédition ${shipment.id}"
        )
    }

    // Add Product Dialog
    if (showAddProductDialog) {
        Dialog(onDismissRequest = { showAddProductDialog = false }) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Ajouter un produit",
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    OutlinedTextField(
                        value = viewModel.newProduct.name,
                        onValueChange = {
                            viewModel.updateNewProduct(viewModel.newProduct.copy(name = it))
                        },
                        label = { Text("Nom du produit") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = viewModel.newProduct.quantity.toString(),
                        onValueChange = {
                            val quantity = it.toIntOrNull() ?: 0
                            viewModel.updateNewProduct(viewModel.newProduct.copy(quantity = quantity))
                        },
                        label = { Text("Quantité") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = viewModel.newProduct.description,
                        onValueChange = {
                            viewModel.updateNewProduct(viewModel.newProduct.copy(description = it))
                        },
                        label = { Text("Description (optionnel)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = {
                                showAddProductDialog = false
                                viewModel.resetNewProduct()
                            }
                        ) {
                            Text("Annuler")
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (viewModel.newProduct.name.isNotBlank() && viewModel.newProduct.quantity > 0) {
                                    val updatedProducts = editedShipment.products.toMutableList()
                                    updatedProducts.add(viewModel.newProduct)
                                    editedShipment = editedShipment.copy(products = updatedProducts)
                                    viewModel.resetNewProduct()
                                    showAddProductDialog = false
                                }
                            }
                        ) {
                            Text("Ajouter")
                        }
                    }
                }
            }
        }
    }

    // Delete Confirmation Dialog
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Supprimer l'expédition") },
            text = { Text("Êtes-vous sûr de vouloir supprimer cette expédition? Cette action ne peut pas être annulée.") },
            confirmButton = {
                Button(
                    onClick = {
                        scope.launch {
                            loading = true
                            trySuspendFunction("Expédition supprimée avec succès") {
viewModel.deleteShipment()
                                onShowSnackbar(
                                    SnackbarState(
                                        message = "Expédition supprimée avec succès",
                                        type = SnackbarType.SUCCESS
                                    )
                                )
                                showDeleteConfirmation = false
                            }
                            loading = false
                        }
                    }
                ) {
                    Text("Supprimer")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text("Annuler")
                }
            }
        )
    }

    BoxWithLoading(
        modifier = modifier.fillMaxSize(),
        loading = loading
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp)
        ) {
            item {
                Title(
                    text = if (viewModel.isCreate) "Nouvelle expédition" else "Détails de l'expédition",
                    color = Color.Black.lighten(.2f)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Client Name field
                OutlinedTextField(
                    value = editedShipment.clientName,
                    onValueChange = { editedShipment = editedShipment.copy(clientName = it) },
                    label = { Text("Nom du client") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Client Phone field
                OutlinedTextField(
                    value = editedShipment.clientPhone,
                    onValueChange = { editedShipment = editedShipment.copy(clientPhone = it) },
                    label = { Text("Téléphone du client") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Weight field
                OutlinedTextField(
                    value = editedShipment.weightKg.toString(),
                    onValueChange = {
                        val weight = it.toDoubleOrNull() ?: 0.0
                        editedShipment = editedShipment.copy(weightKg = weight)
                    },
                    label = { Text("Poids (KG)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Volume field
                OutlinedTextField(
                    value = editedShipment.volumeCbm.toString(),
                    onValueChange = {
                        val volume = it.toDoubleOrNull() ?: 0.0
                        editedShipment = editedShipment.copy(volumeCbm = volume)
                    },
                    label = { Text("Volume (CBM)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Amount Due field
                OutlinedTextField(
                    value = editedShipment.amountDue.toString(),
                    onValueChange = {
                        val amount = it.toDoubleOrNull() ?: 0.0
                        editedShipment = editedShipment.copy(amountDue = amount)
                    },
                    label = { Text("Montant dû") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Status selection
                Text(
                    text = "Statut",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    ShipmentStatus.values().forEach { status ->
                        FilterChip(
                            selected = editedShipment.statusIndex == status.ordinal,
                            onClick = { editedShipment = editedShipment.copy(statusIndex = status.ordinal) },
                            label = { Text(status.label) }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Products section
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Title(
                        text = "Produits",
                        color = Color.Black.lighten(.2f),
                        modifier = Modifier.weight(1f)
                    )

                    // Only ADMIN and EMPLOYEE can add products
                    if (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role) {
                        IconButton(
                            onClick = { showAddProductDialog = true }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Ajouter un produit",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
            }

            // Product list
            if (editedShipment.products.isNotEmpty()) {
                items(editedShipment.products) { product ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = product.name,
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Text(
                                    text = "Quantité: ${product.quantity}",
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                if (product.description.isNotBlank()) {
                                    Text(
                                        text = product.description,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                    )
                                }
                            }

                            // Only ADMIN and EMPLOYEE can delete products
                            if (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role) {
                                IconButton(
                                    onClick = {
                                        val updatedProducts = editedShipment.products.toMutableList()
                                        updatedProducts.remove(product)
                                        editedShipment = editedShipment.copy(products = updatedProducts)
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "Supprimer le produit",
                                        tint = MaterialTheme.colorScheme.error
                                    )
                                }
                            }
                        }
                    }
                }
            } else {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Aucun produit ajouté",
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))

                // Save button - Only for ADMIN and EMPLOYEE roles
                if (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role) {
                    Button(
                        onClick = {
                            scope.launch {
                                loading = true
                                trySuspendFunction("Expédition enregistrée avec succès") {
viewModel.saveShipment(editedShipment)
                                    onShowSnackbar(
                                        SnackbarState(
                                            message = "Expédition enregistrée avec succès",
                                            type = SnackbarType.SUCCESS
                                        )
                                    )
                                    if (viewModel.isCreate) {
                                        editedShipment = Shipment(cargoId = viewModel.cargoId)
                                    }
                                }
                                loading = false
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Enregistrer")
                    }

                    // Delete button (only for existing shipments and ADMIN role)
                    if (!viewModel.isCreate && role == UserRole.ADMIN.role) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = { showDeleteConfirmation = true },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("Supprimer")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun ShipmentDetailsScreenPreview() {
    JACTheme {
        ShipmentDetailsScreen(
            setHideAppbar = {},
            role = null,
            trySuspendFunction = { _, _ -> },
            onShowSnackbar = {},
            onTitleDescriptionChange = {}
        )
    }
}
