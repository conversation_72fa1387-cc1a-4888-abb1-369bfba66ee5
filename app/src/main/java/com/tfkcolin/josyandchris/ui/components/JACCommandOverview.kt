package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid
import com.tfkcolin.josyandchris.ui.components.modern.ModernCommandCard
import com.tfkcolin.josyandchris.ui.theme.JACTheme

/**
 * pass only OUTPUT transactions related to the commands
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCommandOverview(
    modifier: Modifier = Modifier,
    commandCountsByStep: Map<Int, Int>, // Change to Map<Int, Int> to receive direct counts by step index
    onCommandStepClicked: (Int) -> Unit
){
val commandMap = remember(commandCountsByStep) {
    commandCountsByStep
}
Box(modifier = modifier) {
    Column(modifier = Modifier.align(Alignment.Center)) {
        Title(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            text = "Command Summary",
            color = MaterialTheme.colorScheme.primary
        )
        VerticalGrid {
            CommandStep.entries.forEach { step ->
                ModernCommandCard(
                    step = step,
                    count = commandMap[step.ordinal] ?: 0,
                    onClick = { onCommandStepClicked(step.ordinal) }
                )
            }
        }
    }
}
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun CommandOverviewPreview(){
    JACTheme {
        Box(modifier = Modifier.fillMaxSize()){
            JACCommandOverview(
                Modifier
                    .fillMaxHeight(.5f)
                    .align(Alignment.Center),
                commandCountsByStep = mapOf(
                    0 to 5,
                    1 to 2,
                    2 to 3,
                    3 to 1,
                    4 to 0,
                    5 to 4
                ),
                onCommandStepClicked = {}
            )
        }
    }
}
