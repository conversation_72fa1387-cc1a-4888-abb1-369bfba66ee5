package com.tfkcolin.josyandchris.ui.components.foundation

import DotsTyping
import androidx.compose.animation.Crossfade
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

@Composable
fun ContentWithLoading(
    modifier: Modifier = Modifier,
    loading: Boolean,
    loadingComponent: @Composable () -> Unit = { DotsTyping() },
    content: @Composable () -> Unit,
) {
    Crossfade(
        modifier = modifier,
        targetState = loading
    ) {
        if(it) loadingComponent()
        else content()
    }
}