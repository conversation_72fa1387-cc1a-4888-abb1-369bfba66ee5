package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.util.query.DateRange
import java.text.SimpleDateFormat
import java.util.*

/**
 * Simple date range picker component for filtering
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleDateRangePicker(
    modifier: Modifier = Modifier,
    dateRange: DateRange? = null,
    onDateRangeSelected: (DateRange?) -> Unit,
    label: String = "Date Range"
) {
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }
    
    val dateFormatter = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    
    Column(modifier = modifier) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Start Date
            OutlinedCard(
                modifier = Modifier.weight(1f),
                onClick = { showStartDatePicker = true },
                shape = RoundedCornerShape(8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "Start Date",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = dateRange?.startDate?.let { dateFormatter.format(it) } ?: "Select",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (dateRange?.startDate != null) {
                                MaterialTheme.colorScheme.onSurface
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                    Icon(
                        imageVector = Icons.Default.DateRange,
                        contentDescription = "Select start date",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // End Date
            OutlinedCard(
                modifier = Modifier.weight(1f),
                onClick = { showEndDatePicker = true },
                shape = RoundedCornerShape(8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "End Date",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = dateRange?.endDate?.let { dateFormatter.format(it) } ?: "Select",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (dateRange?.endDate != null) {
                                MaterialTheme.colorScheme.onSurface
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                    Icon(
                        imageVector = Icons.Default.DateRange,
                        contentDescription = "Select end date",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // Quick preset buttons
        if (dateRange == null) {
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                DateRangePresets.getPresets().take(3).forEach { (label, range) ->
                    AssistChip(
                        onClick = { onDateRangeSelected(range) },
                        label = { Text(label, style = MaterialTheme.typography.labelSmall) }
                    )
                }
            }
        } else {
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                TextButton(
                    onClick = { onDateRangeSelected(null) }
                ) {
                    Text("Clear")
                }
            }
        }
    }
    
    // Date picker dialogs would be implemented here with actual DatePickerDialog
    // For now, we'll use a simple implementation
    if (showStartDatePicker) {
        SimpleDatePickerDialog(
            onDateSelected = { selectedDate ->
                val newRange = dateRange?.copy(startDate = selectedDate) 
                    ?: DateRange(selectedDate, selectedDate)
                onDateRangeSelected(newRange)
                showStartDatePicker = false
            },
            onDismiss = { showStartDatePicker = false }
        )
    }
    
    if (showEndDatePicker) {
        SimpleDatePickerDialog(
            onDateSelected = { selectedDate ->
                val newRange = dateRange?.copy(endDate = selectedDate) 
                    ?: DateRange(selectedDate, selectedDate)
                onDateRangeSelected(newRange)
                showEndDatePicker = false
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
}

@Composable
private fun SimpleDatePickerDialog(
    onDateSelected: (Date) -> Unit,
    onDismiss: () -> Unit
) {
    // Simple dialog for date selection - in a real implementation,
    // you would use DatePickerDialog or Material3's DatePicker
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Select Date") },
        text = { Text("Date picker implementation needed") },
        confirmButton = {
            TextButton(
                onClick = {
                    onDateSelected(Date()) // Return current date for now
                }
            ) {
                Text("OK")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
