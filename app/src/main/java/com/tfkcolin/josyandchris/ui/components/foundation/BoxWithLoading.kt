package com.tfkcolin.josyandchris.ui.components.foundation

import DotsTyping
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun BoxWithLoading(
    modifier: Modifier = Modifier,
    loading: Boolean,
    content: @Composable () -> Unit
) {
    Box(modifier = modifier) {
        Crossfade(
            modifier = Modifier.align(Alignment.Center),
            targetState = loading
        ) {
            if(it){
                DotsTyping()
            }
            else {
                content()
            }
        }
    }
}

@Preview
@Composable
private fun BoxWithLoadingPreview(){
    JACTheme {
        BoxWithLoading(loading = true) {
            Text(text = "salut")
        }
    }
}