package com.tfkcolin.josyandchris.ui.components

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACTopAppBar(
    modifier: Modifier = Modifier,
    label: String = "<PERSON><PERSON> and <PERSON>",
    onIconClick: () -> Unit,
    showNavIcon: Boolean = false,
    isLogIn: Boolean,
    onLogOutClick: () -> Unit
){
    TopAppBar(
        title = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier.fillMaxWidth(.7f),
                    text = label,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    textAlign = TextAlign.Center
                )
            }
        },
        modifier = modifier,
        navigationIcon = {
            Crossfade(targetState = showNavIcon) {
                if(it){
                    IconButton(
                        modifier = Modifier.padding(5.dp),
                        onClick = onIconClick
                    ) {
                        Icon(
                            imageVector = Icons.Filled.ArrowBack,
                            contentDescription = "Go back to the previous screen",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                } else {
                    Icon(
                        modifier = Modifier.padding(5.dp),
                        imageVector = Icons.Filled.Home,
                        contentDescription = "Go back to the previous screen",
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
        },
        actions = {
            if(isLogIn){
                IconButton(
                    modifier = Modifier.padding(5.dp),
                    onClick = onLogOutClick
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_baseline_run_circle_24),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primary,
            titleContentColor = MaterialTheme.colorScheme.onPrimary
        )
    )

    /*CenterAlignedTopAppBar(
        modifier = modifier,
        title = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier.fillMaxWidth(.7f),
                    text = label,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    textAlign = TextAlign.Center
                )
                if(isLogIn){
                    IconButton(
                        modifier = Modifier.padding(start = 15.dp),
                        onClick = onLogOutClick
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_baseline_run_circle_24),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        },
        scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(),
        navigationIcon = {
            if(showNavIcon){
                IconButton(onClick = onIconClick) {
                    Icon(
                        imageVector = Icons.Filled.ArrowBack,
                        contentDescription = "Go back to the previous screen"
                    )
                }
            }
        }
    )*/
}

@Preview(showBackground = true)
@Composable
fun JACTopAppBarPreview() {
    JACTheme {
        JACTopAppBar(
            onIconClick = {},
            isLogIn = true,
            onLogOutClick = {}
        )
    }
}