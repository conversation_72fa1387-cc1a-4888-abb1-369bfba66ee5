package com.tfkcolin.josyandchris.ui.components.modern

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.*
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.delay

@Composable
fun ModernCommandCard(
    step: CommandStep,
    count: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    animationDelay: Int = 0
) {
    var isVisible by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.92f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn() + scaleIn(initialScale = 0.7f)
    ) {
        Card(
            onClick = onClick,
            modifier = modifier
                .padding(8.dp)
                .scale(animatedScale),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 6.dp,
                pressedElevation = 3.dp
            ),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            interactionSource = interactionSource
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    painter = painterResource(id = step.iconResId),
                    contentDescription = step.step,
                    modifier = Modifier.size(40.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = step.step,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "$count commandes",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun ModernCommandOverview(
    modifier: Modifier = Modifier,
    commandCountsByStep: Map<Int, Int>,
    onCommandStepClicked: (Int) -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(24.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // Header with gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                MaterialTheme.colorScheme.primary,
                                MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Résumé des Commandes",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // Command cards grid
            VerticalGrid(columns = 2) {
                CommandStep.entries.forEachIndexed { index, step ->
                    AnimatedCommandCard(
                        step = step,
                        count = commandCountsByStep[step.ordinal] ?: 0,
                        onClick = { onCommandStepClicked(step.ordinal) },
                        animationDelay = index * 100
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
private fun AnimatedCommandCard(
    step: CommandStep,
    count: Int,
    onClick: () -> Unit,
    animationDelay: Int
) {
    var isVisible by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.92f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn() + scaleIn(initialScale = 0.8f)
    ) {
        Card(
            onClick = onClick,
            modifier = Modifier
                .padding(8.dp)
                .scale(animatedScale),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp,
                pressedElevation = 2.dp
            ),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
            ),
            interactionSource = interactionSource
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Icon with background
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(
                            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = step.iconResId),
                        contentDescription = step.step,
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = step.step,
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // Animated count
                AnimatedContent(
                    targetState = count,
                    transitionSpec = {
                        slideInVertically { height -> height } + fadeIn() with
                        slideOutVertically { height -> -height } + fadeOut()
                    }
                ) { targetCount ->
                    Text(
                        text = "$targetCount",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                Text(
                    text = if (count == 1) "commande" else "commandes",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun ModernCargoOverview(
    modifier: Modifier = Modifier,
    cargoCountsByStatus: Map<CargoStatus, Int>,
    onCargoStatusClicked: (Int) -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(24.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // Header with gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                MaterialTheme.colorScheme.secondary,
                                MaterialTheme.colorScheme.secondary.copy(alpha = 0.8f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Résumé des Cargaisons",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondary
                )
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // Cargo status cards
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                CargoStatus.entries.forEachIndexed { index, status ->
                    AnimatedCargoStatusCard(
                        status = status,
                        count = cargoCountsByStatus[status] ?: 0,
                        onClick = { onCargoStatusClicked(status.ordinal) },
                        animationDelay = index * 80
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
private fun AnimatedCargoStatusCard(
    status: CargoStatus,
    count: Int,
    onClick: () -> Unit,
    animationDelay: Int
) {
    var isVisible by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn() + slideInHorizontally(initialOffsetX = { it / 2 })
    ) {
        Card(
            onClick = onClick,
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = when(status) {
                    CargoStatus.LOADING -> MaterialTheme.colorScheme.tertiary.copy(alpha = 0.1f)
                    CargoStatus.IN_TRANSIT -> MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                    CargoStatus.ARRIVED -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.1f)
                    CargoStatus.UNLOADING -> MaterialTheme.colorScheme.tertiary.copy(alpha = 0.1f)
                    CargoStatus.COMPLETED -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                }
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    // Status icon with colored background
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(RoundedCornerShape(12.dp))
                            .background(
                                color = when(status) {
                                    CargoStatus.LOADING -> MaterialTheme.colorScheme.tertiary
                                    CargoStatus.IN_TRANSIT -> MaterialTheme.colorScheme.primary
                                    CargoStatus.ARRIVED -> MaterialTheme.colorScheme.secondary
                                    CargoStatus.UNLOADING -> MaterialTheme.colorScheme.tertiary
                                    CargoStatus.COMPLETED -> Color(0xFF4CAF50)
                                }.copy(alpha = 0.2f)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = status.iconResId),
                            contentDescription = status.label,
                            modifier = Modifier.size(28.dp),
                            tint = when(status) {
                                CargoStatus.LOADING -> MaterialTheme.colorScheme.tertiary
                                CargoStatus.IN_TRANSIT -> MaterialTheme.colorScheme.primary
                                CargoStatus.ARRIVED -> MaterialTheme.colorScheme.secondary
                                CargoStatus.UNLOADING -> MaterialTheme.colorScheme.tertiary
                                CargoStatus.COMPLETED -> Color(0xFF4CAF50)
                            }
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column {
                        Text(
                            text = status.label,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (count == 1) "$count cargo" else "$count cargos",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ModernCommandOverviewPreview() {
    JACTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            ModernCommandOverview(
                commandCountsByStep = mapOf(
                    0 to 4,
                    1 to 2,
                    2 to 1,
                    3 to 3,
                    4 to 0,
                    5 to 5
                ),
                onCommandStepClicked = {}
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ModernCargoOverviewPreview() {
    JACTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            ModernCargoOverview(
                cargoCountsByStatus = mapOf(
                    CargoStatus.LOADING to 3,
                    CargoStatus.IN_TRANSIT to 5,
                    CargoStatus.ARRIVED to 2,
                    CargoStatus.UNLOADING to 1,
                    CargoStatus.COMPLETED to 4
                ),
                onCargoStatusClicked = {}
            )
        }
    }
}
