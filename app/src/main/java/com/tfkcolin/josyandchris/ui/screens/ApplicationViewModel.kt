package com.tfkcolin.josyandchris.ui.screens

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.auth.AuthManager
import com.tfkcolin.josyandchris.auth.AuthorizationManager
import com.tfkcolin.josyandchris.auth.domain.model.AuthUser
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationState
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.ui.data.*
import com.tfkcolin.josyandchris.dto.*
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.repository.CommandRepository
import com.tfkcolin.josyandchris.repository.CargoRepository
import com.tfkcolin.josyandchris.repository.ShipmentRepository
import com.tfkcolin.josyandchris.repository.FinancialTransactionRepository
import com.tfkcolin.josyandchris.repository.CountryRepository
import com.tfkcolin.josyandchris.util.query.QueryFilters
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ApplicationViewModel @Inject constructor(
    val authManager: AuthManager,
    val authorizationManager: AuthorizationManager,
    private val commandRepository: CommandRepository,
    private val cargoRepository: CargoRepository,
    private val shipmentRepository: ShipmentRepository,
    private val financialTransactionRepository: FinancialTransactionRepository,
    private val countryRepository: CountryRepository
): ViewModel() {
    
    // Initialize state properties first
    private val _confirmationState: MutableState<ConfirmationState?> = mutableStateOf(null)
    val confirmationState = _confirmationState as State<ConfirmationState?>
    
    val snackbarState: MutableState<SnackbarState?> = mutableStateOf(null)
    val showAddTransactionDialog: MutableState<Boolean> = mutableStateOf(false)
    
    val currentUser: MutableState<AuthUser?> = mutableStateOf(authManager.currentUser)
    val role: MutableState<String?> = mutableStateOf(null)
    
    // New StateFlow for aggregated statistics
    private val _appStats: MutableStateFlow<StatsDTO> = MutableStateFlow(StatsDTO())
    val appStats: StateFlow<StatsDTO> = _appStats.asStateFlow()
    
    private fun loadUserRole(userId: String) {
        viewModelScope.launch {
            authorizationManager.loadUserRole(userId)
            // Update the role string for backward compatibility
            role.value = authorizationManager.getCurrentUserRole()?.role
        }
    }
    
    // Query filter states for rebuilding QueryBuilders
    private val _commandFilters = MutableStateFlow(QueryFilters())
    private val _cargoFilters = MutableStateFlow(QueryFilters())
    private val _shipmentFilters = MutableStateFlow(QueryFilters())
    
    // PagingData flows for efficient data loading
    val commandsPagingData: Flow<PagingData<Command>> = _commandFilters
        .flatMapLatest { filters ->
            commandRepository.getCommandsPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)
    
    val cargosPagingData: Flow<PagingData<Cargo>> = _cargoFilters
        .flatMapLatest { filters ->
            cargoRepository.getCargosPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)
    
    val shipmentsPagingData: Flow<PagingData<Shipment>> = _shipmentFilters
        .flatMapLatest { filters ->
            shipmentRepository.getShipmentsPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)
    
    // Loading functions wrapped in suspend functions (for on-demand loading)
    suspend fun loadCommandStats(): CommandStatsDetail {
        return commandRepository.getCommandsCountByAllStatuses()
            .fold(
                onSuccess = { counts ->
                    val mappedCounts = counts.mapKeys { (index, _) -> 
                        CommandStep.values().find { it.ordinal == index } ?: CommandStep.RECORD
                    }.mapValues { it.value.toInt() }
                    CommandStatsDetail(totalCommands = counts.values.sum().toInt(), countsByStatus = mappedCounts)
                },
                onFailure = { CommandStatsDetail() }
            )
    }
    
    suspend fun loadCargoStats(): CargoStatsDetail {
        return cargoRepository.getCargosCountByAllStatuses()
            .fold(
                onSuccess = { counts ->
                    val mappedCounts = counts.mapKeys { (index, _) -> 
                        CargoStatus.values().find { it.ordinal == index } ?: CargoStatus.LOADING
                    }.mapValues { it.value.toInt() }
                    CargoStatsDetail(totalCargo = counts.values.sum().toInt(), countsByStatus = mappedCounts)
                },
                onFailure = { CargoStatsDetail() }
            )
    }
    
    suspend fun loadTransactionStats(): TransactionStats {
        val success = 100 // TODO: Replace with actual call to repository
        val failure = 10 // TODO: Replace with actual call to repository
        val total = success + failure
        return TransactionStats(totalTransactions = total, successfulTransactions = success, failedTransactions = failure)
    }
    
    suspend fun loadShipmentStats(): ShipmentStats {
        return shipmentRepository.getShipmentsCountByAllCargos()
            .fold(
                onSuccess = { counts ->
                    // TODO: Map actual shipment statuses to stats
                    ShipmentStats(totalShipments = counts.values.sum().toInt(), shipped = counts.values.sum().toInt(), inTransit = 0, delivered = 0)
                },
                onFailure = { ShipmentStats(0, 0, 0, 0) }
            )
    }
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        // Observe auth state changes from AuthManager
        viewModelScope.launch {
            authManager.authState.collect { authState ->
                currentUser.value = authManager.currentUser
                
                // Load user role when user logs in
                currentUser.value?.let { user ->
                    loadUserRole(user.id)
                    refreshAppStats() // Refresh stats when user logs in
                }
            }
        }
    }
    
    // Refresh application statistics
    fun refreshAppStats() {
        viewModelScope.launch {
            val commandStats = loadCommandStats()
            val cargoStats = loadCargoStats()
            val transactionStats = loadTransactionStats()
            val shipmentStats = loadShipmentStats()
            
            _appStats.value = StatsDTO(
                commandStats = commandStats,
                cargoStats = cargoStats,
                shipmentStats = shipmentStats,
                transactionStats = transactionStats
            )
        }
    }

    /**
     * Load financial transactions with pagination support
     * Should be called from screens that need transaction details
     */
    suspend fun loadTransactions(
        limit: Int = 20,
        startAfter: String? = null
    ): List<FinancialTransaction> {
        return financialTransactionRepository.loadTransactionsPaged(limit, startAfter)
            .getOrDefault(emptyList())
    }
    
    /**
     * Load countries list
     * Should be called from screens that need country details
     */
    suspend fun loadCountries(): List<CountryData> {
        return countryRepository.loadCountries()
            .getOrDefault(emptyList())
    }
    
    /**
     * Get country statistics flow for real-time updates
     * Used in screens that show country balance information
     */
    fun getCountryStatsFlow(): Flow<Map<String, CountryStatsDetail>> = 
        countryRepository.getCountriesFlow()
            .flatMapLatest { countries ->
                kotlinx.coroutines.flow.flow {
                    val stats = mutableMapOf<String, CountryStatsDetail>()
                    countries.forEach { country ->
                        val inputTotal = financialTransactionRepository
                            .getTransactionsTotalByCountryAndType(country.name, 0)
                            .getOrDefault(0.0)
                        val outputTotal = financialTransactionRepository
                            .getTransactionsTotalByCountryAndType(country.name, 1)
                            .getOrDefault(0.0)
                        
                        stats[country.name] = CountryStatsDetail(
                            countryName = country.name,
                            inputTotal = inputTotal,
                            outputTotal = outputTotal,
                            balance = inputTotal - outputTotal
                        )
                    }
                    emit(stats as Map<String, CountryStatsDetail>)
                }
            }
            .catch { exception ->
                Timber.e(exception, "Error loading country statistics")
                emit(emptyMap<String, CountryStatsDetail>())
            }

    fun showSnackbar(state: SnackbarState?) {
        snackbarState.value = state
    }

    fun showConfirmationDialog(state: ConfirmationState) {
        _confirmationState.value = state
    }

    fun hideConfirmationDialog() {
        _confirmationState.value = null
    }

    fun isLogIn() = currentUser.value != null

    fun logOut() {
        viewModelScope.launch {
            authManager.signOut()
        }
    }

    fun setShowAddTransactionDialog(state: Boolean){
        showAddTransactionDialog.value = state
    }
    
    // Permission checking methods
    fun canCreateCommands(): Boolean = authorizationManager.canCreateCommands()
    fun canManageTransactions(): Boolean = authorizationManager.canManageTransactions()
    fun canManageCountries(): Boolean = authorizationManager.canManageCountries()
    fun canManageCargos(): Boolean = authorizationManager.canManageCargos()
    fun isAdmin(): Boolean = authorizationManager.isAdmin()
    fun isEmployee(): Boolean = authorizationManager.isEmployee()
    fun isDeliverer(): Boolean = authorizationManager.isDeliverer()
    
    /**
     * Set user role manually (for testing or admin purposes)
     */
    fun setUserRole(role: UserRole) {
        currentUser.value?.let { user ->
            viewModelScope.launch {
                authorizationManager.setUserRole(user.id, role)
                <EMAIL> = role.role
            }
        }
    }
    
    // ===== REFRESH FUNCTIONS =====
    
    /**
     * Refresh commands data by rebuilding QueryBuilder
     */
    fun refreshCommands() {
        _commandFilters.value = QueryFilters()
        // PagingData will automatically refresh when filters change
    }
    
    /**
     * Refresh cargos data by rebuilding QueryBuilder
     */
    fun refreshCargos() {
        _cargoFilters.value = QueryFilters()
        // PagingData will automatically refresh when filters change
    }
    
    /**
     * Refresh shipments data by rebuilding QueryBuilder
     */
    fun refreshShipments() {
        _shipmentFilters.value = QueryFilters()
        // PagingData will automatically refresh when filters change
    }
    
    /**
     * Refresh all data and stats
     */
    fun refreshAll() {
        refreshCommands()
        refreshCargos()
        refreshShipments()
        refreshAppStats() // Refresh statistics as well
    }
    
    // ===== SEARCH FUNCTIONS =====
    
    /**
     * Search commands by rebuilding QueryBuilder with search term
     */
    fun searchCommands(query: String) {
        val filters = if (query.isBlank()) {
            QueryFilters()
        } else {
            QueryFilters(searchTerm = query)
        }
        _commandFilters.value = filters
    }
    
    /**
     * Search cargos by rebuilding QueryBuilder with search term
     */
    fun searchCargos(query: String) {
        val filters = if (query.isBlank()) {
            QueryFilters()
        } else {
            QueryFilters(searchTerm = query)
        }
        _cargoFilters.value = filters
    }
    
    /**
     * Search shipments by rebuilding QueryBuilder with search term
     */
    fun searchShipments(query: String) {
        val filters = if (query.isBlank()) {
            QueryFilters()
        } else {
            QueryFilters(searchTerm = query)
        }
        _shipmentFilters.value = filters
    }
    
    // ===== FILTER FUNCTIONS =====
    
    /**
     * Filter commands by status
     */
    fun filterCommandsByStatus(statusIndex: Int?) {
        val filters = QueryFilters(statusIndex = statusIndex)
        _commandFilters.value = filters
    }
    
    /**
     * Filter cargos by status
     */
    fun filterCargosByStatus(statusIndex: Int?) {
        val filters = QueryFilters(statusIndex = statusIndex)
        _cargoFilters.value = filters
    }
    
    /**
     * Filter shipments by status
     */
    fun filterShipmentsByStatus(statusIndex: Int?) {
        val filters = QueryFilters(statusIndex = statusIndex)
        _shipmentFilters.value = filters
    }
    
    /**
     * Apply combined filters for commands
     */
    fun applyCommandFilters(
        statusIndex: Int? = null,
        searchTerm: String? = null,
        additionalFilters: Map<String, Any> = emptyMap()
    ) {
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            additionalFilters = additionalFilters
        )
        _commandFilters.value = filters
    }
    
    /**
     * Apply combined filters for cargos
     */
    fun applyCargoFilters(
        statusIndex: Int? = null,
        searchTerm: String? = null,
        additionalFilters: Map<String, Any> = emptyMap()
    ) {
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            additionalFilters = additionalFilters
        )
        _cargoFilters.value = filters
    }
    
    /**
     * Apply combined filters for shipments
     */
    fun applyShipmentFilters(
        statusIndex: Int? = null,
        searchTerm: String? = null,
        additionalFilters: Map<String, Any> = emptyMap()
    ) {
        val filters = QueryFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            additionalFilters = additionalFilters
        )
        _shipmentFilters.value = filters
    }
}
