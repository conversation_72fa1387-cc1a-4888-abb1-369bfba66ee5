package com.tfkcolin.josyandchris.ui.screens

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import androidx.paging.compose.LazyPagingItems
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationState
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.screens.accounting.accountingScreen
import com.tfkcolin.josyandchris.ui.screens.accounting.navigateToAccountingScreen
import com.tfkcolin.josyandchris.ui.screens.addimagedatascreen.addImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.addimagedatascreen.navigateToAddImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.cargodetails.cargoDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.cargodetails.navigateToCargoDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.cargolist.cargoListNavigation
import com.tfkcolin.josyandchris.ui.screens.cargolist.navigateToCargoScreen
import com.tfkcolin.josyandchris.ui.screens.commanddetails.commandDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.commanddetails.navigateToCommandDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.editimagedata.editImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.commandlist.commandListNavigation // Ensure this is imported
import com.tfkcolin.josyandchris.ui.screens.commandlist.navigateToCommandScreen // Use the new navigation function
import com.tfkcolin.josyandchris.ui.screens.editimagedata.navigateToEditImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.homeselection.countrySelectionScreen
import com.tfkcolin.josyandchris.ui.screens.shipmentdetails.shipmentDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.shipmentdetails.navigateToShipmentDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.homeselection.navigateToCountrySelectionScreen
import com.tfkcolin.josyandchris.ui.screens.loadingscreen.loadingScreen
import com.tfkcolin.josyandchris.ui.screens.login.loginNavigation
import com.tfkcolin.josyandchris.auth.AuthManager
import com.tfkcolin.josyandchris.ui.screens.commandlist.commandListNavigation

@Composable
fun NavigationGraph(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    countries: List<CountryData>,
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    authManager: AuthManager,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    startDestination: String = Screen.LoginScreen.route,
    showAddTransactionDialog: Boolean = false,
    onShowAddTransactionDialogChanged: () -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onLoginResult: (Throwable?) -> Unit,
    onTitleDescriptionChange: (String) -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit
){
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = startDestination
    ){
        loadingScreen()
        loginNavigation(
            onNavigateToHome = {
                navController.navigateToCountrySelectionScreen{
                    popUpTo(Screen.CountrySelectionScreen.route){
                        inclusive = false
                    }
                }
            },
            onNavigateToRegistration = {}
        )
        countrySelectionScreen(
            onShowSnackbar = onShowSnackbar,
            countries = countries,
            role = role,
            authManager = authManager,
            onNavigateToCountry = { country ->
                navController.navigateToAccountingScreen(country.name){
                    popUpTo(Screen.CountrySelectionScreen.route){
                        inclusive = false
                    }
                }
            },
            onNavigateToCommandScreen = { stepIndex ->
                // Convert stepIndex to CommandStep if valid, otherwise pass null
                val stepFilter = if (stepIndex >= 0 && stepIndex < CommandStep.entries.size) {
                    CommandStep.entries[stepIndex]
                } else null
                navController.navigateToCommandScreen(stepFilter = stepFilter)
            },
            onNavigateToAddImageScreen = {
                navController.navigateToAddImageDataScreen()
            },
            onNavigateToCargoScreen = { statusIndex ->
                navController.navigateToCargoScreen(statusIndex = statusIndex)
            },
            onShowConfirmationDialog = onShowConfirmationDialog
        )
        accountingScreen(
            countriesData = countries,
            showAddTransactionDialog = showAddTransactionDialog,
            onShowSnackbar = onShowSnackbar,
            onShowAddTransactionDialogChanged = onShowAddTransactionDialogChanged,
            onTitleDescriptionChange = onTitleDescriptionChange
        )
        commandDetailsScreen(
            onShowSnackbar = onShowSnackbar,
            role = role,
            setHideAppbar = setHideAppbar,
            onNavigateToAddImageDataScreen = {
                navController.navigateToAddImageDataScreen()
            },
            onTitleDescriptionChange = onTitleDescriptionChange,
            // Removed imagesData - should be loaded by the screen itself
            trySuspendFunction = trySuspendFunction
        )
        addImageDataScreen(
            // Removed imagesData - should be loaded by the screen itself
            onShowSnackbar = onShowSnackbar,
            onNavigateToEditImageScreen = {
                navController.navigateToEditImageDataScreen(it)
            },
        )
        commandListNavigation(
            onNavigateToCommandDetail = { id ->
                navController.navigateToCommandDetailsScreen(id = id, isCreate = false)
            },
            onNavigateToCreateCommand = {
                navController.navigateToCommandDetailsScreen(id = "", isCreate = true)
            }
        )
        editImageDataScreen(
            onShowSnackbar = onShowSnackbar,
            onNavigateBack = {navController.navigateUp()}
        )
        cargoListNavigation(
            onNavigateToCargoDetail = { id ->
                navController.navigateToCargoDetailsScreen(id = id, isCreate = false)
            }
        )
        cargoDetailsScreen(
            setHideAppbar = setHideAppbar,
            role = role,
            trySuspendFunction = trySuspendFunction,
            onShowSnackbar = onShowSnackbar,
            onNavigateToShipmentDetailsScreen = { shipmentId, cargoId ->
                navController.navigateToShipmentDetailsScreen(id = shipmentId, cargoId = cargoId)
            },
            onTitleDescriptionChange = onTitleDescriptionChange
        )
        shipmentDetailsScreen(
            setHideAppbar = setHideAppbar,
            role = role,
            trySuspendFunction = trySuspendFunction,
            onShowSnackbar = onShowSnackbar,
            onTitleDescriptionChange = onTitleDescriptionChange
        )
    }
}
