package com.tfkcolin.josyandchris.ui.screens.commanddetails

import DotsTyping
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Build
import androidx.activity.compose.BackHandler
import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ContentAlpha
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material.icons.outlined.Edit
import androidx.compose.material.icons.outlined.ShoppingCart
import androidx.compose.material3.*
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.ui.components.*
import com.tfkcolin.josyandchris.ui.components.dialog.JACAddProductDialog
import com.tfkcolin.josyandchris.ui.components.foundation.BoxWithLoading
import com.tfkcolin.josyandchris.ui.components.foundation.ExtensibleText
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import kotlinx.coroutines.launch

data class DeleteDialogData(
    val title: String,
    val description: String,
    val confirmationText: String,
    val cancelText: String,
    val action: () -> Unit
)

@Composable
fun CommandDetailsScreen(
    modifier: Modifier = Modifier,
    changeMade: Boolean,
    command: Command,
    role: String?,
    onShowSnackbar: (SnackbarState) -> Unit,
    setHideAppbar: (Boolean) -> Unit,
    imageData: List<ImageData>,
    isCreate: Boolean,
    onCommandChanged: (Command) -> Unit,
    onCommandSaved: suspend () -> Unit,
    onUploadProof: suspend (String) -> Unit,
    onDeleteImage: suspend (path: String) -> Unit,
    previews: List<CommandDetailsScreenViewModel.PreviewsData>,
    onUploadPreview: suspend (url: String, filename: String, productIndex: Int) -> Unit,
    onAddImageData: () -> Unit,
){
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var newProductBuffer: MiniProduct? by remember { mutableStateOf(null) }
    var productToUpdateIndex: Int? by remember { mutableStateOf(null) }
    var currentObservation by remember { mutableStateOf("") }

    val canEditData = remember { role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role }

    var previewUploadLoading by remember { mutableStateOf(false) }

    var removeData: DeleteDialogData? by remember { mutableStateOf(null) }

    var productToShowPreviewIndex: Int? by rememberSaveable { mutableStateOf(null) }

    val selectedUploadedProductPreview by remember(command, productToShowPreviewIndex, previews) {
        derivedStateOf {
            productToShowPreviewIndex?.let { index ->
                command.products[index].previewsPathName?.let {
                    previews.find { it.node == command.products[index].previewsPathName }
                }
            }
        }
    }
    var localImageBuffer by rememberSaveable(productToShowPreviewIndex) {
        mutableStateOf<List<FirebaseStorageFileData>>(listOf())
    }

    BackHandler(enabled = productToShowPreviewIndex != null) {
        setHideAppbar(false)
        productToShowPreviewIndex = null
        //localImageBuffer = listOf()
    }

    Box(modifier = modifier) {
        AnimatedContent(
            targetState = productToShowPreviewIndex == null,
            transitionSpec = {
                if(targetState){ // previews come to the screen from the top and content goes down
                    (fadeIn(animationSpec = tween(400)) + slideIntoContainer(
                        animationSpec = tween(400),
                        towards = AnimatedContentTransitionScope.SlideDirection.Down
                    )).togetherWith(
                        fadeOut(animationSpec = tween(400)) + slideOutOfContainer(
                            animationSpec = tween(400),
                            towards = AnimatedContentTransitionScope.SlideDirection.Down
                        )
                    )
                } else { // previews leave towards the top and content enter from the bottom
                    (fadeIn(animationSpec = tween(400)) + slideIntoContainer(
                        animationSpec = tween(400),
                        towards = AnimatedContentTransitionScope.SlideDirection.Up
                    )).togetherWith(
                        fadeOut(animationSpec = tween(400)) + slideOutOfContainer(
                            animationSpec = tween(400),
                            towards = AnimatedContentTransitionScope.SlideDirection.Up
                        )
                    )
                }
            }, label = ""
        ) {
            if(it){
                Column {
                    Box (modifier = Modifier.weight(1f)){
                        LazyColumn(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally) {
                            item {
                                Title(
                                    text = CommandStep.entries[command.commandStepIndex].label,
                                    color = Color.Black.lighten(.2f)
                                )
                                var linkCopied by remember { mutableStateOf(false) }
                                var codeCopied by remember { mutableStateOf(false) }
                                var expanded by remember { mutableStateOf(false) }
                                ElevatedButton(
                                    modifier = Modifier.fillMaxWidth(.8f),
                                    onClick = { expanded = true },
                                    enabled = canEditData,
                                    shape = RoundedCornerShape(10)
                                ) {
                                    Icon(
                                        modifier = Modifier
                                            .padding(end = 5.dp)
                                            .weight(1f),
                                        imageVector = Icons.Default.Share,
                                        contentDescription = null
                                    )
                                    Text(
                                        modifier = Modifier.weight(1f),
                                        text = "Partager"
                                    )
                                    DropdownMenu(
                                        modifier = Modifier.heightIn(max = 400.dp),
                                        expanded = expanded,
                                        onDismissRequest = { expanded = false }
                                    ) {
                                        DropdownMenuItem(
                                            onClick = {
                                                val clipboard = context
                                                    .getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                                val clip: ClipData = ClipData
                                                    .newPlainText("lien de la commande", "jacweb.vercel.app/commands/${command.id}")
                                                clipboard.setPrimaryClip(clip)
                                                if(Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2)
                                                    onShowSnackbar(SnackbarState(
                                                        message = "le lien à bien été copié",
                                                    ))
                                                linkCopied = true
                                            },
                                            text = { Text("Copier le lien") },
                                            enabled = !linkCopied,
                                            trailingIcon = {
                                                AnimatedVisibility(visible = linkCopied) {
                                                    Icon(
                                                        modifier = modifier.padding(start = 5.dp),
                                                        imageVector = Icons.Default.CheckCircle,
                                                        contentDescription = null,
                                                        tint = Color.Green
                                                    )
                                                }
                                            }
                                        )
                                        DropdownMenuItem(
                                            onClick = {
                                                val clipboard = context
                                                    .getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                                val clip: ClipData = ClipData
                                                    .newPlainText("code de la commande", command.id)
                                                clipboard.setPrimaryClip(clip)
                                                if(Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2)
                                                    onShowSnackbar(SnackbarState(
                                                        message = "le code à bien été copié",
                                                    ))
                                                codeCopied = true
                                            },
                                            text = { Text("Copier le code") },
                                            enabled = !codeCopied,
                                            trailingIcon = {
                                                AnimatedVisibility(visible = codeCopied) {
                                                    Icon(
                                                        modifier = modifier.padding(start = 5.dp),
                                                        imageVector = Icons.Default.CheckCircle,
                                                        contentDescription = null,
                                                        tint = Color.Green
                                                    )
                                                }
                                            }
                                        )
                                    }
                                }
                                JACEditClientData(
                                    modifier = Modifier.padding(bottom = 4.dp),
                                    clientData = command.client,
                                    onClientDataChange = { onCommandChanged(command.copy(client = it)) },
                                    editable = canEditData
                                )
                                Column(
                                    modifier = Modifier.align(Alignment.Center),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Title(text = "Produits")
                                    Icon(
                                        modifier = Modifier
                                            .size(50.dp)
                                            .padding(bottom = 4.dp),
                                        tint = MaterialTheme.colorScheme.primaryContainer,
                                        imageVector = Icons.Default.ShoppingCart, contentDescription = null)
                                    OutlinedButton(
                                        modifier = Modifier
                                            .padding(bottom = 4.dp),
                                        onClick = { newProductBuffer = MiniProduct() },
                                        shape = RoundedCornerShape(10),
                                        enabled = canEditData
                                    ) {
                                        Icon(imageVector = Icons.Default.AddCircle, contentDescription = null)
                                        Text(modifier = Modifier.padding(start = 2.dp), text = "Ajouter un produit")
                                    }
                                    if(command.products.isEmpty()){
                                        Box(modifier = Modifier.height(150.dp)) {
                                            CompositionLocalProvider(LocalContentAlpha provides ContentAlpha.disabled) {
                                                Text(
                                                    modifier = Modifier.align(Alignment.Center),
                                                    text = "Aucun produit sélectionné actuellement",
                                                    style = MaterialTheme.typography.labelMedium
                                                )
                                            }
                                        }
                                    } else {
                                        VerticalGrid {
                                            // index of commands are used here as id
                                            command.products.forEachIndexed { index, product ->
                                                Box {
                                                    JACProductItem(
                                                        modifier = Modifier.padding(2.dp),
                                                        product = product,
                                                        soldOutEditable = canEditData,
                                                        color = if(product.productEvolutionStep != null) {
                                                            if(product.productEvolutionStep == CommandStep.entries[command.commandStepIndex].step)
                                                                Color.Green
                                                            else if((CommandStep.entries
                                                                    .firstOrNull { it.step == product.productEvolutionStep }?.ordinal
                                                                    ?: 0) > command.commandStepIndex
                                                            ) Color.Blue
                                                            else Color.Red
                                                        } else Color.Red,
                                                        onProductChange = { p ->
                                                            onCommandChanged(
                                                                command.copy(
                                                                    products = command.products.map {
                                                                        if(it == product) p else it
                                                                    }
                                                                )
                                                            )
                                                        }
                                                    )
                                                    if(canEditData) {
                                                        Row(
                                                            modifier = Modifier
                                                                .padding(2.dp)
                                                                .background(color = MaterialTheme
                                                                    .colorScheme
                                                                    .primaryContainer.copy(alpha = .4f),
                                                                    shape = RoundedCornerShape(5)
                                                                )
                                                                .align(Alignment.TopEnd)
                                                        ) {
                                                            IconButton(
                                                                modifier = Modifier
                                                                    .weight(1f),
                                                                onClick = {
                                                                    setHideAppbar(true)
                                                                    productToShowPreviewIndex = index
                                                                },
                                                                enabled = !isCreate
                                                            ) {
                                                                Icon(
                                                                    imageVector = Icons.Outlined.ShoppingCart,
                                                                    contentDescription = null
                                                                )
                                                            }
                                                            IconButton(
                                                                modifier = Modifier
                                                                    .weight(1f),
                                                                onClick = { productToUpdateIndex = index }
                                                            ) {
                                                                Icon(
                                                                    imageVector = Icons.Outlined.Edit,
                                                                    contentDescription = null
                                                                )
                                                            }
                                                            IconButton(
                                                                modifier = Modifier
                                                                    .weight(1f),
                                                                onClick = {
                                                                    removeData = DeleteDialogData(
                                                                        action = {
                                                                            onCommandChanged(
                                                                                command.copy(
                                                                                    products = command.products.filter { it != product }
                                                                                )
                                                                            )
                                                                        },
                                                                        title = "Enlever le produit",
                                                                        description = "voulez-vous vraiment enlever le produit ?",
                                                                        confirmationText = "Oui",
                                                                        cancelText = "Annuler"
                                                                    )
                                                                }
                                                            ) {
                                                                Icon(
                                                                    imageVector = Icons.Outlined.Delete,
                                                                    contentDescription = null
                                                                )
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            item {
                                Title(text = "Preuve de paiement")
                                JACImagePicker(
                                    modifier = Modifier
                                        .padding(bottom = 2.dp)
                                        .fillMaxWidth(.5f)
                                        .wrapContentHeight(),
                                    imageUrl = command.paymentProofImageUrl,
                                    onPickImage = {
                                        onCommandChanged(
                                            command.copy(
                                                paymentProofImageUrl = it.toString(),
                                                proofUploaded = false
                                            )
                                        )
                                    }
                                )
                                if(!command.proofUploaded && command.paymentProofImageUrl != null){
                                    OutlinedButton(
                                        onClick = {
                                            previewUploadLoading = true
                                            scope.launch {
                                                onUploadProof(command.paymentProofImageUrl)
                                                previewUploadLoading = false
                                            }
                                        },
                                        enabled = !isCreate
                                    ) {
                                        BoxWithLoading(loading = previewUploadLoading) {
                                            Row(verticalAlignment = Alignment.CenterVertically) {
                                                Icon(
                                                    modifier = Modifier.padding(end = 5.dp),
                                                    imageVector = Icons.AutoMirrored.Filled.Send,
                                                    contentDescription = null)
                                                Text(text = "Upload l'image")
                                            }
                                        }
                                    }
                                }
                            }
                            item {
                                Title(text = "Observations")
                                JACTextFieldWithButton(
                                    modifier = Modifier.padding(bottom = 4.dp),
                                    value = currentObservation,
                                    onValueChange = { currentObservation = it },
                                    onClick = {
                                        if(currentObservation.isNotEmpty()){
                                            onCommandChanged(
                                                command.copy(
                                                    observation = arrayListOf<String>().apply {
                                                        addAll(command.observation)
                                                        add(currentObservation)
                                                    }
                                                )
                                            )
                                            currentObservation = ""
                                        }
                                    }
                                )
                                Text(
                                    modifier = Modifier.padding(bottom = 4.dp),
                                    text = "liste des observations",
                                    style = MaterialTheme.typography.titleSmall
                                )
                                if(command.observation.isEmpty()){
                                    Box(modifier = Modifier.height(100.dp)){
                                        Text(
                                            modifier = Modifier.align(Alignment.Center),
                                            text = "Aucune observation enregistrer",
                                            style = MaterialTheme.typography.labelMedium,
                                            color = MaterialTheme.colorScheme.onBackground.copy(alpha = .8f)
                                        )
                                    }
                                } else {
                                    command.observation.forEach { observation ->
                                        key(observation){
                                            Box{
                                                ExtensibleText(
                                                    modifier = Modifier.padding(4.dp),
                                                    text = observation
                                                )
                                                IconButton(
                                                    modifier = Modifier.align(Alignment.TopEnd),
                                                    onClick = {
                                                        onCommandChanged(
                                                            command.copy(
                                                                observation = arrayListOf<String>().apply {
                                                                    addAll(
                                                                        command.observation.filter { it != observation }
                                                                    )
                                                                }
                                                            )
                                                        )
                                                    }
                                                ) {
                                                    Icon(
                                                        imageVector = Icons.Default.Delete,
                                                        contentDescription = null,
                                                        tint = MaterialTheme.colorScheme.primary
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            item {
                                Box(modifier = Modifier.padding(bottom = ButtonDefaults.MinHeight))
                            }
                        }
                    }
                    JACCurrentStepView(step = CommandStep.entries[command.commandStepIndex])
                    Row(
                        modifier = Modifier
                            .padding(2.dp)
                            .fillMaxWidth()
                    ){
                        TextButton(
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onCommandChanged(
                                    command
                                        .copy(
                                            commandStepIndex = CommandStep.entries[command.commandStepIndex].previous()?.ordinal ?: 0
                                        )
                                )
                            },
                            enabled = command.commandStepIndex > 0
                        ) {
                            Icon(
                                modifier = Modifier.padding(end = 2.dp),
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack, contentDescription = null)
                            Text(text = "Précédent")
                        }
                        TextButton(
                            modifier = Modifier.weight(1f),
                            onClick = {
                                onCommandChanged(
                                    command
                                        .copy(
                                            commandStepIndex = CommandStep.entries[command.commandStepIndex].next()?.ordinal ?: 0
                                        )
                                )
                            },
                            enabled = command.commandStepIndex < CommandStep.entries.size - 1
                        ) {
                            Icon(
                                modifier = Modifier.padding(end = 2.dp),
                                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                                contentDescription = null
                            )
                            Text(text = "Suivant")
                        }
                    }
                }
            } else {
                JACPreviewImage(
                    modifier = Modifier.fillMaxSize(),
                    productName = productToShowPreviewIndex?.let { command.products[it].name } ?: "",
                    showNext = productToShowPreviewIndex != null
                            && productToShowPreviewIndex!! < command.products.size - 1,
                    showPrev = productToShowPreviewIndex != null
                            && productToShowPreviewIndex!! > 0,
                    onNext = {
                        productToShowPreviewIndex?.let {
                            if(it < command.products.size){
                                productToShowPreviewIndex = productToShowPreviewIndex?.plus(1)
                                localImageBuffer = listOf()
                            }
                        }
                    },
                    onPrev = {
                        productToShowPreviewIndex?.let {
                            if(it > 0){
                                productToShowPreviewIndex = productToShowPreviewIndex?.minus(1)
                                localImageBuffer = listOf()
                            }
                        }
                    },
                    uploadedImages = selectedUploadedProductPreview?.previews,
                    localImages = localImageBuffer,
                    onUploadImage = { url, filename ->
                        onUploadPreview(url, filename, productToShowPreviewIndex!!)
                    },
                    onDeleteImage = { path ->
                        removeData = DeleteDialogData(
                            action = {
                                scope.launch {
                                    onDeleteImage(path)
                                }
                            },
                            title = "Suprimer l'image",
                            description = "Voulez vous vraiment supprimez cette image ? cette action est irreversible.",
                            confirmationText = "Oui",
                            cancelText = "Non"
                        )
                    },
                    onLocalImagesChanged = { images ->
                        localImageBuffer = images
                    }
                )
            }
        }
        AnimatedVisibility(
            modifier = Modifier.align(Alignment.TopEnd),
            visible = changeMade && productToShowPreviewIndex == null
        ) {
            var loading by remember { mutableStateOf(false) }
            ElevatedButton(
                onClick = {
                    loading = true
                    scope.launch {
                        onCommandSaved()
                        loading = false
                    }
                },
                elevation = ButtonDefaults.elevatedButtonElevation(
                    defaultElevation = 6.dp,
                    pressedElevation = 4.dp,
                    disabledElevation = 0.dp
                ),
                enabled = changeMade
            ) {
                Crossfade(targetState = loading, label = "fade button") {
                    if(it) DotsTyping()
                    else {
                        Row {
                            Icon(
                                modifier = Modifier.padding(end = 5.dp),
                                imageVector = Icons.Filled.ThumbUp, contentDescription = null)
                            Text(text = "Valider")
                        }
                    }
                }
            }
        }
    }
    AnimatedVisibility(visible = productToUpdateIndex != null) {
        JACAddProductDialog(
            product = productToUpdateIndex
                ?.let { command.products[productToUpdateIndex!!] } ?: MiniProduct(),
            onProductChanged = {
                onCommandChanged(
                    command.copy(
                        products = command.products.mapIndexed { index, product ->
                            if(index == productToUpdateIndex) it else product
                        }
                    )
                )
            },
            validateText = "Modifier",
            images = imageData,
            onDismissRequest = { productToUpdateIndex = null },
            onValidate = { productToUpdateIndex = null },
            onAddImageData = onAddImageData
        )
    }
    AnimatedVisibility(visible = newProductBuffer != null) {
        JACAddProductDialog(
            product = newProductBuffer ?: MiniProduct(),
            onProductChanged = { newProductBuffer = it },
            images = imageData,
            onDismissRequest = {
                newProductBuffer = null
            },
            onValidate = {
                val newData = arrayListOf(newProductBuffer!!).apply { addAll(command.products) }
                onCommandChanged(
                    command.copy(products = newData)
                )
                newProductBuffer = null
            },
            onAddImageData = onAddImageData
        )
    }
    AnimatedVisibility(visible = removeData != null) {
        AlertDialog(
            onDismissRequest = { removeData = null },
            confirmButton = {
                TextButton(
                    onClick = {
                        removeData!!.action()
                        removeData = null
                    }
                ) {
                    Text(text = removeData?.confirmationText ?: "")
                }
            },
            dismissButton = {
                TextButton(onClick = { removeData = null }) {
                    Text(text = removeData?.cancelText ?: "")
                }
            },
            title = { Text(text = removeData?.title ?: "") },
            text = { Text(text = removeData?.description ?: "") }
        )
    }
}

@Preview(showSystemUi = true, showBackground = true, heightDp = 1200)
@Composable
private fun CommandDetailsScreenPreview(){
    JACTheme {
        CommandDetailsScreen(
            changeMade = true,
            command = Command(products = listOf(MiniProduct(name = "sac", quantity = 20))),
            previews = listOf(),
            imageData = listOf(),
            onCommandChanged = {},
            onCommandSaved = {},
            onUploadProof = {},
            onAddImageData = {},
            onShowSnackbar = {},
            onUploadPreview = {_,_,_ ->},
            onDeleteImage = {},
            role = null,
            setHideAppbar = {},
            isCreate = true
        )
    }
}