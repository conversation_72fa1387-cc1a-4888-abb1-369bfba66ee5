package com.tfkcolin.josyandchris.ui.screens.commanddetails

import android.net.Uri
import androidx.compose.runtime.*
import androidx.lifecycle.SavedStateHandle
import com.tfkcolin.josyandchris.ui.base.BaseViewModel
import timber.log.Timber
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.storage.StorageReference
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.FirebaseStorageFileData
import com.tfkcolin.josyandchris.data.ImageData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class CommandDetailsScreenViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @Named("COMMAND_DB") private val commandDB: CollectionReference,
    @Named("IMAGES_DB") private val imageDB: CollectionReference,
    @Named("STORAGE") private val storage: StorageReference,
) : BaseViewModel(){
    val commandId: String = CommandArgs(savedStateHandle).id
    val isCreate = CommandArgs(savedStateHandle).isCreate

    init {
        Timber.d("CommandDetailsScreenViewModel created with commandId: $commandId, isCreate: $isCreate")
    }

    private val commandRef: MutableState<DocumentReference> = mutableStateOf(
        if(!isCreate) commandDB.document(commandId)
        else commandDB.document()
    )

    private val commandImageStorage: State<StorageReference> = derivedStateOf {
        storage.child("command/images/paymentProof/${commandRef.value.id}")
    }
    private val commandPreviewImagesStorage: State<StorageReference> = derivedStateOf {
        storage.child("command/images/previews/${commandRef.value.id}")
    }

    private val _changeMade = mutableStateOf(false)
    val changeMade = _changeMade as State<Boolean>

    data class PreviewsData(
        val previews: List<FirebaseStorageFileData?>?,
        val node: String,
        val loading: Boolean = true
    )

    /**
     * the list can contain a null string if we failed to get its download url
     */
    var previews: List<PreviewsData> by mutableStateOf(listOf())
    
    // StateFlow for images data
    private val _imagesData = MutableStateFlow<List<ImageData>>(emptyList())
    val imagesData: StateFlow<List<ImageData>> = _imagesData.asStateFlow()
    
    init {
        loadImagesData()
    }
    
    private fun loadImagesData() {
        launchWithErrorHandling(showLoading = false) {
            try {
                val snapshot = imageDB.whereEqualTo("upload", true).get().await()
                val images = snapshot.documents.mapNotNull { doc ->
                    try {
                        doc.toObject(ImageData::class.java)?.copy(id = doc.id)
                    } catch (e: RuntimeException) {
                        Timber.e(e, "Failed to deserialize image data for document: ${doc.id}")
                        null
                    }
                }
                _imagesData.value = images
            } catch (e: Exception) {
                Timber.e(e, "Failed to load images data")
                _imagesData.value = emptyList()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Timber.i("CommandDetailsScreenViewModel cleared")
    }

    fun setChangeMade(value: Boolean){
        _changeMade.value = value
    }

    fun loadPreviews(command: Command) {
        launchWithErrorHandling(showLoading = false) {
            try {
                val temp = arrayListOf<PreviewsData>()
                command.products.map { it.previewsPathName }.forEach {
                    it?.let { path ->
                        temp.add(loadPreviewInternal(path))
                    }
                }
                previews = temp
            } catch (e: Exception) {
                Timber.e(e, "Failed to load previews")
                handleError(e)
            }
        }
    }

    private suspend fun loadPreviewInternal(node: String): PreviewsData {
        return try {
            previews = previews.map { if(it.node == node) it.copy(loading = true) else it }
            val result = commandPreviewImagesStorage.value.child(node).listAll().await()
            val previewsList = result.items.map { storage ->
                FirebaseStorageFileData(
                    url = storage.downloadUrl.await().toString(),
                    name = storage.name,
                    path = storage.path
                )
            }
            PreviewsData(
                node = node,
                previews = previewsList,
                loading = false
            )
        } catch (e: Exception) {
            previews = previews.map { if(it.node == node) it.copy(loading = false) else it }
            throw e
        }
    }

    fun savePaymentProof(fileUri: Uri, onSuccess: (Uri) -> Unit) {
        launchWithErrorHandling {
            try {
                val res = commandImageStorage.value.putFile(fileUri).await()
                val downloadUrl = res.storage.downloadUrl.await()
                showSuccess("Payment proof uploaded successfully")
                onSuccess(downloadUrl)
            } catch (e: Exception) {
                Timber.e(e, "Failed to save payment proof")
                handleError(e)
            }
        }
    }

    fun savePreviewImage(
        fileUri: Uri,
        commandPreviewNodeName: String,
        uploadedFileName: String,
        onSuccess: (Uri) -> Unit
    ) {
        launchWithErrorHandling {
            try {
                val res = commandPreviewImagesStorage.value
                    .child(commandPreviewNodeName)
                    .child(uploadedFileName)
                    .putFile(fileUri).await()
                val downloadUrl = res.storage.downloadUrl.await()
                showSuccess("Preview image uploaded successfully")
                onSuccess(downloadUrl)
            } catch (e: Exception) {
                Timber.e(e, "Failed to save preview image")
                handleError(e)
            }
        }
    }

    fun updateCommand(command: Command) {
        launchWithErrorHandling {
            try {
                commandRef.value.set(command).await()
                if(isCreate) {
                    commandRef.value = commandDB.document()
                }
                showSuccess(if (isCreate) "Command created successfully" else "Command updated successfully")
                setChangeMade(false)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update command")
                handleError(e)
            }
        }
    }

    fun deleteCommand() {
        launchWithErrorHandling {
            try {
                commandRef.value.delete().await()
                showSuccess("Command deleted successfully")
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete command")
                handleError(e)
            }
        }
    }

    fun deleteFile(path: String) {
        launchWithErrorHandling {
            try {
                storage.child(path).delete().await()
                showSuccess("File deleted successfully")
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete file: $path")
                handleError(e)
            }
        }
    }
    
    fun loadCommand(onSuccess: (Command) -> Unit) {
        launchWithErrorHandling {
            try {
                Timber.d("Loading command with id: ${commandRef.value.id}")
                val snapshot = commandRef.value.get().await()
                val command = snapshot.toObject(Command::class.java)?.copy(id = snapshot.id)
                command?.let {
                    onSuccess(it)
                } ?: run {
                    handleError(Exception("Command not found"))
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to load command")
                handleError(e)
            }
        }
    }

}
