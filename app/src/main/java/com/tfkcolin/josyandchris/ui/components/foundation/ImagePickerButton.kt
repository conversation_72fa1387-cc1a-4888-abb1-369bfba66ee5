package com.tfkcolin.josyandchris.ui.components.foundation

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.R

@Composable
fun ImagePickerButton(
    modifier: Modifier = Modifier,
    text: String = "Choisir une Image",
    onPickImage: (Uri) -> Unit
) {
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent(),
        onResult = { uri -> uri?.let { onPickImage(uri) } }
    )
    OutlinedButton(
        modifier = modifier,
        onClick = { imagePicker.launch("image/*") },
        shape = RoundedCornerShape(10)
    ) {
        Icon(
            modifier = Modifier.padding(end = 5.dp),
            painter = painterResource(id = R.drawable.baseline_image_search_24),
            contentDescription = null
        )
        Text(
            text = text,
            textAlign = TextAlign.Center
        )
    }
}