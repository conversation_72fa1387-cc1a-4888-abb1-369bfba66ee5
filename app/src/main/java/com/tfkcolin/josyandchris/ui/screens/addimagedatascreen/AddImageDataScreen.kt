package com.tfkcolin.josyandchris.ui.screens.addimagedatascreen

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.paging.LoadState
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.components.JACImage
import com.tfkcolin.josyandchris.ui.components.JACImageDataCreator
import com.tfkcolin.josyandchris.ui.components.JACTopAppBar
import com.tfkcolin.josyandchris.ui.components.Title
import com.tfkcolin.josyandchris.ui.components.foundation.BoxWithLoading
import com.tfkcolin.josyandchris.ui.components.foundation.JACSelect
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import com.tfkcolin.josyandchris.ui.states.ImageScreenUiState
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import androidx.compose.runtime.collectAsState
import androidx.hilt.navigation.compose.hiltViewModel

import com.tfkcolin.josyandchris.util.query.QueryFilters

@Composable
fun AddImageDataScreen(
    modifier: Modifier = Modifier,
    imageData: ImageData,
    paginatedImages: LazyPagingItems<ImageData>,
    uiState: ImageScreenUiState,
    onNavigateToEditImageScreen: (String) -> Unit,
    gridState: LazyGridState = rememberLazyGridState(),
    onUploadImage: (name: String, id: String, url: String) -> Unit,
    onImageDataChanged: (ImageData) -> Unit,
    onAddImageData: suspend (ImageData) -> Unit,
    onFilterChange: (category: String?, genre: String?, uploaded: Boolean?) -> Unit,
    onDismissError: () -> Unit,
    distinctCategories: List<String>,
    distinctGenres: List<String>,
    filters: QueryFilters // New parameter
) {
    val scope = rememberCoroutineScope()
    
    val pagingState = paginatedImages.loadState.refresh
    val isLoading = uiState.imageOperationState is ImageOperationState.Loading

    val selectedGenre = (filters.additionalFilters["genre"] as? String) ?: "tous"
    val selectedCategory = (filters.additionalFilters["category"] as? String) ?: "tous"
    val showUploaded = (filters.additionalFilters["upload"] as? Boolean)

    var genreExpanded by remember { mutableStateOf(false) }
    var categoryExpanded by remember { mutableStateOf(false) }

    val columnCount = 2
    BoxWithLoading(loading = pagingState is LoadState.Loading) {
        LazyVerticalGrid(
            modifier = modifier,
            state = gridState,
            columns = GridCells.Fixed(columnCount),
            horizontalArrangement = Arrangement.Center
        ){
            item(span = {
                GridItemSpan(maxLineSpan)
            }) {
                Column(
                    modifier = modifier
                        .padding(vertical = 5.dp)
                        .fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ){
                    Text(
                        text = "Nouvelle image",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        textAlign = TextAlign.Center
                    )
                JACImageDataCreator(
                    modifier = Modifier
                        .padding(4.dp)
                        .fillMaxWidth(),
                    imageData = imageData,
                    onImageDataChange = onImageDataChanged,
                    errorMessage = if (uiState.imageOperationState is ImageOperationState.Error) uiState.imageOperationState.message else null,
                    onDismissError = onDismissError
                )
                Button(
                    onClick = {
                        scope.launch {
                            onAddImageData(imageData.copy(
                                category = imageData.category.trim().lowercase(),
                                genre = imageData.genre.trim().lowercase()
                            ))
                        }
                    },
                    enabled = !isLoading,
                    shape = RoundedCornerShape(10.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    BoxWithLoading(loading = isLoading) {
                        Row(verticalAlignment = Alignment.CenterVertically){
                            Icon(
                                modifier = Modifier.padding(end = 8.dp),
                                imageVector = Icons.Default.Check,
                                contentDescription = null
                            )
                            Text(text = "Sauvegarder l'image")
                        }
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Images disponibles",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    textAlign = TextAlign.Center
                )
            }
        }
        item(span = {
            GridItemSpan(maxLineSpan)
        }) {
            Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    JACSelect(
                        modifier = Modifier.weight(1f),
                        selected = selectedGenre,
                        expanded = genreExpanded,
                        onExpandedChange = { genreExpanded = it }
                    ) {
                        distinctGenres.forEach { genre -> // Use distinctGenres
                            DropdownMenuItem(
                                text = {
                                    Text(text = genre)
                                },
                                onClick = {
                                    onFilterChange(selectedCategory.takeIf { it != "tous" }, genre.takeIf { it != "tous" }, showUploaded)
                                    genreExpanded = false
                                }
                            )
                        }
                    }
                    JACSelect(
                        modifier = Modifier.weight(1f),
                        selected = selectedCategory,
                        expanded = categoryExpanded,
                        onExpandedChange = { categoryExpanded = it }
                    ) {
                        distinctCategories.forEach { category -> // Use distinctCategories
                            DropdownMenuItem(
                                text = {
                                    Text(text = category)
                                },
                                onClick = {
                                    onFilterChange(category.takeIf { it != "tous" }, selectedGenre.takeIf { it != "tous" }, showUploaded)
                                    categoryExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Statut:",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    
                    FilterChip(
                        selected = showUploaded == null,
                        onClick = { onFilterChange(selectedCategory.takeIf { it != "tous" }, selectedGenre.takeIf { it != "tous" }, null) },
                        label = { Text("Tous") },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = MaterialTheme.colorScheme.primary,
                            selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                        )
                    )
                    
                    FilterChip(
                        selected = showUploaded == true,
                        onClick = { onFilterChange(selectedCategory.takeIf { it != "tous" }, selectedGenre.takeIf { it != "tous" }, true) },
                        label = { Text("Uploadé") },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = MaterialTheme.colorScheme.primary,
                            selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                        )
                    )
                    
                    FilterChip(
                        selected = showUploaded == false,
                        onClick = { onFilterChange(selectedCategory.takeIf { it != "tous" }, selectedGenre.takeIf { it != "tous" }, false) },
                        label = { Text("En attente") },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = MaterialTheme.colorScheme.primary,
                            selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                        )
                    )
                }
            }
        }
        item(span = {
            GridItemSpan(maxLineSpan)
        }) {
            if(pagingState is LoadState.NotLoading && paginatedImages.itemCount == 0){
                Box(
                    modifier
                        .padding(10.dp)
                        .fillMaxWidth()) {
                    Text(
                        text = "Aucun élément trouvé. Essayez de sélectionner une autre catégorie ou un autre genre.",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color.Black.lighten(.4f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        // Using either the paginated data or the filtered list
        val itemCount = paginatedImages.itemCount
        items(itemCount) { index ->
            val image = paginatedImages[index]
            if (image != null) {
                ImageDataItem(
                    onNavigateToEditImageScreen = onNavigateToEditImageScreen,
                    image = image,
                    onUploadImage = onUploadImage,
                    isUploading = uiState.uploadingImageId == image.id
                )
            } else {
                Box(modifier = Modifier.size(200.dp), contentAlignment = Alignment.Center) {
                    CircularProgressIndicator()
                }
            }
        }
        
        // Show loading state
        item(span = { GridItemSpan(maxLineSpan) }) {
            when (paginatedImages.loadState.append) {
                is LoadState.Loading -> {
                    Box(modifier = Modifier.fillMaxWidth().padding(16.dp), contentAlignment = Alignment.Center) {
                        CircularProgressIndicator()
                    }
                }
                is LoadState.Error -> {
                    Text(
                        "Erreur lors du chargement des données",
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.fillMaxWidth().padding(16.dp),
                        textAlign = TextAlign.Center
                    )
                }
                else -> {}
            }
        }
        item(span = {
            GridItemSpan(maxLineSpan)
        }) {
            Box(modifier = Modifier.height(ButtonDefaults.MinHeight))
        }
    }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ImageDataItem(
    onNavigateToEditImageScreen: (String) -> Unit,
    image: ImageData,
    onUploadImage: (name: String, id: String, url: String) -> Unit,
    isUploading: Boolean = false
) {
    OutlinedCard(
        modifier = Modifier
            .padding(8.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        onClick = {
            onNavigateToEditImageScreen(image.id)
        },
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline
        ),
        elevation = CardDefaults.outlinedCardElevation(
            defaultElevation = 4.dp
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Ajouter le: ${
                    SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(
                        image.created
                    )
                }",
                style = MaterialTheme.typography.labelMedium,
                maxLines = 1,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                overflow = TextOverflow.Ellipsis
            )
            JACImage(
                modifier = Modifier
                    .height(200.dp)
                    .fillMaxWidth(),
                data = image.url
            )
            if (image.category.isNotEmpty() || image.genre.isNotEmpty()) {
                Text(
                    text = "${image.category}${if (image.genre.isNotEmpty()) "," else ""}${" " + image.genre}",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.primary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Bold
                )
            }
            if (!image.upload) {
                Button(
                    onClick = {
                        image.url?.let { url ->
                            onUploadImage(
                                image.name ?: UUID.randomUUID().toString(),
                                image.id,
                                url
                            )
                        }
                    },
                    shape = RoundedCornerShape(8.dp),
                    enabled = !isUploading && image.url != null,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    BoxWithLoading(loading = isUploading) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                modifier = Modifier.padding(end = 8.dp),
                                imageVector = Icons.AutoMirrored.Filled.Send,
                                contentDescription = null
                            )
                            Text(text = "Upload")
                        }
                    }
                }
            } else {
                Text(
                    text = "Uploadé",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AddImageDataScreenPreview(){
    JACTheme {
        Scaffold(
            topBar = {
                JACTopAppBar(onIconClick = { /*TODO*/ }, isLogIn = true) {}
            }
        ) {
            val paginatedImages = flowOf(PagingData.from(listOf(
                ImageData(id = "1", category = "sac", genre = "homme", upload = true),
                ImageData(id = "2", category = "montre", genre = "femme", upload = false)
            ))).collectAsLazyPagingItems()

            AddImageDataScreen(
                modifier = Modifier
                    .padding(start = 5.dp, end = 5.dp)
                    .padding(it),
                paginatedImages = paginatedImages,
                imageData = ImageData(),
                uiState = ImageScreenUiState(),
                onImageDataChanged = {},
                onAddImageData = {},
                onUploadImage = { _, _, _ -> },
                onNavigateToEditImageScreen = {},
                onFilterChange = { _, _, _ -> },
                onDismissError = {},
                distinctCategories = listOf("tous", "sac", "montre"),
                distinctGenres = listOf("tous", "homme", "femme"),
                filters = QueryFilters()
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun ImageDataItemPreview(){
    JACTheme {
        VerticalGrid {
            ImageDataItem(
                onNavigateToEditImageScreen = {},
                image = ImageData(
                    category = "sac",
                    genre = "homme",
                    upload = true
                ),
                onUploadImage = { _, _, _ -> }
            )
            ImageDataItem(
                onNavigateToEditImageScreen = {},
                image = ImageData(
                    category = "montre",
                    genre = "femme",
                    upload = false
                ),
                onUploadImage = { _, _, _ -> }
            )
        }
    }
}
