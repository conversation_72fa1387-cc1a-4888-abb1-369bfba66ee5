package com.tfkcolin.josyandchris.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.AndroidViewModel
import android.app.Application
import com.tfkcolin.josyandchris.domain.exception.DomainException
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import com.tfkcolin.josyandchris.JACApplication

/**
 * Base ViewModel class that provides common error handling functionality
 */
abstract class BaseViewModel : AndroidViewModel(app()) {
    
    companion object {
        private lateinit var applicationInstance: Application
        
        fun initialize(application: Application) {
            applicationInstance = application
        }
        
        fun app(): Application {
            return applicationInstance
        }
    }
    
    // SnackbarState flow for UI error/success messages
    private val _snackbarState = MutableSharedFlow<SnackbarState>()
    val snackbarState: SharedFlow<SnackbarState> = _snackbarState.asSharedFlow()
    
    // Loading state
    private val _isLoading = MutableSharedFlow<Boolean>()
    val isLoading: SharedFlow<Boolean> = _isLoading.asSharedFlow()
    
    /**
     * Default exception handler that logs errors and shows snackbar
     */
protected val defaultExceptionHandler = (getApplication() as JACApplication).globalHandler ?: CoroutineExceptionHandler { _, exception ->
        handleError(exception)
    }
    
    /**
     * Launch a coroutine with default error handling
     */
    protected fun launchWithErrorHandling(
        showLoading: Boolean = true,
        block: suspend CoroutineScope.() -> Unit
    ) {
        viewModelScope.launch(defaultExceptionHandler) {
            if (showLoading) setLoading(true)
            try {
                block()
            } finally {
                if (showLoading) setLoading(false)
            }
        }
    }
    
    /**
     * Handle a Result with automatic error handling
     */
    protected suspend fun <T> Result<T>.handleResult(
        onSuccess: suspend (T) -> Unit,
        onError: (suspend (DomainException) -> Unit)? = null
    ) {
        fold(
            onSuccess = { value ->
                onSuccess(value)
            },
            onFailure = { throwable ->
                val domainException = throwable as? DomainException 
                    ?: DomainException.UnknownException(
                        message = throwable.message ?: "Unknown error",
                        cause = throwable
                    )
                
                if (onError != null) {
                    onError(domainException)
                } else {
                    handleError(domainException)
                }
            }
        )
    }
    
    /**
     * Show success message via snackbar
     */
    protected fun showSuccess(message: String) {
        viewModelScope.launch {
            _snackbarState.emit(SnackbarState(
                message = message,
                type = SnackbarType.SUCCESS
            ))
        }
    }
    
    /**
     * Show error message via snackbar
     */
    protected fun showError(message: String) {
        viewModelScope.launch {
            _snackbarState.emit(SnackbarState(
                message = message,
                type = SnackbarType.ERROR
            ))
        }
    }
    
    /**
     * Show info message via snackbar
     */
    protected fun showInfo(message: String) {
        viewModelScope.launch {
            _snackbarState.emit(SnackbarState(
                message = message,
                type = SnackbarType.INFO
            ))
        }
    }
    
    /**
     * Set loading state
     */
    protected fun setLoading(isLoading: Boolean) {
        viewModelScope.launch {
            _isLoading.emit(isLoading)
        }
    }
    
    /**
     * Handle errors based on exception type
     */
    protected open fun handleError(throwable: Throwable) {
        Timber.e(throwable, "Error in ${this::class.simpleName}")
        
        val errorMessage = when (throwable) {
            is DomainException.NetworkException -> throwable.message
            is DomainException.AuthException -> throwable.message
            is DomainException.DataException -> throwable.message
            is DomainException.StorageException -> throwable.message
            is DomainException.BusinessException -> throwable.message
            is DomainException -> throwable.message
            else -> "An unexpected error occurred"
        }
        
        showError(errorMessage)
    }
    
    /**
     * Execute a suspend function with Result handling
     */
    protected suspend fun <T> executeSafely(
        showLoading: Boolean = true,
        onSuccess: (suspend (T) -> Unit)? = null,
        onError: (suspend (DomainException) -> Unit)? = null,
        block: suspend () -> Result<T>
    ) {
        if (showLoading) setLoading(true)
        try {
            block().handleResult(
                onSuccess = { value ->
                    onSuccess?.invoke(value)
                },
                onError = onError
            )
        } catch (e: Exception) {
            handleError(e)
        } finally {
            if (showLoading) setLoading(false)
        }
    }
}
