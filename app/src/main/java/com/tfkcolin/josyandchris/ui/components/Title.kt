package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.util.lighten

@Composable
fun Title(
    modifier: Modifier = Modifier,
    text: String,
    color: Color = Color.Black.lighten(.23f)
) {
    Text(
        modifier = modifier.padding(vertical = 10.dp),
        text = text,
        style = MaterialTheme.typography.headlineSmall,
        fontWeight = FontWeight.Bold,
        color = color,
        textAlign = TextAlign.Center
    )
}