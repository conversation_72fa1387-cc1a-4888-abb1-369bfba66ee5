package com.tfkcolin.josyandchris.ui.screens.commandlist

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.components.*
import timber.log.Timber
import com.tfkcolin.josyandchris.ui.components.modern.ModernCommandCard
import com.tfkcolin.josyandchris.ui.components.foundation.DateRangePresets
import com.tfkcolin.josyandchris.ui.data.CommandUiFilterState
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun CommandListScreen(
    modifier: Modifier = Modifier,
    onNavigateToCommandDetail: (String) -> Unit,
    onNavigateToCreateCommand: () -> Unit = {},
    preAppliedClientFilter: String? = null,
    preAppliedStepFilter: CommandStep? = null,
    viewModel: CommandListViewModel = hiltViewModel()
) {
    var isSelectionMode by remember { mutableStateOf(false) }
    var selectedCommandIds by remember { mutableStateOf(setOf<String>()) }

    LaunchedEffect(preAppliedClientFilter, preAppliedStepFilter) {
        if (preAppliedClientFilter != null || preAppliedStepFilter != null) {
            val currentState = viewModel.uiFilterState.value
            viewModel.updateUiFilterState(
                currentState.copy(
                    searchTerm = preAppliedClientFilter ?: currentState.searchTerm,
                    selectedStep = preAppliedStepFilter ?: currentState.selectedStep
                )
            )
        }
    }

    val initialFilterState by viewModel.uiFilterState.collectAsState()
    var uiFilterState by remember { mutableStateOf(initialFilterState) }

    LaunchedEffect(initialFilterState) {
        uiFilterState = initialFilterState
    }

    val paginatedCommands = viewModel.commandsPagingData.collectAsLazyPagingItems()
    val listState = rememberLazyListState()

    val refreshing = paginatedCommands.loadState.refresh is LoadState.Loading
    val pullRefreshState = rememberPullRefreshState(
        refreshing = refreshing,
        onRefresh = { paginatedCommands.refresh() }
    )

    var searchQuery by remember { mutableStateOf(uiFilterState.searchTerm) }
    var showFilters by remember { mutableStateOf(false) }

    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }

    LaunchedEffect(searchQuery) {
        if (searchQuery != uiFilterState.searchTerm) {
            delay(300)
            uiFilterState = uiFilterState.copy(searchTerm = searchQuery)
        }
    }

    Scaffold(
        topBar = {
            if (isSelectionMode) {
                SelectionTopAppBar(
                    selectedCount = selectedCommandIds.size,
                    onClearSelection = {
                        isSelectionMode = false
                        selectedCommandIds = emptySet()
                    },
                    onSelectAll = {
                        selectedCommandIds = (0 until paginatedCommands.itemCount).mapNotNull { index ->
                            paginatedCommands[index]?.id
                        }.toSet()
                    },
                    onDeleteSelected = { /* Handle bulk delete */ },
                    modifier = Modifier.zIndex(3f)
                )
            } else {
                TopAppBar(
                    title = { Text("Commands") },
                    actions = {
                        IconButton(onClick = { showFilters = true }) {
                            Icon(
                                imageVector = if (uiFilterState.hasActiveFilters()) Icons.Default.FilterList else Icons.Default.FilterAlt,
                                contentDescription = "Filter commands"
                            )
                        }
                    }
                )
            }
        },
        floatingActionButton = {
            if (!isSelectionMode) {
                FloatingActionButton(
                    onClick = onNavigateToCreateCommand,
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                ) {
                    Icon(imageVector = Icons.Default.Add, contentDescription = "Create Command")
                }
            }
        },
        modifier = modifier
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .pullRefresh(pullRefreshState)
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                if (paginatedCommands.loadState.refresh is LoadState.Loading && paginatedCommands.itemCount == 0) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillParentMaxSize()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                }

                when (val refreshState = paginatedCommands.loadState.refresh) {
                    is LoadState.Error -> {
                        item {
                            ErrorStateCard(
                                message = refreshState.error.localizedMessage ?: "Failed to load commands",
                                onRetry = { paginatedCommands.retry() },
                                modifier = Modifier.padding(16.dp)
                            )
                        }
                    }
                    else -> Unit
                }

                items(paginatedCommands.itemCount) { index ->
                    val command = paginatedCommands[index]
                    command?.let { cmd ->
                        val haptic = LocalHapticFeedback.current
                        val isSelected = selectedCommandIds.contains(cmd.id)
                        val commandId = cmd.id

                        SelectableCommandCard(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 8.dp)
                                .pointerInput(commandId) {
                                    detectTapGestures(
                                        onLongPress = {
                                            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                            if (!isSelectionMode) {
                                                isSelectionMode = true
                                                selectedCommandIds = setOf(commandId)
                                            } else {
                                                selectedCommandIds = if (isSelected) selectedCommandIds - commandId else selectedCommandIds + commandId
                                            }
                                        },
                                        onTap = {
                                            if (isSelectionMode) {
                                                selectedCommandIds = if (isSelected) selectedCommandIds - commandId else selectedCommandIds + commandId
                                            } else {
                                                Timber.d("Navigating to command detail with id: $commandId")
                                                onNavigateToCommandDetail(commandId)
                                            }
                                        }
                                    )
                                },
                            command = cmd,
                            isSelected = isSelected,
                            isSelectionMode = isSelectionMode,
                            onClick = {
                                if (isSelectionMode) {
                                    selectedCommandIds = if (isSelected) selectedCommandIds - commandId else selectedCommandIds + commandId
                                } else {
                                    Timber.d("Navigating to command detail with id: $commandId")
                                    onNavigateToCommandDetail(commandId)
                                }
                            },
                            onViewDetails = {
                                Timber.d("Navigating to command detail with id: $commandId")
                                onNavigateToCommandDetail(commandId)
                            }
                        )
                    }
                }

                when (val appendState = paginatedCommands.loadState.append) {
                    is LoadState.Loading -> item { LoadingIndicator(modifier = Modifier.fillMaxWidth().padding(16.dp)) }
                    is LoadState.Error -> item { RetryButton(onRetry = { paginatedCommands.retry() }, modifier = Modifier.fillMaxWidth().padding(16.dp)) }
                    else -> Unit
                }

                if (paginatedCommands.loadState.refresh is LoadState.NotLoading && paginatedCommands.itemCount == 0) {
                    item {
                        EnhancedEmptyStateCard(
                            title = if (uiFilterState.hasActiveFilters()) "No Commands Match Your Filters" else "Ready to Start?",
                            subtitle = if (uiFilterState.hasActiveFilters()) "Try adjusting your search criteria or filters." else "Create your first command to get started.",
                            actionButtonText = if (uiFilterState.hasActiveFilters()) "Clear Filters" else "Create Command",
                            onActionClick = if (uiFilterState.hasActiveFilters()) {
                                {
                                    uiFilterState = CommandUiFilterState()
                                    searchQuery = ""
                                }
                            } else onNavigateToCreateCommand,
                            showCreateAction = !uiFilterState.hasActiveFilters(),
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }

                item { Spacer(modifier = Modifier.height(88.dp)) }
            }

            PullRefreshIndicator(
                refreshing = refreshing,
                state = pullRefreshState,
                modifier = Modifier.align(Alignment.TopCenter).zIndex(2f),
                contentColor = MaterialTheme.colorScheme.primary
            )

            AnimatedVisibility(
                visible = uiFilterState.hasActiveFilters() && !showFilters,
                enter = slideInVertically() + fadeIn(),
                exit = slideOutVertically() + fadeOut(),
                modifier = Modifier.align(Alignment.BottomStart).padding(16.dp)
            ) {
                ActiveFilterChips(
                    uiFilterState = uiFilterState,
                    onFilterStateChange = { uiFilterState = it },
                    onClearAllFilters = {
                        uiFilterState = CommandUiFilterState()
                        searchQuery = ""
                    }
                )
            }
        }
    }

    if (showFilters) {
        FilterBottomSheet(
            filterState = uiFilterState,
            presetFilters = getPresetFilters(),
            onFilterStateChange = { newState -> uiFilterState = newState },
            onApply = { appliedState ->
                uiFilterState = appliedState
                searchQuery = appliedState.searchTerm
                showFilters = false
            },
            onClear = {
                uiFilterState = CommandUiFilterState(
                    recentClientChips = uiFilterState.recentClientChips,
                    recentLocationChips = uiFilterState.recentLocationChips
                )
                searchQuery = ""
                showFilters = false
            },
            onDismiss = { showFilters = false }
        )
    }
}

@Composable
private fun ActiveFilterChips(
    uiFilterState: CommandUiFilterState,
    onFilterStateChange: (CommandUiFilterState) -> Unit,
    onClearAllFilters: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        if (uiFilterState.searchTerm.isNotBlank()) {
            item {
                FilterChip(
                    selected = true,
                    onClick = { onFilterStateChange(uiFilterState.copy(searchTerm = "")) },
                    label = { Text(uiFilterState.searchTerm) },
                    trailingIcon = { Icon(imageVector = Icons.Default.Close, contentDescription = "Remove filter") }
                )
            }
        }

        uiFilterState.selectedStep?.let { step ->
            item {
                FilterChip(
                    selected = true,
                    onClick = { onFilterStateChange(uiFilterState.copy(selectedStep = null)) },
                    label = { Text(step.step) },
                    trailingIcon = { Icon(imageVector = Icons.Default.Close, contentDescription = "Remove filter") }
                )
            }
        }

        if (uiFilterState.hasActiveFilters()) {
            item {
                OutlinedButton(
                    onClick = onClearAllFilters,
                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                ) {
                    Text("Clear All")
                }
            }
        }
    }
}

@Composable
private fun ErrorStateCard(message: String, onRetry: () -> Unit, modifier: Modifier = Modifier) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(imageVector = Icons.Default.Error, contentDescription = null, tint = MaterialTheme.colorScheme.error)
            Text(text = message, style = MaterialTheme.typography.bodyMedium, color = MaterialTheme.colorScheme.onErrorContainer)
            Button(onClick = onRetry) { Text("Retry") }
        }
    }
}

@Composable
private fun LoadingIndicator(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        CircularProgressIndicator(modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(12.dp))
        Text(text = "Loading more...", style = MaterialTheme.typography.bodyMedium, color = MaterialTheme.colorScheme.onSurfaceVariant)
    }
}

@Composable
private fun RetryButton(onRetry: () -> Unit, modifier: Modifier = Modifier) {
    OutlinedButton(onClick = onRetry, modifier = modifier) {
        Icon(imageVector = Icons.Default.Refresh, contentDescription = null, modifier = Modifier.size(16.dp))
        Spacer(modifier = Modifier.width(8.dp))
        Text("Retry")
    }
}

@Composable
private fun EnhancedEmptyStateCard(
    title: String,
    subtitle: String,
    actionButtonText: String,
    onActionClick: () -> Unit,
    showCreateAction: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            Icon(
                imageVector = if (showCreateAction) Icons.Default.Add else Icons.Default.FilterAlt,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(text = title, style = MaterialTheme.typography.headlineSmall, fontWeight = FontWeight.Bold, color = MaterialTheme.colorScheme.onSurface)
                Text(text = subtitle, style = MaterialTheme.typography.bodyMedium, color = MaterialTheme.colorScheme.onSurfaceVariant, textAlign = androidx.compose.ui.text.style.TextAlign.Center)
            }
            
            Button(
                onClick = onActionClick,
                modifier = Modifier.fillMaxWidth(0.8f),
                elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
            ) {
                Icon(imageVector = if (showCreateAction) Icons.Default.Add else Icons.Default.ClearAll, contentDescription = null, modifier = Modifier.size(20.dp))
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = actionButtonText, style = MaterialTheme.typography.titleSmall, fontWeight = FontWeight.Medium)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SelectableCommandCard(
    modifier: Modifier = Modifier,
    command: Command,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onViewDetails: () -> Unit
) {
    val backgroundColor = if (isSelected) MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f) else MaterialTheme.colorScheme.surface
    
    Card(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        border = if (isSelected) androidx.compose.foundation.BorderStroke(2.dp, MaterialTheme.colorScheme.primary) else null
    ) {
        Box {
            ModernCommandCard(
                command = command,
                onClick = onClick,
                onViewDetails = onViewDetails,
                modifier = Modifier.background(Color.Transparent)
            )
            
            if (isSelectionMode) {
                Box(modifier = Modifier.align(Alignment.TopEnd).padding(16.dp)) {
                    if (isSelected) {
                        Surface(
                            shape = RoundedCornerShape(20.dp),
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(28.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Selected",
                                modifier = Modifier.padding(6.dp).size(16.dp),
                                tint = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    } else {
                        Surface(
                            shape = RoundedCornerShape(20.dp),
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                            modifier = Modifier.size(28.dp)
                        ) {
                            Box(modifier = Modifier.fillMaxSize())
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SelectionTopAppBar(
    selectedCount: Int,
    onClearSelection: () -> Unit,
    onSelectAll: () -> Unit,
    onDeleteSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = { Text(text = "$selectedCount selected", style = MaterialTheme.typography.titleLarge, fontWeight = FontWeight.Medium) },
        navigationIcon = {
            IconButton(onClick = onClearSelection) {
                Icon(imageVector = Icons.Default.Close, contentDescription = "Clear selection")
            }
        },
        actions = {
            IconButton(onClick = onSelectAll) {
                Icon(imageVector = Icons.Default.SelectAll, contentDescription = "Select all")
            }
            IconButton(onClick = onDeleteSelected) {
                Icon(imageVector = Icons.Default.Delete, contentDescription = "Delete selected", tint = MaterialTheme.colorScheme.error)
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer,
            titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer,
            navigationIconContentColor = MaterialTheme.colorScheme.onPrimaryContainer,
            actionIconContentColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        modifier = modifier
    )
}

private fun getPresetFilters(): Map<String, CommandUiFilterState> {
    return mapOf(
        "Recent Orders" to CommandUiFilterState(dateRange = DateRangePresets.lastDays(30)),
        "Pending Payment" to CommandUiFilterState(paymentStatus = false),
        "Completed Orders" to CommandUiFilterState(selectedStep = CommandStep.OK, paymentStatus = true),
        "This Month" to CommandUiFilterState(dateRange = DateRangePresets.thisMonth()),
        "Last Month" to CommandUiFilterState(dateRange = DateRangePresets.lastMonth()),
        "Orders in Progress" to CommandUiFilterState(selectedStep = CommandStep.BUYING),
        "Ready for Delivery" to CommandUiFilterState(selectedStep = CommandStep.READY)
    )
}

private fun CommandUiFilterState.hasActiveFilters(): Boolean {
    return searchTerm.isNotBlank() ||
           selectedStep != null ||
           proofUploaded != null ||
           dateRange != null ||
           clientName.isNotBlank() ||
           clientPhone.isNotBlank() ||
           clientCountry.isNotBlank() ||
           clientCity.isNotBlank() ||
           paymentStatus != null
}

private fun CommandUiFilterState.getActiveFilterCount(): Int {
    var count = 0
    if (searchTerm.isNotBlank()) count++
    if (selectedStep != null) count++
    if (proofUploaded != null) count++
    if (dateRange != null) count++
    if (clientName.isNotBlank()) count++
    if (clientPhone.isNotBlank()) count++
    if (clientCountry.isNotBlank()) count++
    if (clientCity.isNotBlank()) count++
    if (paymentStatus != null) count++
    return count
}
