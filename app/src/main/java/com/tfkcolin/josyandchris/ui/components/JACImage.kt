package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil.compose.AsyncImage
import coil.request.ImageRequest

/**
 *
 * @param data the data to load.
 * The default supported data types are:
 * . String  (mapped to a Uri )
 * . Uri  ("android.resource", "content", "file", "http", and "https" schemes only)
 * . HttpUrl
 * . File
 * . DrawableRes
 * . Drawable
 * . Bitmap
 * . ByteArray
 * . ByteBuffer
 */
@Composable
fun JACImage(
    modifier: Modifier = Modifier,
    data: Any?,
    contentScale: ContentScale = ContentScale.Crop,
    contentDescription: String? = null
) {
    Box(modifier = modifier){
        AsyncImage(
            model = ImageRequest
                .Builder(LocalContext.current)
                .data(data)
                .crossfade(300)
                .error(android.R.drawable.ic_dialog_alert)
                .build(),
            modifier = Modifier.fillMaxWidth().align(Alignment.Center),
            contentScale = contentScale,
            contentDescription = contentDescription
        )
    }
}