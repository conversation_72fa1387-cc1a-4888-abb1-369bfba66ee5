package com.tfkcolin.josyandchris.ui.screens.commanddetails

import android.net.Uri
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.data.FirebaseStorageFileData
import com.tfkcolin.josyandchris.ui.components.JACImage
import com.tfkcolin.josyandchris.ui.components.Title
import com.tfkcolin.josyandchris.ui.components.foundation.ContentWithLoading
import com.tfkcolin.josyandchris.ui.components.foundation.MultipleImagePickerIconButton
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.dashedBackground
import com.tfkcolin.josyandchris.util.getFilename
import kotlinx.coroutines.launch
import java.util.*
import kotlin.math.ceil

/*private enum class ImageState(val description: String) {
    LOCAL("Local"),
    REMOTE("Server")
}*/

//TODO("add buttons to add and delete images")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACPreviewImage(
    modifier: Modifier = Modifier,
    productName: String,
    showNext: Boolean,
    showPrev: Boolean,
    onNext: () -> Unit,
    onPrev: () -> Unit,
    uploadedImages: List<FirebaseStorageFileData?>?,
    localImages: List<FirebaseStorageFileData>,
    onLocalImagesChanged: (List<FirebaseStorageFileData>) -> Unit,
    onDeleteImage: suspend (path: String) -> Unit,
    onUploadImage: suspend (url: String, filename: String) -> Unit,
){
    var currentImageIndex by remember { mutableIntStateOf(0) }
    val images = remember(localImages, uploadedImages) {
        listOf(
            *localImages.toTypedArray(),
            *(uploadedImages?.filterNotNull() ?: listOf()).toTypedArray()
        )
    }
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    val onPick: (List<Uri>) -> Unit = remember {
        { uriList ->
            onLocalImagesChanged(
                setOf(
                    *localImages.toTypedArray(),
                    *uriList.map { uri ->
                        FirebaseStorageFileData(
                            url = uri.toString(),
                            name = context.getFilename(uri) ?: UUID.randomUUID().toString(),
                            path = ""
                        )
                    }.toTypedArray()
                ).toList()
            )
        }
    }
    val color = MaterialTheme.colorScheme.tertiary
    val listState = rememberLazyListState()

    val selectedImage by remember(currentImageIndex, images) {
        derivedStateOf {
            images.getOrNull(currentImageIndex)
        }
    }

    BoxWithConstraints(modifier = modifier){
        val width = remember { maxWidth }
        val placeHolderIntWidth = remember { 120.dp }
        val nPlaceHolderForWidth = remember(width) { ceil(width.value / placeHolderIntWidth.value).toInt() }
        val nPlaceHolder = remember(nPlaceHolderForWidth, images.size) { nPlaceHolderForWidth - images.size - 1 } // because one the placeholder is the one for adding images and it is always shown so we need to remove 1
        val placeHolder = remember(images.size, nPlaceHolder) {
            if(images.size < nPlaceHolderForWidth)
                (0 until nPlaceHolder).toList()
            else listOf()
        }
        val firstVisibleItemIndex by remember { derivedStateOf { listState.firstVisibleItemIndex } }
        val showFloatingButton by remember(firstVisibleItemIndex, nPlaceHolderForWidth, images.size) {
            derivedStateOf {
                listState.firstVisibleItemIndex + nPlaceHolderForWidth <= images.size
            }
        }
        Surface(
            modifier = Modifier
                .fillMaxSize()
        ){
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ){
                    IconButton(
                        modifier = Modifier.weight(1f),
                        onClick = onPrev,
                        enabled = showPrev
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = null
                        )
                    }
                    Text(
                        modifier = Modifier.weight(1f),
                        text = productName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.secondary,
                        textAlign = TextAlign.Center
                    )
                    IconButton(
                        modifier = Modifier.weight(1f),
                        onClick = onNext,
                        enabled = showNext
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowForward,
                            contentDescription = null
                        )
                    }
                }
                Crossfade(
                    modifier = Modifier
                        .weight(1f),
                    targetState = images.isNotEmpty(), label = "fade preview"
                ) {
                    if(it) {
                        OutlinedCard(
                            modifier = Modifier
                                .padding(vertical = 5.dp)
                                .fillMaxWidth(.8f)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Title(
                                    text = selectedImage?.name ?: "",
                                    color = MaterialTheme.colorScheme.secondary
                                )
                                JACImage(
                                    modifier = Modifier.weight(1f),
                                    data = selectedImage?.url,
                                    contentScale = ContentScale.FillWidth
                                )
                                Row {
                                    if(localImages.contains(selectedImage)){
                                        var loading by remember { mutableStateOf(false) }
                                        TextButton(
                                            onClick = {
                                                loading = true
                                                scope.launch {
                                                    onUploadImage(
                                                        selectedImage!!.url,
                                                        selectedImage!!.name
                                                    )
                                                    onLocalImagesChanged(
                                                        localImages.filter { img -> img != selectedImage }
                                                    )
                                                    loading = false
                                                }
                                            },
                                            content = {
                                                Icon(
                                                    modifier = Modifier.padding(end = 5.dp),
                                                    imageVector = Icons.Default.Share,
                                                    contentDescription = null
                                                )
                                                ContentWithLoading(loading = loading) {
                                                    Text(text = "upload")
                                                }
                                            }
                                        )
                                    }
                                    var deleteLoading by remember { mutableStateOf(false) }
                                    TextButton(
                                        onClick = {
                                            if(localImages.contains(selectedImage)){
                                                onLocalImagesChanged(
                                                    localImages.filter { img -> img != selectedImage }
                                                )
                                            } else {
                                                deleteLoading = true
                                                scope.launch {
                                                    onDeleteImage(selectedImage!!.path)
                                                    deleteLoading = false
                                                }
                                            }
                                        }
                                    ) {
                                        Icon(
                                            modifier = Modifier.padding(end = 5.dp),
                                            imageVector = Icons.Default.Delete,
                                            contentDescription = null
                                        )
                                        ContentWithLoading(loading = deleteLoading) {
                                            Text(text = "Suprimer")
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        Box(modifier = Modifier.fillMaxSize()) {
                            Column(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .then(dashedBackground),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    modifier = Modifier
                                        .padding(vertical = 10.dp)
                                        .fillMaxWidth(.65f),
                                    text = "Vous n'avez encore enregistrez aucune image pour ce produit",
                                    style = MaterialTheme.typography.labelLarge,
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center,
                                    color = color
                                )
                                Icon(
                                    modifier = Modifier.size(200.dp),
                                    painter = painterResource(id = R.drawable.baseline_image_200),
                                    contentDescription = null,
                                    tint = color
                                )
                            }
                        }
                    }
                }
                OutlinedCard(
                    modifier = Modifier
                        .padding(bottom = 2.dp)
                        .fillMaxWidth()
                        .height(150.dp),
                    shape = RectangleShape
                ) {
                    LazyRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                brush = Brush.verticalGradient(
                                    0f to Color.Black,
                                    .5f to Color.Black.copy(alpha = .8f),
                                    1f to Color.Black,
                                )
                            )
                            .padding(5.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        state = listState
                    ) {
                        items(images, { it.url }){ image ->
                            val index = remember { images.indexOf(image) }
                            Box(
                                modifier = Modifier.fillMaxHeight(),
                            ) {
                                Card(
                                    modifier = Modifier
                                        .padding(horizontal = 2.dp, vertical = 4.dp),
                                    onClick = {
                                        currentImageIndex = index
                                    },
                                    border = BorderStroke(
                                        1.dp,
                                        if(currentImageIndex == index)
                                            MaterialTheme.colorScheme.primaryContainer
                                        else Color.Transparent
                                    )
                                ) {
                                    JACImage(
                                        modifier = modifier
                                            .fillMaxHeight(.9f)
                                            .width(120.dp),
                                        data = image.url,
                                        contentScale = ContentScale.FillHeight
                                    )
                                }
                                Badge(modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(5.dp)) {
                                    Text(
                                        text = if(localImages.contains(image)) "locale" else "distant",
                                        fontStyle = FontStyle.Italic
                                    )
                                }
                            }
                        }
                        items(placeHolder){
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .padding(5.dp)
                                    .width(120.dp)
                                    .then(dashedBackground)
                            ) {
                                Icon(
                                    modifier = Modifier
                                        .size(60.dp)
                                        .align(Alignment.Center),
                                    painter = painterResource(id = R.drawable.baseline_image_200),
                                    contentDescription = null,
                                    tint = color
                                )
                            }
                        }
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .padding(5.dp)
                                    .width(120.dp)
                                    .then(dashedBackground)
                            ) {
                                MultipleImagePickerIconButton(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .align(Alignment.Center),
                                    onPickImage = onPick
                                ) {
                                    Icon(
                                        modifier = Modifier.size(60.dp),
                                        imageVector = Icons.Outlined.Add,
                                        contentDescription = null,
                                        tint = color
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        AnimatedVisibility(
            modifier = Modifier
                .padding(5.dp)
                .align(Alignment.TopEnd),
            visible = showFloatingButton,
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            OutlinedCard(
                elevation = CardDefaults.outlinedCardElevation(
                    defaultElevation = 10.dp,
                    pressedElevation = 5.dp
                ),
                colors = CardDefaults.outlinedCardColors(
                    containerColor = Color.Black
                )
            ) {
                MultipleImagePickerIconButton(
                    modifier = Modifier
                        .padding(horizontal = 5.dp),
                    onPickImage = onPick,
                    icon = {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_image_search_24),
                            contentDescription = null,
                            tint = color
                        )
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACPreviewImagePreview(){
    JACTheme {
        JACPreviewImage(
            modifier = Modifier.fillMaxSize(),
            productName = "",
            uploadedImages = listOf(),
            onUploadImage = {_, _ ->},
            localImages = listOf(),
            onDeleteImage = {},
            onLocalImagesChanged = {},
            showNext = false,
            showPrev = false,
            onNext = {},
            onPrev = {}
        )
    }
}