package com.tfkcolin.josyandchris.ui.screens.editimagedata

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import com.tfkcolin.josyandchris.ui.base.BaseViewModel
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.repository.ImageDataRepository
import com.tfkcolin.josyandchris.repository.StorageRepository
import com.tfkcolin.josyandchris.ui.states.ImageScreenUiState
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import android.net.Uri
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class EditImageDataViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val imageDataRepository: ImageDataRepository,
    private val storageRepository: StorageRepository
): BaseViewModel() {
    val imageDataId = ImageDataIdArgs(savedStateHandle).id
    var currentImageData by mutableStateOf(ImageData())
        private set

    private val _uiState = MutableStateFlow(ImageScreenUiState())
    val uiState: StateFlow<ImageScreenUiState> = _uiState.asStateFlow()

    init {
        launchWithErrorHandling {
            imageDataRepository.getImageData(imageDataId).handleResult(
                onSuccess = { imageData ->
                    currentImageData = imageData
                }
            )
        }
    }

    fun imageDataChanged(imageData: ImageData){
        currentImageData = imageData
    }

    fun updateImageData() = launchWithErrorHandling {
        if (currentImageData.category.isBlank() || currentImageData.genre.isBlank()) {
            _uiState.value = _uiState.value.copy(
                imageOperationState = ImageOperationState.Error(
                    message = "Category and Genre cannot be empty.",
                    exception = IllegalArgumentException("Category and Genre cannot be empty.")
                )
            )
            return@launchWithErrorHandling
        }
        imageDataRepository.updateImageData(currentImageData).handleResult(
            onSuccess = {
                _uiState.value = _uiState.value.copy(
                    imageOperationState = ImageOperationState.Success("Image data updated")
                )
                showSuccess("Image data updated")
            },
            onError = { exception ->
                _uiState.value = _uiState.value.copy(
                    imageOperationState = ImageOperationState.Error(
                        exception,
                        "Failed to update image data"
                    )
                )
            }
        )
    }

    fun uploadImage(filename: String, fileUri: Uri) = launchWithErrorHandling {
        _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
        storageRepository.uploadFile(filename, fileUri).handleResult(
            onSuccess = { newUrl ->
                val updatedImageData = currentImageData.copy(
                    url = newUrl,
                    upload = true,
                    path = filename
                )
                imageDataRepository.updateImageData(updatedImageData).handleResult(
                    onSuccess = {
                        currentImageData = updatedImageData
                        _uiState.value = _uiState.value.copy(
                            imageOperationState = ImageOperationState.Success("Image uploaded successfully")
                        )
                        showSuccess("Image uploaded successfully")
                    },
                    onError = { exception ->
                        _uiState.value = _uiState.value.copy(
                            imageOperationState = ImageOperationState.Error(
                                exception,
                                "Failed to update image data after upload"
                            )
                        )
                    }
                )
            },
            onError = { exception ->
                _uiState.value = _uiState.value.copy(
                    imageOperationState = ImageOperationState.Error(
                        exception,
                        "Failed to upload image"
                    )
                )
            }
        )
    }

    fun deleteImage(path: String) = launchWithErrorHandling {
        _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)
        storageRepository.deleteFile(path).handleResult(
            onSuccess = {
                imageDataRepository.deleteImageData(imageDataId).handleResult(
                    onSuccess = {
                        _uiState.value = _uiState.value.copy(
                            imageOperationState = ImageOperationState.Success("Image deleted successfully")
                        )
                        showSuccess("Image deleted successfully")
                    },
                    onError = { exception ->
                        _uiState.value = _uiState.value.copy(
                            imageOperationState = ImageOperationState.Error(
                                exception,
                                "Failed to delete image data"
                            )
                        )
                    }
                )
            },
            onError = { exception ->
                _uiState.value = _uiState.value.copy(
                    imageOperationState = ImageOperationState.Error(
                        exception,
                        "Failed to delete image file"
                    )
                )
            }
        )
    }

    fun replaceImage(oldPath: String, newFilename: String, newFileUri: Uri) = launchWithErrorHandling {
        _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Loading)

        storageRepository.uploadFile(newFilename, newFileUri).handleResult(
            onSuccess = { newUrl ->
                storageRepository.deleteFile(oldPath).handleResult(
                    onSuccess = {
                        val updatedImageData = currentImageData.copy(
                            url = newUrl,
                            upload = true,
                            path = newFilename
                        )
                        imageDataRepository.updateImageData(updatedImageData).handleResult(
                            onSuccess = {
                                currentImageData = updatedImageData
                                _uiState.value = _uiState.value.copy(
                                    imageOperationState = ImageOperationState.Success("Image replaced successfully")
                                )
                                showSuccess("Image replaced successfully")
                            },
                            onError = { exception ->
                                _uiState.value = _uiState.value.copy(
                                    imageOperationState = ImageOperationState.Error(
                                        exception,
                                        "Failed to update image data after replacement"
                                    )
                                )
                            }
                        )
                    },
                    onError = { exception ->
                        _uiState.value = _uiState.value.copy(
                            imageOperationState = ImageOperationState.Error(
                                exception,
                                "Failed to delete old image"
                            )
                        )
                    }
                )
            },
            onError = { exception ->
                _uiState.value = _uiState.value.copy(
                    imageOperationState = ImageOperationState.Error(
                        exception,
                        "Failed to upload new image"
                    )
                )
            }
        )
    }

    fun clearState() {
        _uiState.value = ImageScreenUiState()
    }

    fun dismissError() {
        _uiState.value = _uiState.value.copy(imageOperationState = ImageOperationState.Idle)
    }
}
