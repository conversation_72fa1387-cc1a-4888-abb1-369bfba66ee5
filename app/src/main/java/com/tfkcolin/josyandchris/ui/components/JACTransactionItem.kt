package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.formatToPrice
import java.text.DateFormat

@Composable
fun JACTransactionItem(
    modifier: Modifier = Modifier,
    transaction: FinancialTransaction,
    onClick: () -> Unit
){
    OutlinedCard(
        modifier = modifier,
        shape = RoundedCornerShape(5),
        elevation = CardDefaults.elevatedCardElevation(
            defaultElevation = if(transaction.marked) 10.dp else 5.dp
        ),
        border = BorderStroke(
            width = 1.dp,
            color = if(transaction.marked) MaterialTheme.colorScheme.tertiary
            else MaterialTheme.colorScheme.surface
        )
    ) {
        Box(
            modifier = Modifier
                .padding(5.dp)
                .fillMaxWidth()
                .clickable { onClick() }
        ) {
            Column {
                Text(
                    modifier = Modifier
                        .padding(bottom = 5.dp),
                    text = "Enregistré le: ${DateFormat.getInstance().format(transaction.created)}",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .5f)
                )
                Text(
                    modifier = Modifier
                        .padding(bottom = 5.dp),
                    text = transaction.label,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = "${transaction.price.formatToPrice()} FCFA",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
            Icon(
                modifier = Modifier.align(Alignment.TopEnd),
                painter = painterResource(
                    id = when (transaction.transactionTypeIndex) {
                        TransactionType.INPUT.ordinal -> com.tfkcolin.josyandchris.R.drawable.ic_baseline_call_received_24
                        else -> com.tfkcolin.josyandchris.R.drawable.ic_baseline_call_made_24
                    }
                ),
                contentDescription = null,
                tint = when (transaction.transactionTypeIndex) {
                    TransactionType.INPUT.ordinal -> Color.Green
                    else -> Color.Red
                }
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACTransactionItemPreview(){
    JACTheme {
        Column(modifier = Modifier.padding(5.dp)) {
            JACTransactionItem(
                modifier = Modifier.padding(vertical = 5.dp),
                transaction = FinancialTransaction(
                    id = "0",
                    label = "Achat de marchandise",
                    price = 50000,
                    country = "cameroun",
                    marked = false,
                    transactionTypeIndex = TransactionType.INPUT.ordinal
                ),
                onClick = {}
            )
            JACTransactionItem(
                transaction = FinancialTransaction(
                    id = "0",
                    label = "Achat de marchandise",
                    price = 50000,
                    country = "cameroun",
                    marked = true,
                    transactionTypeIndex = TransactionType.OUTPUT.ordinal
                ),
                onClick = {}
            )
        }
    }
}