package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExtensibleText(
    modifier: Modifier = Modifier,
    text: String
) {
    var expand by remember { mutableStateOf(false) }
    Box(modifier = modifier){
        ElevatedCard(
            modifier = Modifier
                .fillMaxSize()
                .animateContentSize(),
            onClick = { expand = !expand },
            shape = RectangleShape
        ) {
            Text(
                modifier = Modifier.padding(10.dp),
                text = text,
                maxLines = if(expand) Int.MAX_VALUE else 2,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Preview
@Composable
fun ExtensibleTextPreview(){
    JACTheme {
        Surface(color = MaterialTheme.colorScheme.background) {
            ExtensibleText(text = "voici un grand texte qui ne vous apportera rien de plus mais qui me permet de tester le comportement du composant et de voir si il coupe le texte avec trois points")
        }
    }
}