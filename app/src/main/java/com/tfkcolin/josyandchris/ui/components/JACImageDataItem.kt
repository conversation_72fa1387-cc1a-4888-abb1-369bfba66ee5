package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.clickable
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ImageData

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACImageDataItem(
    modifier: Modifier = Modifier,
    imageData: ImageData,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier.clickable { onClick() },
        elevation = CardDefaults.cardElevation(4.dp)
    ) {
        Column(modifier = Modifier.padding(8.dp)) {
            Text(text = "Image: ${imageData.name ?: "Unknown"}")
            Text(text = "Category: ${imageData.category}")
            Text(text = "Genre: ${imageData.genre}")
            if (imageData.upload) {
                Text(text = "Status: Uploaded")
            } else {
                Text(text = "Status: Pending")
            }
        }
    }
}
