package com.tfkcolin.josyandchris.ui.components.foundation


import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun MultipleImagePickerIconButton(
    modifier: Modifier = Modifier,
    onPickImage: (List<Uri>) -> Unit,
    icon: @Composable () -> Unit = {
        Icon(
            painter = painterResource(id = R.drawable.baseline_image_search_24),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primaryContainer
        )
    },
) {
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents(),
        onResult = { uriList -> onPickImage(uriList) }
    )
    IconButton(
        modifier = modifier,
        onClick = { imagePicker.launch("image/*") },
    ) {
        icon()
    }
}

@Preview
@Composable
private fun MultipleImagePickerIconButtonPreview(){
    JACTheme {
        MultipleImagePickerIconButton(
            onPickImage = {}
        )
    }
}