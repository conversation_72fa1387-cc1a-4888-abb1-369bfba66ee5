package com.tfkcolin.josyandchris.ui.components.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACAddCountryDialog(
    modifier: Modifier = Modifier,
    onDismissRequest: () -> Unit,
    countryData: CountryData,
    onCountryDataChanged: (CountryData) -> Unit,
    onAddCountry: () -> Unit
){
    Dialog(onDismissRequest = onDismissRequest) {
        ElevatedCard(
            modifier = modifier
        ) {
            Column(
                modifier = Modifier.padding(5.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier.padding(bottom = 5.dp),
                    text = "Ajouter un nouveau pays",
                    style = MaterialTheme.typography.titleLarge
                )
                Text(
                    modifier = Modifier.padding(bottom = 5.dp),
                    text = "Entrer les informations sur le pays",
                    style = MaterialTheme.typography.bodySmall
                )
                OutlinedTextField(
                    modifier = Modifier.padding(bottom = 5.dp),
                    value = countryData.name,
                    onValueChange = {
                        onCountryDataChanged(
                            countryData.copy(name = it)
                        )
                    },
                    placeholder = {
                        Text(text = "Cameroun")
                    },
                    supportingText = {
                        Text(text = "Nom du pays")
                    },
                    keyboardOptions = KeyboardOptions(capitalization = KeyboardCapitalization.Sentences)
                )
                OutlinedTextField(
                    modifier = Modifier.padding(bottom = 5.dp),
                    value = countryData.devise,
                    onValueChange = {
                        onCountryDataChanged(
                            countryData.copy(devise = it)
                        )
                    },
                    placeholder = {
                        Text(text = "FCFA")
                    },
                    supportingText = {
                        Text(text = "devise utilisée (FCFA, $, ...)")
                    },
                    keyboardOptions = KeyboardOptions(capitalization = KeyboardCapitalization.Sentences)
                )
                Row {
                    TextButton(
                        modifier = Modifier.weight(1f),
                        onClick = onDismissRequest
                    ) {
                        Text(text = "Annuler")
                    }
                    TextButton(
                        modifier = Modifier.weight(1f),
                        onClick = {
                            onAddCountry()
                            onDismissRequest()
                        }
                    ) {
                        Text(text = "Ajouter")
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACAddCountryDialogPreview(){
    JACTheme {
        JACAddCountryDialog(
            onDismissRequest = {},
            onCountryDataChanged = {},
            onAddCountry = {},
            countryData = CountryData(name = "cameroun", devise = "fcfa")
        )
    }
}