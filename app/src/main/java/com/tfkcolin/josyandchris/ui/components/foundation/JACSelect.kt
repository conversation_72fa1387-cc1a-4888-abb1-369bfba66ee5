package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun JACSelect(
    modifier: Modifier = Modifier,
    selected: String,
    expanded: Boolean = false,
    onExpandedChange: (Boolean) -> Unit,
    itemContent: @Composable ((ColumnScope) -> Unit)
){
    Box(modifier = modifier.wrapContentSize(align = Alignment.Center)){
        TextButton(onClick = { onExpandedChange(!expanded) }) {
            Row(modifier = Modifier.padding(5.dp)) {
                Text(
                    modifier = Modifier.padding(end = 5.dp),
                    text = selected
                )
                Icon(
                    imageVector = if(expanded) Icons.Filled.KeyboardArrowUp else Icons.Filled.ArrowDropDown,
                    contentDescription = if(expanded) "collapse" else "expand",
                )
            }
        }
        DropdownMenu(
            modifier = Modifier.heightIn(max = 250.dp),
            expanded = expanded,
            onDismissRequest = { onExpandedChange(false) }
        ) {
            itemContent(this)
        }
    }
}