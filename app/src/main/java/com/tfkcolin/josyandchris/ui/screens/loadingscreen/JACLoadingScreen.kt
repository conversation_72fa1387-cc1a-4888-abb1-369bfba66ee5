package com.tfkcolin.josyandchris.ui.screens.loadingscreen

import DotsFlashing
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.BuildConfig
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACLoadingScreen(
    modifier: Modifier = Modifier
){
    Box(modifier = modifier){
        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxSize(.7f),
        ) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .sizeIn(maxHeight = 150.dp),
                painter = painterResource(id = R.drawable.jocel_xxhdpi),
                contentDescription = "JAC_LOGO"
            )
            DotsFlashing(modifier = Modifier.align(Alignment.Center))
            Text(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .align(Alignment.BottomCenter),
                text = LocalContext.current.getString(R.string.app_name),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        Text(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(5.dp),
            text = "version: ${BuildConfig.VERSION_NAME}",
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.secondaryContainer
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun LoadingScreenPreview(){
    JACTheme {
        JACLoadingScreen(modifier = Modifier.fillMaxSize())
    }
}