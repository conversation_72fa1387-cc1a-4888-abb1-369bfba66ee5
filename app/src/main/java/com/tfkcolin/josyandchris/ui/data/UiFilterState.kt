package com.tfkcolin.josyandchris.ui.data

import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.DateRange
import java.util.Date

/**
 * Sort options for Command queries
 */
enum class CommandSortOrder {
    CREATED_DESC,    // Default: newest first
    CREATED_ASC,     // oldest first
    CLIENT_NAME_ASC, // client name A-Z
    CLIENT_NAME_DESC,// client name Z-A
    STATUS_ASC,      // status ascending
    STATUS_DESC      // status descending
}

/**
 * UI filter state for Commands
 */
data class CommandUiFilterState(
    val selectedStep: CommandStep? = null,
    val searchTerm: String = "",
    val proofUploaded: Boolean? = null,
    val paymentStatus: Boolean? = null, // Added for filter compatibility
    val dateRange: DateRange? = null,
    // Client filter fields
    val clientName: String = "",
    val phone: String = "", // Alias for clientPhone for backward compatibility
    val clientPhone: String = "", // Keep for existing usage
    val country: String = "", // Alias for clientCountry
    val clientCountry: String = "", // Keep for existing usage  
    val city: String = "", // Alias for clientCity
    val clientCity: String = "", // Keep for existing usage
    // Sorting
    val sortOrder: CommandSortOrder = CommandSortOrder.CREATED_DESC,
    // Recent search chips
    val recentClientChips: List<String> = emptyList(),
    val recentLocationChips: List<String> = emptyList()
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        proofUploaded?.let { additionalFilters["proofUploaded"] = it }
        
        // Client filter fields with combined AND logic for multi-criteria
        // Using correct Firestore nested field structure: client.name, client.tel, etc.
        if (clientName.isNotBlank()) additionalFilters["client.name"] = clientName
        
        // Use new field names if provided, otherwise fall back to legacy names
        val phoneValue = phone.ifBlank { clientPhone }
        if (phoneValue.isNotBlank()) additionalFilters["client.tel"] = phoneValue
        
        val countryValue = country.ifBlank { clientCountry }
        if (countryValue.isNotBlank()) additionalFilters["client.country"] = countryValue
        
        val cityValue = city.ifBlank { clientCity }
        if (cityValue.isNotBlank()) additionalFilters["client.city"] = cityValue
        
        // Add sort order for repository processing
        additionalFilters["sortOrder"] = sortOrder
        
        return QueryFilters(
            statusIndex = selectedStep?.ordinal,
            searchTerm = searchTerm.ifBlank { null },
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
    }
}

/**
 * UI filter state for Cargos
 */
data class CargoUiFilterState(
    val selectedStatus: CargoStatus? = null,
    val searchTerm: String = "",
    val originFilter: String = "",
    val destinationFilter: String = "",
    val dateRange: DateRange? = null
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        if (originFilter.isNotBlank()) additionalFilters["origin"] = originFilter
        if (destinationFilter.isNotBlank()) additionalFilters["destination"] = destinationFilter
        
        return QueryFilters(
            statusIndex = selectedStatus?.ordinal,
            searchTerm = searchTerm.ifBlank { null },
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
    }
}

/**
 * UI filter state for Shipments
 */
data class ShipmentUiFilterState(
    val selectedStatus: Int? = null,
    val searchTerm: String = "",
    val cargoId: String = "",
    val clientName: String = "",
    val clientPhone: String = "",
    val dateRange: DateRange? = null
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        if (cargoId.isNotBlank()) additionalFilters["cargoId"] = cargoId
        if (clientName.isNotBlank()) additionalFilters["clientName"] = clientName
        if (clientPhone.isNotBlank()) additionalFilters["clientPhone"] = clientPhone
        
        return QueryFilters(
            statusIndex = selectedStatus,
            searchTerm = searchTerm.ifBlank { null },
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
    }
}

/**
 * UI filter state for Transactions
 */
data class TransactionUiFilterState(
    val selectedTransactionType: TransactionType? = null,
    val searchTerm: String = "",
    val country: String = "",
    val commandId: String = "",
    val marked: Boolean? = null,
    val selectedMonth: Int? = null,
    val selectedYear: Int? = null,
    val dateRange: DateRange? = null
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        if (country.isNotBlank()) additionalFilters["country"] = country
        if (commandId.isNotBlank()) additionalFilters["commandId"] = commandId
        marked?.let { additionalFilters["marked"] = it }
        selectedMonth?.let { additionalFilters["selectedMonth"] = it }
        selectedYear?.let { additionalFilters["selectedYear"] = it }
        
        return QueryFilters(
            statusIndex = selectedTransactionType?.ordinal,
            searchTerm = searchTerm.ifBlank { null },
            dateRange = dateRange,
            additionalFilters = additionalFilters
        )
    }
}

/**
 * UI filter state for Countries
 */
data class CountryUiFilterState(
    val searchTerm: String = "",
    val name: String = "",
    val devise: String = "",
    val searchTokens: List<String> = emptyList()
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        if (name.isNotBlank()) additionalFilters["name"] = name
        if (devise.isNotBlank()) additionalFilters["devise"] = devise
        if (searchTokens.isNotEmpty()) additionalFilters["searchTokens"] = searchTokens
        
        return QueryFilters(
            searchTerm = searchTerm.ifBlank { null },
            additionalFilters = additionalFilters
        )
    }
}

/**
 * UI filter state for ImageData
 */
data class ImageDataUiFilterState(
    val searchTerm: String = "",
    val category: String = "",
    val genre: String = "",
    val uploaded: Boolean? = null
) {
    fun toQueryFilters(): QueryFilters {
        val additionalFilters = mutableMapOf<String, Any>()
        if (category.isNotBlank()) additionalFilters["category"] = category
        if (genre.isNotBlank()) additionalFilters["genre"] = genre
        uploaded?.let { additionalFilters["upload"] = it }
        
        return QueryFilters(
            searchTerm = searchTerm.ifBlank { null },
            additionalFilters = additionalFilters
        )
    }
}
