package com.tfkcolin.josyandchris.ui.screens.commanddetails

import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.*
import androidx.navigation.compose.composable
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import timber.log.Timber
import java.util.*

private const val commandIdArgs = "commandId"
private const val isCreateArgs = "isCreate"

internal class CommandArgs(val id: String, val isCreate: Boolean) {
    constructor(savedStateHandle: SavedStateHandle) :
            this(
                savedStateHandle.get<String>(commandIdArgs) ?: "",
                savedStateHandle.get<Boolean>(isCreateArgs) ?: true
            ) {
        Timber.d("CommandArgs created with id: $id, isCreate: $isCreate")
    }
}

// if we have multiple params to pass we use this syntax
// "${Screen.AccountingScreen.route}?$commandIdArgs={$commandIdArgs}&$stepIndexArgs={$stepIndexArgs}"

fun NavGraphBuilder.commandDetailsScreen(
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onNavigateToAddImageDataScreen: () -> Unit,
    onTitleDescriptionChange: (String) -> Unit
){
    composable(
        route = "${Screen.CommandDetailsScreen.route}?$commandIdArgs={$commandIdArgs}&$isCreateArgs={$isCreateArgs}",
        arguments = listOf(
            navArgument(commandIdArgs) {
                nullable = true
                defaultValue = null
                type = NavType.StringType
            },
            navArgument(isCreateArgs) {
                defaultValue = true
                type = NavType.BoolType
            }
        )
    ){
        val viewModel = hiltViewModel<CommandDetailsScreenViewModel>()
        
        // Load command from repository if not creating
        var command by remember { mutableStateOf(Command()) }
        
        LaunchedEffect(viewModel.commandId) {
            if (!viewModel.isCreate && viewModel.commandId.isNotEmpty()) {
                viewModel.loadCommand { loadedCommand ->
                    command = loadedCommand
                }
            }
        }

        DisposableEffect(command){
            onTitleDescriptionChange(command.id)
            onDispose {
                onTitleDescriptionChange("")
            }
        }
        LaunchedEffect(Unit){
            viewModel.loadPreviews(command)
        }
        
        // Observe images data from ViewModel
        val imagesData by viewModel.imagesData.collectAsState()
        
        // put the dialog here or put it in App since it is used in multiple screen
        CommandDetailsScreen(
            changeMade = viewModel.changeMade.value,
            command = command,
            previews = viewModel.previews,
            setHideAppbar = setHideAppbar,
            isCreate = viewModel.isCreate,
            onCommandChanged = {
                command = it
                viewModel.setChangeMade(true)
            },
            role = role,
            onDeleteImage = { path ->
                viewModel.deleteFile(path)
                viewModel.loadPreviews(command)
            },
            onCommandSaved = {
                viewModel.updateCommand(command)
                if(viewModel.isCreate)
                    command = Command()
                viewModel.setChangeMade(false)
            },
            onUploadProof = { uri ->
                viewModel.savePaymentProof(uri.toUri()) { downloadUrl ->
                    command = command.copy(
                        paymentProofImageUrl = downloadUrl.toString(),
                        proofUploaded = true
                    )
                    viewModel.updateCommand(command)
                }
            },
            onUploadPreview = { url, filename, productIndex ->
                // Check if preview folder exists for this product
                if(command.products[productIndex].previewsPathName == null){
                    command = command.copy(
                        products = command.products.mapIndexed { index, product ->
                            if(index == productIndex) product.copy(
                                previewsPathName = UUID.randomUUID().toString())
                            else product
                        }
                    )
                    viewModel.updateCommand(command)
                }
                // Upload preview
                viewModel.savePreviewImage(
                    fileUri = url.toUri(),
                    commandPreviewNodeName = command.products[productIndex].previewsPathName!!,
                    uploadedFileName = filename
                ) { _ ->
                    // Update preview list
                    viewModel.loadPreviews(command)
                }
            },
            imageData = imagesData.filter { it.upload },
            onAddImageData = {
                onNavigateToAddImageDataScreen()
            },
            onShowSnackbar = onShowSnackbar
        )
    }
}

fun NavController.navigateToCommandDetailsScreen(id: String = "", isCreate: Boolean = false, navOptions: NavOptions? = null)
{
    Timber.d("navigateToCommandDetailsScreen with id: $id, isCreate: $isCreate")
    this.navigate(
        "${Screen.CommandDetailsScreen.route}?$commandIdArgs=$id&$isCreateArgs=$isCreate",
        navOptions
    )
}
