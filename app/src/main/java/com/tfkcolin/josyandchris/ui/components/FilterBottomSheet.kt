package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.components.foundation.JACCheckBox
import com.tfkcolin.josyandchris.ui.components.foundation.JACTextField
import com.tfkcolin.josyandchris.ui.components.foundation.SimpleDateRangePicker
import com.tfkcolin.josyandchris.ui.components.foundation.DateRangePresets
import com.tfkcolin.josyandchris.ui.data.CommandUiFilterState
import com.tfkcolin.josyandchris.util.query.DateRange
import java.text.SimpleDateFormat
import java.util.*

/**
 * Advanced filter bottom sheet for Commands with comprehensive filtering options
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterBottomSheet(
    modifier: Modifier = Modifier,
    filterState: CommandUiFilterState = CommandUiFilterState(),
    onFilterStateChange: (CommandUiFilterState) -> Unit = {},
    onApply: (CommandUiFilterState) -> Unit = {},
    onClear: () -> Unit = {},
    onDismiss: () -> Unit = {},
    presetFilters: Map<String, CommandUiFilterState> = mapOf(
        "Recent" to CommandUiFilterState(dateRange = recentDateRange()),
        "Pending Payment" to CommandUiFilterState(paymentStatus = false),
        "Completed" to CommandUiFilterState(selectedStep = CommandStep.OK)
    )
) {
var localFilterState by remember(filterState) { mutableStateOf(filterState) }
    
    // Update local state when filterState changes
    LaunchedEffect(filterState) {
        localFilterState = filterState
    }
    
    ModalBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismiss,
        dragHandle = { BottomSheetDefaults.DragHandle() }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // Header
            Text(
                text = "Advanced Filters",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Preset Filters Section
            PresetFiltersSection(
                presets = presetFilters,
                onPresetSelected = { preset ->
                    localFilterState = preset
                    // Don't immediately apply - wait for user to click Apply
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Client Filter Section
            ClientFilterSection(
                filterState = localFilterState,
                onFilterChange = { newState ->
                    localFilterState = newState
                    // Don't immediately apply - wait for user to click Apply
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Date Range Picker Section
            DateRangePickerSection(
                dateRange = localFilterState.dateRange,
                onDateRangeChange = { dateRange ->
                    localFilterState = localFilterState.copy(dateRange = dateRange)
                    // Don't immediately apply - wait for user to click Apply
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Payment Status Toggle Section
            PaymentStatusToggleSection(
                paymentStatus = localFilterState.paymentStatus,
                onPaymentStatusChange = { status ->
                    localFilterState = localFilterState.copy(paymentStatus = status)
                    // Don't immediately apply - wait for user to click Apply
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Command Step Flow Row Section
            CommandStepFlowRowSection(
                selectedStep = localFilterState.selectedStep,
                onStepSelected = { step ->
                    localFilterState = localFilterState.copy(selectedStep = step)
                    // Don't immediately apply - wait for user to click Apply
                }
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Action Buttons
            ActionButtonsSection(
                onClear = {
                    val clearedState = CommandUiFilterState()
                    localFilterState = clearedState
                    // Don't immediately apply - user still needs to click Apply after clearing
                    onClear()
                },
                onApply = {
                    onApply(localFilterState)
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
private fun PresetFiltersSection(
    presets: Map<String, CommandUiFilterState>,
    onPresetSelected: (CommandUiFilterState) -> Unit
) {
    Column {
        Text(
            text = "Quick Filters",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(presets.toList()) { (name, preset) ->
                FilterChip(
                    onClick = { onPresetSelected(preset) },
                    label = { Text(name) },
                    selected = false,
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Filled.FilterList,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun ClientFilterSection(
    filterState: CommandUiFilterState,
    onFilterChange: (CommandUiFilterState) -> Unit
) {
    Column {
        Text(
            text = "Client Filter",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Client Name with AutoComplete
        AutoCompleteTextField(
            label = "Client Name",
            value = filterState.clientName,
            onValueChange = { name ->
                onFilterChange(filterState.copy(clientName = name))
            },
            suggestions = filterState.recentClientChips,
            leadingIcon = {
                Icon(Icons.Filled.Person, contentDescription = null)
            }
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Client Phone with AutoComplete
        AutoCompleteTextField(
            label = "Phone Number",
            value = filterState.clientPhone,
            onValueChange = { phone ->
                onFilterChange(filterState.copy(clientPhone = phone))
            },
            suggestions = filterState.recentClientChips.filter { it.matches(Regex(".*\\d.*")) },
            leadingIcon = {
                Icon(Icons.Filled.Phone, contentDescription = null)
            }
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Client Country with AutoComplete
            AutoCompleteTextField(
                modifier = Modifier.weight(1f),
                label = "Country",
                value = filterState.clientCountry,
                onValueChange = { country ->
                    onFilterChange(filterState.copy(clientCountry = country))
                },
                suggestions = filterState.recentLocationChips,
                leadingIcon = {
                    Icon(Icons.Filled.Public, contentDescription = null)
                }
            )
            
            // Client City with AutoComplete
            AutoCompleteTextField(
                modifier = Modifier.weight(1f),
                label = "City",
                value = filterState.clientCity,
                onValueChange = { city ->
                    onFilterChange(filterState.copy(clientCity = city))
                },
                suggestions = filterState.recentLocationChips,
                leadingIcon = {
                    Icon(Icons.Filled.LocationCity, contentDescription = null)
                }
            )
        }
        
        // Recent Chips Display
        if (filterState.recentClientChips.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Recent",
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                modifier = Modifier.padding(top = 4.dp)
            ) {
                items(filterState.recentClientChips.take(5)) { chip ->
                    SuggestionChip(
                        onClick = {
                            // Apply the chip to appropriate field based on content
                            when {
                                chip.matches(Regex(".*\\d.*")) -> 
                                    onFilterChange(filterState.copy(clientPhone = chip))
                                else -> 
                                    onFilterChange(filterState.copy(clientName = chip))
                            }
                        },
                        label = { Text(chip, style = MaterialTheme.typography.labelSmall) }
                    )
                }
            }
        }
    }
}

@Composable
private fun AutoCompleteTextField(
    modifier: Modifier = Modifier,
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    suggestions: List<String> = emptyList(),
    leadingIcon: @Composable (() -> Unit)? = null
) {
    var expanded by remember { mutableStateOf(false) }
    val filteredSuggestions = suggestions.filter { 
        it.contains(value, ignoreCase = true) && it != value 
    }.take(3)
    
    Column(modifier = modifier) {
        JACTextField(
            modifier = Modifier.fillMaxWidth(),
            value = value,
            onValueChange = { 
                onValueChange(it)
                expanded = it.isNotEmpty() && filteredSuggestions.isNotEmpty()
            },
            label = { Text(label) },
            leadingIcon = leadingIcon,
            trailingIcon = if (filteredSuggestions.isNotEmpty()) {
                {
                    IconButton(onClick = { expanded = !expanded }) {
                        Icon(
                            imageVector = if (expanded) Icons.Filled.ExpandLess else Icons.Filled.ExpandMore,
                            contentDescription = null
                        )
                    }
                }
            } else null
        )
        
        if (expanded && filteredSuggestions.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 2.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column {
                    filteredSuggestions.forEach { suggestion ->
                        Text(
                            text = suggestion,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    onValueChange(suggestion)
                                    expanded = false
                                }
                                .padding(12.dp),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        if (suggestion != filteredSuggestions.last()) {
                            HorizontalDivider()
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DateRangePickerSection(
    dateRange: DateRange?,
    onDateRangeChange: (DateRange?) -> Unit
) {
    Column {
        Text(
            text = "Date Range",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        SimpleDateRangePicker(
            dateRange = dateRange,
            onDateRangeSelected = onDateRangeChange,
            label = "Select Date Range"
        )
    }
}

@Composable
private fun PaymentStatusToggleSection(
    paymentStatus: Boolean?,
    onPaymentStatusChange: (Boolean?) -> Unit
) {
    Column {
        Text(
            text = "Payment Status",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // All Payments
            FilterChip(
                modifier = Modifier.weight(1f),
                onClick = { onPaymentStatusChange(null) },
                label = { 
                    Text("All", textAlign = TextAlign.Center, modifier = Modifier.fillMaxWidth()) 
                },
                selected = paymentStatus == null,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Filled.List,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                }
            )
            
            // Paid
            FilterChip(
                modifier = Modifier.weight(1f),
                onClick = { onPaymentStatusChange(true) },
                label = { 
                    Text("Paid", textAlign = TextAlign.Center, modifier = Modifier.fillMaxWidth()) 
                },
                selected = paymentStatus == true,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Filled.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp),
                        tint = if (paymentStatus == true) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
                    )
                }
            )
            
            // Unpaid
            FilterChip(
                modifier = Modifier.weight(1f),
                onClick = { onPaymentStatusChange(false) },
                label = { 
                    Text("Unpaid", textAlign = TextAlign.Center, modifier = Modifier.fillMaxWidth()) 
                },
                selected = paymentStatus == false,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Filled.Cancel,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp),
                        tint = if (paymentStatus == false) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface
                    )
                }
            )
        }
    }
}

@Composable
private fun CommandStepFlowRowSection(
    selectedStep: CommandStep?,
    onStepSelected: (CommandStep?) -> Unit
) {
    Column {
        Text(
            text = "Command Status",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Clear selection chip
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { onStepSelected(null) },
                label = { Text("All Steps") },
                selected = selectedStep == null,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Filled.ClearAll,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                }
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Command steps flow
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(CommandStep.entries) { step ->
                FilterChip(
                    onClick = { 
                        onStepSelected(if (selectedStep == step) null else step)
                    },
                    label = { 
                        Text(
                            text = step.step,
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    selected = selectedStep == step,
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = step.iconResId),
                            contentDescription = step.label,
                            modifier = Modifier.size(16.dp),
                            tint = if (selectedStep == step) 
                                MaterialTheme.colorScheme.onPrimaryContainer
                            else 
                                MaterialTheme.colorScheme.onSurface
                        )
                    }
                )
            }
        }
        
        // Selected step details
        if (selectedStep != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = painterResource(id = selectedStep.iconResId),
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = selectedStep.label,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

@Composable
private fun ActionButtonsSection(
    onClear: () -> Unit,
    onApply: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Clear Button
        OutlinedButton(
            onClick = onClear,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = Icons.Filled.ClearAll,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("Clear")
        }
        
        // Apply Button
        Button(
            onClick = onApply,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = Icons.Filled.Check,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("Apply Filters")
        }
    }
}

/**
 * Helper function to create a recent date range (last 30 days)
 */
private fun recentDateRange(): DateRange {
    val endDate = Date()
    val calendar = Calendar.getInstance()
    calendar.time = endDate
    calendar.add(Calendar.DAY_OF_MONTH, -30)
    val startDate = calendar.time
    return DateRange(startDate, endDate)
}
