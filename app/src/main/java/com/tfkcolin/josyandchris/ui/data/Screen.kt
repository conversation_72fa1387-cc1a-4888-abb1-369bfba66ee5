package com.tfkcolin.josyandchris.ui.data

enum class Screen(
    val route: String,
    val label: String
){
    LoadingScreen(route = "loading", label = "Chargement des données"),
    LoginScreen(route = "login", label = "Se connecter"),
    CountrySelectionScreen(route = "home", label = "Vue globale"),
    AccountingScreen(route = "accounting", label = "Caisse"),
    CommandScreen(route = "command_list", label = "Commandes"),
    CommandDetailsScreen(route = "command_details", label = "Détails de la commande"),
    ProductScreen(route = "products", label = "Catalogue des produits"),
    AddProductScreen(route = "new_products", label = "Nouveau produits"),
    EditImageDataScreen(route = "edit_image_data", label = "Modifier les données de l'image"),
    CargoScreen(route = "cargo_list", label = "Cargos"),
    CargoDetailsScreen(route = "cargo_details", label = "Détails du cargo"),
    ShipmentDetailsScreen(route = "shipment_details", label = "Détails de l'expédition");
}

