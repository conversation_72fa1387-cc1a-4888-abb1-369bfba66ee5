package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CountryData

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCountryItem(
    modifier: Modifier = Modifier,
    country: CountryData
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(4.dp)
    ) {
        Column(modifier = Modifier.padding(8.dp)) {
            Text(text = "Country: ${country.name}")
            Text(text = "Devise: ${country.devise}")
        }
    }
}
