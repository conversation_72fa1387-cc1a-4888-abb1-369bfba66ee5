package com.tfkcolin.josyandchris.ui.screens.login

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.IntentSenderRequest
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.compose.composable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.R
import com.tfkcolin.josyandchris.ui.data.Screen
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.coroutines.delay

@Composable
fun LoginScreen(
    viewModel: LogInViewModel = hiltViewModel(),
    onNavigateToHome: () -> Unit,
    onNavigateToRegistration: () -> Unit
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    val loginEvent by viewModel.loginEvents.collectAsState()
    val haptic = LocalHapticFeedback.current

    // Animation states
    val logoScale = remember { Animatable(0.8f) }
    val contentAlpha = remember { Animatable(0f) }

    // Initial animations
    LaunchedEffect(Unit) {
        logoScale.animateTo(
            targetValue = 1f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            )
        )
        contentAlpha.animateTo(
            targetValue = 1f,
            animationSpec = tween(500, delayMillis = 300)
        )
    }

    var emailFocused by remember { mutableStateOf(false) }
    val emailScale by animateFloatAsState(
        targetValue = if (emailFocused) 1.02f else 1f,
        label = "emailScale"
    )

    // Handle login events
    LaunchedEffect(loginEvent) {
        when (loginEvent) {
            is LoginEvent.NavigateToHome -> {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onNavigateToHome()
                viewModel.clearEvent()
            }

            is LoginEvent.NavigateToRegistration -> {
                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                onNavigateToRegistration()
                viewModel.clearEvent()
            }

            is LoginEvent.PasswordResetEmailSent -> {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                // Show a snackbar or dialog here
                viewModel.clearEvent()
            }

            else -> { /* No action needed */
            }
        }
    }

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { res ->
        viewModel.onOneTapResult(res.data)
    }

    // Password reset dialog state
    var showPasswordResetDialog by remember { mutableStateOf(false) }
    var focusedField by remember { mutableStateOf<String?>(null) }
    var triggerOneTapSignIn by remember { mutableStateOf(false) }

    if (showPasswordResetDialog) {
        AlertDialog(
            onDismissRequest = { showPasswordResetDialog = false },
            title = { Text("Reset Password") },
            text = {
                Text("We'll send a password reset link to ${uiState.email}")
            },
            confirmButton = {
                Button(
                    onClick = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        viewModel.sendPasswordResetEmail()
                        showPasswordResetDialog = false
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("Send Link")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showPasswordResetDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }

    // Error snackbar
    val snackbarHostState = remember { SnackbarHostState() }

    LaunchedEffect(uiState.errorMessage) {
        uiState.errorMessage?.let {
            snackbarHostState.showSnackbar(
                message = it,
                duration = SnackbarDuration.Short
            )
        }
    }

    // Handle One Tap sign-in trigger
    LaunchedEffect(triggerOneTapSignIn) {
        if (triggerOneTapSignIn) {
            val intentSender = viewModel.launchOneTapSignIn()
            intentSender?.let {
                launcher.launch(IntentSenderRequest.Builder(it).build())
            }
            triggerOneTapSignIn = false
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Animated gradient background
        val colors = listOf(
            MaterialTheme.colorScheme.surface,
            MaterialTheme.colorScheme.surfaceVariant,
            MaterialTheme.colorScheme.surface
        )

        val transition = rememberInfiniteTransition(label = "backgroundTransition")
        val translateAnim = transition.animateFloat(
            initialValue = 0f,
            targetValue = 1000f,
            animationSpec = infiniteRepeatable(
                animation = tween(10000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "backgroundAnimation"
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        colors = colors,
                        start = Offset(translateAnim.value, 0f),
                        end = Offset(translateAnim.value + 100f, 2000f)
                    )
                )
        )

        // Make the content scrollable to handle smaller screens
        BoxWithConstraints(
            modifier = Modifier.fillMaxSize()
        ) {
            val screenHeight = this.maxHeight
            val contentPadding = if (screenHeight < 600.dp) 8.dp else 16.dp
            val logoSize = if (screenHeight < 600.dp) 100.dp else 150.dp
            val spacerHeight = if (screenHeight < 600.dp) 8.dp else 16.dp

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(contentPadding)
                    .alpha(contentAlpha.value),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Logo with animation
                Box(
                    modifier = Modifier
                        .padding(top = contentPadding * 2)
                        .scale(logoScale.value),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.size(logoSize),
                        painter = painterResource(id = R.drawable.jocel_xxhdpi),
                        contentDescription = "JAC_LOGO"
                    )
                }

                Spacer(modifier = Modifier.height(spacerHeight))

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = contentPadding, vertical = contentPadding / 2)
                        .alpha(contentAlpha.value),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(contentPadding),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(spacerHeight)
                    ) {
                        // Title
                        Text(
                            text = "Welcome Back",
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = "Sign in to continue",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        Spacer(modifier = Modifier.height(spacerHeight))

                        // Email field with animation
                        val emailFieldElevation by animateFloatAsState(
                            targetValue = if (focusedField == "email") 4f else 1f,
                            label = "emailElevation"
                        )

                        OutlinedTextField(
                            value = uiState.email,
                            onValueChange = { viewModel.updateEmail(it) },
                            label = { Text("Email") },
                            leadingIcon = {
                                Icon(
                                    Icons.Default.Email,
                                    contentDescription = null,
                                    tint = if (emailFocused) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            },
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                            isError = !uiState.isEmailValid,
                            supportingText = {
                                if (!uiState.isEmailValid) {
                                    Text("Please enter a valid email address")
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .scale(emailScale)
                                .onFocusChanged {
                                    emailFocused = it.isFocused
                                    if (it.isFocused) {
                                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    }
                                },
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = MaterialTheme.colorScheme.primary,
                                unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                                focusedLabelColor = MaterialTheme.colorScheme.primary,
                                cursorColor = MaterialTheme.colorScheme.primary
                            )
                        )

                        // Password field with animation
                        val passwordFieldElevation by animateFloatAsState(
                            targetValue = if (focusedField == "password") 4f else 1f,
                            label = "passwordElevation"
                        )

                        OutlinedTextField(
                            value = uiState.password,
                            onValueChange = { viewModel.updatePassword(it) },
                            label = { Text("Password") },
                            leadingIcon = {
                                Icon(Icons.Default.Lock, contentDescription = null)
                            },
                            trailingIcon = {
                                IconButton(onClick = {
                                    haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    viewModel.togglePasswordVisibility()
                                }) {
                                    Icon(
                                        if (uiState.passwordVisible) painterResource(R.drawable.baseline_visibility_off_24)
                                        else painterResource(R.drawable.baseline_visibility_24),
                                        contentDescription = if (uiState.passwordVisible) "Hide password" else "Show password"
                                    )
                                }
                            },
                            singleLine = true,
                            visualTransformation = if (uiState.passwordVisible) VisualTransformation.None
                            else PasswordVisualTransformation(),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                            isError = !uiState.isPasswordValid,
                            supportingText = {
                                if (!uiState.isPasswordValid) {
                                    Text("Password must be at least 6 characters")
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .onFocusChanged {
                                    focusedField = if (it.isFocused) "password" else null
                                    if (it.isFocused) {
                                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    }
                                },
                            shape = RoundedCornerShape(8.dp)
                        )

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = uiState.rememberMe,
                                onCheckedChange = {
                                    haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    viewModel.toggleRememberMe()
                                }
                            )
                            Text("Remember me")

                            Spacer(modifier = Modifier.weight(1f))

                            // Only show biometric option if available
                            if (uiState.isBiometricAvailable) {
                                IconButton(
                                    onClick = {
                                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                        viewModel.authenticateWithBiometrics()
                                    }
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.baseline_fingerprint_24),
                                        contentDescription = "Use biometric authentication",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        // Login button with animation
                        val buttonScale = remember { Animatable(1f) }

                        Button(
                            onClick = {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                viewModel.validateAndSignIn()
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp)
                                .scale(buttonScale.value),
                            enabled = !uiState.isLoading,
                            shape = RoundedCornerShape(8.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary,
                                contentColor = MaterialTheme.colorScheme.onPrimary
                            ),
                            elevation = ButtonDefaults.buttonElevation(
                                defaultElevation = 4.dp,
                                pressedElevation = 8.dp
                            )
                        ) {
                            Text(
                                "Login",
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Bold
                                )
                            )
                        }

                        HorizontalDivider(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            color = MaterialTheme.colorScheme.outlineVariant
                        )

                        Text(
                            text = "Or continue with",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // Google sign-in button
                        OutlinedButton(
                            onClick = {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                triggerOneTapSignIn = true
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp),
                            enabled = !uiState.isLoading,
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_google),
                                contentDescription = "Google Sign-In",
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Sign in with Google", style = MaterialTheme.typography.bodyLarge)
                        }
                    }
                }

                // Password reset option
                TextButton(
                    onClick = {
                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        showPasswordResetDialog = true
                    }
                ) {
                    Text("Forgot Password?")
                }

                // Registration row with animation
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Don't have an account?")

                    val registerScale = remember { Animatable(1f) }

                    LaunchedEffect(Unit) {
                        while (true) {
                            // Subtle pulse animation
                            registerScale.animateTo(
                                targetValue = 1.05f,
                                animationSpec = tween(700, easing = FastOutSlowInEasing)
                            )
                            registerScale.animateTo(
                                targetValue = 1f,
                                animationSpec = tween(700, easing = FastOutSlowInEasing)
                            )
                            delay(2000)
                        }
                    }

                    TextButton(
                        onClick = {
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                            viewModel.navigateToRegistration()
                        },
                        modifier = Modifier.scale(registerScale.value)
                    ) {
                        Text(
                            "Register",
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                // Terms and Privacy Policy - moved up from bottom
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    TextButton(onClick = { /* Navigate to Terms */ }) {
                        Text("Terms of Service")
                    }
                    TextButton(onClick = { /* Navigate to Privacy Policy */ }) {
                        Text("Privacy Policy")
                    }
                }
            }
        }

        // Snackbar host
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(16.dp)
        ) { data ->
            Snackbar(
                shape = RoundedCornerShape(8.dp),
                containerColor = MaterialTheme.colorScheme.errorContainer,
                contentColor = MaterialTheme.colorScheme.onErrorContainer,
                action = {
                    TextButton(
                        onClick = { snackbarHostState.currentSnackbarData?.dismiss() }
                    ) {
                        Text("Dismiss", color = MaterialTheme.colorScheme.primary)
                    }
                }
            ) {
                Text(data.visuals.message)
            }
        }

        // Loading overlay - improved positioning and animation
        if (uiState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)),
                contentAlignment = Alignment.Center
            ) {
                Card(
                    modifier = Modifier
                        .width(200.dp)
                        .wrapContentHeight(),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp),
                            strokeWidth = 4.dp,
                            color = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Signing in...",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}

// Update the composable function in the NavGraphBuilder
fun NavGraphBuilder.loginNavigation(
    onNavigateToHome: () -> Unit,
    onNavigateToRegistration: () -> Unit
) {
    composable(
        route = Screen.LoginScreen.route,
        enterTransition = {
            slideIntoContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Up,
                animationSpec = tween(500, easing = EaseOutQuint)
            ) + fadeIn(
                animationSpec = tween(500, easing = EaseOutQuint)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Down,
                animationSpec = tween(500, easing = EaseInQuint)
            ) + fadeOut(
                animationSpec = tween(300, easing = EaseInQuint)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Up,
                animationSpec = tween(500, easing = EaseOutQuint)
            ) + fadeIn(
                animationSpec = tween(500, easing = EaseOutQuint)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Down,
                animationSpec = tween(500, easing = EaseInQuint)
            ) + fadeOut(
                animationSpec = tween(300, easing = EaseInQuint)
            )
        }
    ) {
        LoginScreen(
            onNavigateToHome = onNavigateToHome,
            onNavigateToRegistration = onNavigateToRegistration
        )
    }
}

fun NavController.navigateToLoginScreen(
    navOptions: NavOptionsBuilder.() -> Unit = {
        this.popUpTo(Screen.LoginScreen.route) { inclusive = true }
    }
) {
    this.navigate(Screen.LoginScreen.route, navOptions)
}

@Preview(showBackground = true)
@Composable
fun LoginScreenPreview() {
    MaterialTheme {
        LoginScreen(
            onNavigateToHome = { },
            onNavigateToRegistration = { }
        )
    }
}
