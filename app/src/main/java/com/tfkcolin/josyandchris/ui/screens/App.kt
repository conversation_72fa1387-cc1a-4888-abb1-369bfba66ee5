package com.tfkcolin.josyandchris.ui.screens

import android.app.Activity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.paging.compose.collectAsLazyPagingItems
import com.google.accompanist.insets.ProvideWindowInsets
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.google.android.gms.tasks.Tasks
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.domain.exception.DomainException
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.ui.components.JACFloatingActionButton
import com.tfkcolin.josyandchris.ui.components.JACSnackbarHost
import com.tfkcolin.josyandchris.ui.components.JACTopAppBar
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationDialog
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationState
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import com.tfkcolin.josyandchris.ui.screens.cargodetails.navigateToCargoDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.commanddetails.navigateToCommandDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.homeselection.navigateToCountrySelectionScreen
import com.tfkcolin.josyandchris.ui.screens.login.navigateToLoginScreen
import com.tfkcolin.josyandchris.util.getPreferences
import kotlinx.coroutines.launch

@Composable
fun App(){
    // A surface container using the 'background' color from the theme
    /*val systemUi = rememberSystemUiController()
    SideEffect {
        systemUi.setStatusBarColor(color = Color.Black)
    }*/
    ProvideWindowInsets(
        windowInsetsAnimationsEnabled = true
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            Content()
        }
    }
}

@Composable
fun Content(
    modifier: Modifier = Modifier
) {
    val navController = rememberNavController()
    val snackbarState = remember { SnackbarHostState() }

    val viewModel: ApplicationViewModel = hiltViewModel()

    // Countries and transactions are loaded on demand by their respective screens
    var countries by remember { mutableStateOf(listOf<CountryData>()) }
    var transactions by remember { mutableStateOf(listOf<FinancialTransaction>()) }
    
    var role by viewModel.role
    val user by viewModel.currentUser
    
    // Load countries when user is authenticated
    LaunchedEffect(user) {
        if (user != null) {
            countries = viewModel.loadCountries()
            // Transactions are now loaded in AccountingScreen with pagination
        }
    }
    
    // Commands, cargos, and shipments are now loaded directly in their respective list screens
    // using pagination for better performance and reduced Firebase read costs

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination by remember { derivedStateOf { navBackStackEntry?.destination } }
    val scope = rememberCoroutineScope()
    var hideAppbar by rememberSaveable { mutableStateOf(false) }

    var titleDescription by remember { mutableStateOf("") }

    val systemUi = rememberSystemUiController()

    LaunchedEffect(hideAppbar){
        systemUi.isStatusBarVisible = !hideAppbar
        systemUi.isSystemBarsVisible = !hideAppbar
    }

    val onShowSnackbar: (SnackbarState) -> Unit = remember {
        { state ->
            viewModel.showSnackbar(state)
            scope.launch {
                snackbarState.showSnackbar(state.message)
            }
        }
    }

    val showError: (String?) -> Unit = remember {
        { err ->
            onShowSnackbar(
                SnackbarState(
                    message = "Error: $err",
                    type = SnackbarType.ERROR
                )
            )
        }
    }

    val showSuccess: (String) -> Unit = remember {
        { msg -> onShowSnackbar(SnackbarState(message = msg, type = SnackbarType.SUCCESS)) }
    }

    val trySuspendFunction: suspend (
        succeedMessage: String?,
        suspend () -> Unit
    ) -> Unit = remember {
        { msg, function ->
            try {
                function()
                msg?.let{
                    showSuccess(msg)
                }
            } catch (e: DomainException){
                showError(e.message)
            }
        }
    }

    LaunchedEffect(user){
        if(user == null){
            navController.popBackStack()
            navController.navigateToLoginScreen()
        } else {
            // User is authenticated, navigate to home screen
            navController.popBackStack()
            navController.navigateToCountrySelectionScreen()
        }
    }

    Scaffold(
        modifier = modifier,
        snackbarHost = {
            JACSnackbarHost(
                hostState = snackbarState,
                variant = viewModel.snackbarState.value?.type ?: SnackbarType.INFO
            )
        },
        containerColor = MaterialTheme.colorScheme.background,
        floatingActionButton = {
            currentDestination?.let { destination ->
                if (destination.route?.startsWith(Screen.CountrySelectionScreen.route) == true
                    && (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role)){
                    JACFloatingActionButton(
                        text = "Ajouter une commande",
                        onClick = {
                            navController.navigateToCommandDetailsScreen()
                        }
                    )
                }
                if (destination.route?.startsWith(Screen.AccountingScreen.route) == true
                    && role == UserRole.ADMIN.role){
                    JACFloatingActionButton(
                        text = "Ajouter une transaction",
                        onClick = {
                            viewModel.setShowAddTransactionDialog(true)
                        }
                    )
                }
                if (destination.route?.startsWith(Screen.CargoScreen.route) == true
                    && (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role)){
                    JACFloatingActionButton(
                        text = "Ajouter un cargo",
                        onClick = {
                            navController.navigateToCargoDetailsScreen()
                        }
                    )
                }
            }
        },
        floatingActionButtonPosition = FabPosition.Center,
        topBar = {
            currentDestination?.let{ destination ->
                val screen by remember(destination) {
                    derivedStateOf {
                        Screen.entries.find {
                            destination.route?.startsWith(it.route) == true
                        }
                    }
                }
                AnimatedVisibility(
                    visible = screen?.route !in listOf(Screen.LoadingScreen.route, Screen.CommandScreen.route) && !hideAppbar,
                    enter = slideInVertically { -it },
                    exit = slideOutVertically { -it }
                ){
                    JACTopAppBar(
                        label = "${screen?.label ?: "Josy and Chris"} ${if(titleDescription.isNotEmpty()) "- $titleDescription" else titleDescription}",
                        onIconClick = {
                            navController.navigateUp()
                        },
                        showNavIcon = !(destination.route
                            ?.startsWith(Screen.CountrySelectionScreen.route) == true
                                || destination.route
                            ?.startsWith(Screen.LoginScreen.route) == true),
                        isLogIn = viewModel.isLogIn(),
                        onLogOutClick = viewModel::logOut
                    )
                }
            }
        }
    ) {
        NavigationGraph(
            modifier = Modifier
                .padding(it)
                .padding(start = 5.dp, end = 5.dp),
            navController = navController,
            setHideAppbar = { status -> hideAppbar = status },
            countries = countries,
            role = role,
            authManager = viewModel.authManager,
            onShowConfirmationDialog = viewModel::showConfirmationDialog,
            showAddTransactionDialog = viewModel.showAddTransactionDialog.value,
            onShowAddTransactionDialogChanged = { viewModel.setShowAddTransactionDialog(false) },
            onShowSnackbar = onShowSnackbar,
            onLoginResult = { throwable ->
                if(throwable?.message == null) {
                    navController.navigateToCountrySelectionScreen()
                } else {
                    scope.launch {
                        snackbarState.showSnackbar(throwable.message ?: "Une erreur c'est produite lors de l'authentification de votre compte")
                    }
                }
            },
            startDestination = Screen.LoadingScreen.route,
            onTitleDescriptionChange = { desc ->
                titleDescription = desc
            },
            trySuspendFunction = trySuspendFunction
        )
        Crossfade(
            targetState = viewModel.confirmationState.value != null, label = "confirm dialog enter"
        ) { show ->
            if(show){
                ConfirmationDialog(
                    state = viewModel.confirmationState.value?: ConfirmationState({}, "", ""),
                    onDismissRequest = viewModel::hideConfirmationDialog
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    JACTheme {
        App()
    }
}