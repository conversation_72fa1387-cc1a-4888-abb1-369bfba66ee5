package com.tfkcolin.josyandchris.ui.components

import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.tfkcolin.josyandchris.ui.data.SnackbarType

@Composable
fun JACSnackbarHost(
    modifier: Modifier = Modifier,
    hostState: SnackbarHostState,
    variant: SnackbarType
){
    SnackbarHost(hostState = hostState, modifier = modifier){ data ->
        Snackbar(
            snackbarData = data,
            containerColor = when(variant){
                SnackbarType.ERROR -> MaterialTheme.colorScheme.error
                SnackbarType.SUCCESS -> Color.Green
                SnackbarType.INFO -> SnackbarDefaults.color
        },
            contentColor = when(variant){
                SnackbarType.ERROR -> MaterialTheme.colorScheme.onError
                SnackbarType.SUCCESS -> Color.White
                SnackbarType.INFO -> SnackbarDefaults.contentColor
            },
        )
    }
}