package com.tfkcolin.josyandchris.ui.components.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.ui.components.foundation.JACCheckBox
import com.tfkcolin.josyandchris.ui.components.foundation.JACSelect
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACAddTransactionDialog(
    modifier: Modifier = Modifier,
    transaction: FinancialTransaction,
    onTransactionChanged: (FinancialTransaction) -> Unit,
    onDismissRequest: () -> Unit,
    onAddTransaction: () -> Unit,
    devise: String
){
    val priceFocusRequester = remember { FocusRequester() }
    var transactionTypeExpanded by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismissRequest) {
        ElevatedCard(modifier = modifier) {
            Column(
                modifier = Modifier.padding(5.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Nouvelle transaction",
                    style = MaterialTheme.typography.titleLarge
                )
                OutlinedTextField(
                    modifier = Modifier.padding(bottom = 4.dp),
                    value = transaction.label,
                    onValueChange = { onTransactionChanged(transaction.copy(label = it)) },
                    label = {
                        Text(
                            text = "Description de la transaction",
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Next,
                        capitalization = KeyboardCapitalization.Sentences
                    ),
                    keyboardActions = KeyboardActions(onNext = { priceFocusRequester.requestFocus() })
                )
                OutlinedTextField(
                    modifier = Modifier.focusRequester(priceFocusRequester),
                    value = "${transaction.price}",
                    onValueChange = { onTransactionChanged(transaction.copy(price = it.toIntOrNull() ?: 0)) },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Number,
                        imeAction = ImeAction.Done
                    ),
                    label = {
                        Text(
                            text = devise,
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    keyboardActions = KeyboardActions(onNext = { priceFocusRequester.freeFocus() })
                )
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        modifier = Modifier.padding(end = 5.dp),
                        text = "Type",
                        style = MaterialTheme.typography.labelSmall
                    )
                    JACSelect(
                        selected = TransactionType.values()[transaction.transactionTypeIndex].label,
                        expanded = transactionTypeExpanded,
                        onExpandedChange = { transactionTypeExpanded = it }
                    ) {
                        TransactionType.values().forEachIndexed { index, transactionType ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        modifier = Modifier.padding(end = 5.dp),
                                        text = transactionType.label,
                                        style = MaterialTheme.typography.labelLarge
                                    )
                                },
                                onClick = {
                                    onTransactionChanged(transaction.copy(transactionTypeIndex = index))
                                },
                                leadingIcon = {
                                    Icon(
                                        painter = painterResource(
                                            id = transactionType.resourceId
                                        ),
                                        contentDescription = "Sélectionner le type comme ${transactionType.label}"
                                    )
                                }
                            )
                        }
                    }
                }
                JACCheckBox(
                    checked = transaction.marked,
                    onCheckedChange = {
                        onTransactionChanged(transaction.copy(marked = it))
                    },
                    label = {
                        Text(text = "Marquer la transaction ?")
                    },
                    helperText = "Marquer toutes les transactions que vous souhaitez garder en tête. Vous pourez les retrouvez ainsi plus facilement."
                )
                Row {
                    TextButton(
                        modifier = Modifier.weight(1f),
                        onClick = onDismissRequest
                    ) {
                        Text(text = "Annuler")
                    }
                    TextButton(
                        modifier = Modifier.weight(1f),
                        onClick = {
                            onAddTransaction()
                            onDismissRequest()
                        }
                    ) {
                        Text(text = "Ajouter")
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun JACAddTransactionDialogPreview(){
    JACTheme {
        JACAddTransactionDialog(
            transaction = FinancialTransaction(),
            onTransactionChanged = {},
            onDismissRequest = {},
            onAddTransaction = {},
            devise = "FCFA"
        )
    }
}