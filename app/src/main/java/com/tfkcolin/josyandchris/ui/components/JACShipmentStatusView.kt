package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ShipmentStatus
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACShipmentStatusView(
    modifier: Modifier = Modifier,
    status: ShipmentStatus
) {
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(5.dp))
            .background(MaterialTheme.colorScheme.primaryContainer)
            .padding(5.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = status.iconResId),
            contentDescription = status.label,
            tint = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.width(5.dp))
        Text(
            text = status.label,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Preview(showBackground = true)
@Composable
fun JACShipmentStatusViewPreview() {
    JACTheme {
        Column {
            JACShipmentStatusView(modifier = Modifier.padding(bottom = 2.dp), status = ShipmentStatus.RECEIVED_AT_ORIGIN)
            JACShipmentStatusView(modifier = Modifier.padding(bottom = 2.dp), status = ShipmentStatus.IN_CARGO)
            JACShipmentStatusView(modifier = Modifier.padding(bottom = 2.dp), status = ShipmentStatus.READY_FOR_PICKUP)
            JACShipmentStatusView(modifier = Modifier.padding(bottom = 2.dp), status = ShipmentStatus.COLLECTED)
        }
    }
}
