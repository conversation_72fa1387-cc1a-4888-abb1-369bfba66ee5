package com.tfkcolin.josyandchris.ui.components
/*
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.material.ContentAlpha
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.MiniProduct
import com.tfkcolin.josyandchris.ui.components.foundation.VerticalGrid

@Composable
fun JACEditProducts(
    modifier: Modifier = Modifier,
    products: List<MiniProduct>,
    onProductsChange: (List<MiniProduct>) -> Unit,
    /*imagesData: List<ImageData>,
    onAddImageData: () -> Unit,
    productToAdd: MiniProduct,
    onProductToAddChange: (MiniProduct) -> Unit,
    onAddProduct: () -> Unit*/
){
    var showConfirmDeleteDialog by remember { mutableStateOf(false) }
    var showAddProductDialog by remember { mutableStateOf(false) }
    var productToRemove by remember { mutableStateOf(MiniProduct()) }

    Box(modifier = modifier){
        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                modifier = Modifier.padding(bottom = 4.dp),
                text = "Produits",
                style = MaterialTheme.typography.headlineSmall
            )
            Icon(
                modifier = Modifier
                    .size(50.dp)
                    .padding(bottom = 4.dp),
                tint = MaterialTheme.colorScheme.primaryContainer,
                imageVector = Icons.Default.ShoppingCart, contentDescription = null)
            Button(
                modifier = Modifier
                    .padding(bottom = 4.dp),
                onClick = { showAddProductDialog = true }
            ) {
                Icon(imageVector = Icons.Default.AddCircle, contentDescription = null)
                Text(modifier = Modifier.padding(start = 2.dp), text = "Ajouter un produit")
            }
            if(products.isEmpty()){
                Box(modifier = Modifier.height(150.dp)) {
                    CompositionLocalProvider(LocalContentAlpha provides ContentAlpha.disabled) {
                        Text(
                            modifier = Modifier.align(Alignment.Center),
                            text = "Aucun produit sélectionné actuellement",
                            style = MaterialTheme.typography.labelMedium
                        )
                    }
                }
            } else {
                VerticalGrid {
                    products.forEach{ product ->
                        Box {
                            JACProductItem(product = product, onProductChange = {})
                            IconButton(
                                modifier = Modifier.align(Alignment.BottomEnd),
                                onClick = {
                                    productToRemove = product
                                    showConfirmDeleteDialog = true
                                }
                            ) {
                                Icon(imageVector = Icons.Default.Delete, contentDescription = null)
                            }
                        }
                    }
                }
            }
        }
        /*AnimatedVisibility(visible = showAddProductDialog){
            JACAddProductDialog(
                modifier = Modifier.fillMaxHeight(.9f),
                product = productToAdd,
                onProductChanged = { onProductToAddChange(it) },
                images = imagesData,
                onDismissRequest = {
                    showAddProductDialog = false
                    onProductToAddChange(MiniProduct())
                    productToRemove = MiniProduct()
                },
                onValidate = {
                    showAddProductDialog = false
                    productToRemove = MiniProduct()
                    onAddProduct()
                },
                onAddImageData = onAddImageData
            )
        }*/
        AnimatedVisibility(visible = showConfirmDeleteDialog) {
            AlertDialog(
                onDismissRequest = { showConfirmDeleteDialog = false },
                confirmButton = {
                    TextButton(
                        onClick = {
                            onProductsChange(products.filter { it != productToRemove })
                            showConfirmDeleteDialog = false
                        }
                    ) {
                        Text(text = "Oui")
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = {
                            productToRemove = MiniProduct()
                            showConfirmDeleteDialog = false
                        }
                    ) {
                        Text(text = "Non")
                    }
                },
                title = {
                    Text(text = "enlever le produit")
                },
                text = {
                    Text(text = "voulez-vous vraiment enlever le produit ?")
                }
            )
        }
    }
}*/