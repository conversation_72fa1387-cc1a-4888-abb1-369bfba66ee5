package com.tfkcolin.josyandchris.ui.screens.addimagedatascreen

import android.net.Uri
import androidx.compose.runtime.mutableStateOf
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.repository.StorageRepository
import com.tfkcolin.josyandchris.ui.states.ImageScreenUiState
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import com.google.android.gms.tasks.Tasks
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.repository.ImageDataRepository
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.util.query.QueryFilters
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class AddImageDataScreenViewModel @Inject constructor(
    private val storageRepository: StorageRepository,
    private val imageDataRepository: ImageDataRepository
): ViewModel() {
    val currentImageData = mutableStateOf(ImageData())
    private val _uiState = MutableStateFlow(ImageScreenUiState())
    val uiState: StateFlow<ImageScreenUiState> = _uiState.asStateFlow()

    private val _filters = MutableStateFlow(QueryFilters())
    val filters: StateFlow<QueryFilters> = _filters.asStateFlow()
    val paginatedImages: Flow<PagingData<ImageData>> = _filters
        .flatMapLatest { filters ->
            imageDataRepository.getImageDataPagingDataWithFilters(filters)
        }
        .cachedIn(viewModelScope)

    private val _distinctCategories = MutableStateFlow<List<String>>(emptyList())
    val distinctCategories: StateFlow<List<String>> = _distinctCategories.asStateFlow()

    private val _distinctGenres = MutableStateFlow<List<String>>(emptyList())
    val distinctGenres: StateFlow<List<String>> = _distinctGenres.asStateFlow()

    init {
        fetchDistinctCategories()
        fetchDistinctGenres()
    }

    fun updateFilters(category: String?, genre: String?, uploaded: Boolean?) {
        val currentFilters = _filters.value.additionalFilters.toMutableMap()
        if (category == null) currentFilters.remove("category") else currentFilters["category"] = category
        if (genre == null) currentFilters.remove("genre") else currentFilters["genre"] = genre
        if (uploaded == null) currentFilters.remove("upload") else currentFilters["upload"] = uploaded
        _filters.value = QueryFilters(additionalFilters = currentFilters)
    }

    private fun fetchDistinctCategories() {
        viewModelScope.launch {
            imageDataRepository.getDistinctCategories().onSuccess { categories ->
                _distinctCategories.value = listOf("tous") + categories.sorted()
            }.onFailure { exception ->
                _uiState.update {
                    it.copy(
                        imageOperationState = ImageOperationState.Error(
                            exception,
                            "Failed to load categories"
                        )
                    )
                }
            }
        }
    }

    private fun fetchDistinctGenres() {
        viewModelScope.launch {
            imageDataRepository.getDistinctGenres().onSuccess { genres ->
                _distinctGenres.value = listOf("tous") + genres.sorted()
            }.onFailure { exception ->
                _uiState.update {
                    it.copy(
                        imageOperationState = ImageOperationState.Error(
                            exception,
                            "Failed to load genres"
                        )
                    )
                }
            }
        }
    }

    suspend fun addImagesData(imageData: ImageData): Result<Unit> {
        if (imageData.url == null || imageData.genre.isBlank() || imageData.category.isBlank()) {
            _uiState.update {
                it.copy(
                    imageOperationState = ImageOperationState.Error(
                        Exception("Validation Error"),
                        "Vous devez enregistrez le genre et la categorie de cette image"
                    )
                )
            }
            return Result.failure(Exception("Validation Error: Missing required fields"))
        }
        _uiState.update { it.copy(imageOperationState = ImageOperationState.Loading) }
        val result = imageDataRepository.createImageData(imageData)
        _uiState.update {
            if (result.isSuccess) {
                currentImageData.value = ImageData()
                // Re-trigger the flow to refresh the list
                _filters.value = _filters.value.copy()
                it.copy(imageOperationState = ImageOperationState.Success("Image data added successfully"))
            } else {
                it.copy(imageOperationState = ImageOperationState.Error(result.exceptionOrNull()!!))
            }
        }
        return result.map { }
    }

    fun uploadImage(filename: String, imageDataId: String, url: String) {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    imageOperationState = ImageOperationState.Loading,
                    uploadingImageId = imageDataId
                )
            }
            try {
                val uploadResult = storageRepository.uploadFile(filename, url.toUri())

                if (uploadResult.isSuccess) {
                    val downloadUrl = uploadResult.getOrNull()
                    if (downloadUrl != null) {
                        val fieldsToUpdate = mapOf(
                            "url" to downloadUrl.toString(),
                            "upload" to true,
                            "path" to filename
                        )

                        var attempt = 0
                        var updateResult: Result<Unit>
                        do {
                            updateResult = imageDataRepository.updateImageDataFields(imageDataId, fieldsToUpdate)
                            if (updateResult.isFailure) {
                                attempt++
                                delay(2.seconds)
                            }
                        } while (updateResult.isFailure && attempt < 3)


                        if (updateResult.isSuccess) {
                            _uiState.update {
                                it.copy(
                                    imageOperationState = ImageOperationState.Success("Image successfully uploaded and data saved."),
                                )
                            }
                        } else {
                            _uiState.update {
                                it.copy(
                                    imageOperationState = ImageOperationState.Error(
                                        updateResult.exceptionOrNull()!!,
                                        "Upload successful, but failed to save data. Please retry."
                                    ),
                                )
                            }
                        }
                    } else {
                        _uiState.update {
                            it.copy(
                                imageOperationState = ImageOperationState.Error(
                                    Exception("Download URL is null"),
                                    "Upload failed: Could not retrieve download URL."
                                ),
                            )
                        }
                    }
                } else {
                    _uiState.update {
                        it.copy(
                            imageOperationState = ImageOperationState.Error(
                                uploadResult.exceptionOrNull()!!,
                                "Image upload failed. Please check your connection and try again."
                            ),
                        )
                    }
                }
            } finally {
                _uiState.update {
                    it.copy(uploadingImageId = null)
                }
            }
        }
    }

    fun clearState() {
        _uiState.update { ImageScreenUiState() }
    }
}
