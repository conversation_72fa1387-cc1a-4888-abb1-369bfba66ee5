package com.tfkcolin.josyandchris.ui.components.foundation

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun MultipleImagePickerButton(
    modifier: Modifier = Modifier,
    text: String = "Choisir une Image",
    onPickImage: (List<Uri>) -> Unit
) {
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents(),
        onResult = { uriList -> onPickImage(uriList) }
    )
    Box(modifier = modifier) {
        Button(
            modifier = Modifier.fillMaxSize(),
            onClick = { imagePicker.launch("image/*") },
        ) { Text(text = text) }
    }
}

@Preview
@Composable
private fun JACTextFieldPreview(){
    JACTheme {
        MultipleImagePickerButton(text = "", onPickImage = {})
    }
}

//
//class PickImageWithMechanismViewModel: ViewModel(){
//    val uri = mutableStateOf("")
//    fun pickImage() {
//        viewModelScope.launch {
//            val result = resultingActivityHandler.getContent("image/*")
//            uri.value = result?.toString()?:"null"
//        }
//    }
//
//    val resultingActivityHandler = ResultingActivityHandler()
//}
//
//
//class ResultingActivityHandler {
//    private var _callback = mutableStateOf<(@Composable () -> Unit)?>(null)
//
//    suspend fun takePicturePreview(
//        maxTry: Int = 10,
//        millis: Long = 200,
//    ): Bitmap?{
//        return request(
//            ActivityResultContracts
//                .TakePicturePreview(),
//            maxTry,
//            millis
//        ){
//            it.launch(null)
//        }
//    }
//    suspend fun getContent(
//        type: String,
//        maxTry: Int = 10,
//        millis: Long = 200,
//    ): Uri?{
//        return request(
//            ActivityResultContracts.GetContent(),
//            maxTry,
//            millis
//        ){
//            it.launch(type)
//        }
//    }
//
//    @OptIn(ExperimentalCoroutinesApi::class)
//    suspend fun <I, O> request(
//        contract: ActivityResultContract<I, O>,
//        maxTry: Int = 10,
//        millis: Long = 200,
//        launcher: (ManagedActivityResultLauncher<I, O>) -> Unit
//    ): O? =  suspendCancellableCoroutine { coroutine ->
//        _callback.value = {
//            val a = rememberLauncherForActivityResult(
//                contract
//            ) {
//                coroutine.resume(it, null)
//                _callback.value = null
//                return@rememberLauncherForActivityResult
//            }
//
//            LaunchedEffect(a){
//                var tried = 0
//                var tryOn = true
//                while (tryOn){
//                    ++tried
//                    delay(millis)
//                    try {
//                        launcher(a)
//                        tryOn = false
//                    } catch (e: Exception) {
//                        if(tried>maxTry){
//                            tryOn = false
//                            coroutine.resume(null, null)
//                            _callback.value = null
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//    @Composable
//    fun handle() {
//        if(_callback.value != null){
//            _callback.value?.invoke()
//        }
//    }
//}
//@Composable
//fun ImagePickerButton(
//    vm: PickImageWithMechanismViewModel = viewModel()
//) {
//    vm.resultingActivityHandler.handle()
//    Box(
//        contentAlignment = Alignment.Center
//    ){
//        Column(
//            modifier = Modifier
//                .fillMaxWidth(),
//            horizontalAlignment = Alignment.CenterHorizontally
//        ){
//            Button(onClick = {
//                vm.pickImage()
//            }) {
//                Text("Pick")
//            }
//            Text(vm.uri.value)
//        }
//    }
//}
//
