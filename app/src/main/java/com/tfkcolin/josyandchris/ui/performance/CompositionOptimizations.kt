package com.tfkcolin.josyandchris.ui.performance

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.ShipmentStatus
import timber.log.Timber

/**
 * Performance optimization utilities for Compose to reduce recomposition
 * Using stable keys and derivedStateOf for better performance
 */

/**
 * Stable data class for command list item to reduce recomposition
 */
@Stable
data class StableCommandItem(
    val id: String,
    val clientName: String,
    val status: String,
    val stepIndex: Int,
    val totalPrice: Double,
    val createdAt: Long,
    val isPaymentProofUploaded: Boolean
) {
    companion object {
        fun from(command: Command): StableCommandItem {
            return StableCommandItem(
                id = command.id,
                clientName = command.client.name,
                status = CommandStep.values().getOrNull(command.commandStepIndex)?.name ?: "Unknown",
                stepIndex = command.commandStepIndex,
                totalPrice = command.products.sumOf { (it.quantity * it.unitSellingPrice).toDouble() },
                createdAt = command.created.time,
                isPaymentProofUploaded = command.proofUploaded
            )
        }
    }
}

/**
 * Stable data class for cargo list item to reduce recomposition
 */
@Stable
data class StableCargoItem(
    val id: String,
    val clientName: String,
    val status: String,
    val statusIndex: Int,
    val totalWeight: Double,
    val createdAt: Long,
    val productsCount: Int
) {
    companion object {
        fun from(cargo: Cargo): StableCargoItem {
            return StableCargoItem(
                id = cargo.id,
                clientName = cargo.origin, // Using origin as client identifier
                status = CargoStatus.values().getOrNull(cargo.statusIndex)?.label ?: "Unknown",
                statusIndex = cargo.statusIndex,
                totalWeight = 0.0, // Weight not available in Cargo model
                createdAt = cargo.created.time,
                productsCount = 0 // Products not directly available in Cargo
            )
        }
    }
}

/**
 * Stable data class for shipment list item to reduce recomposition  
 */
@Stable
data class StableShipmentItem(
    val id: String,
    val clientName: String,
    val status: String,
    val statusIndex: Int,
    val totalPrice: Double,
    val createdAt: Long,
    val productsCount: Int
) {
    companion object {
        fun from(shipment: Shipment): StableShipmentItem {
            return StableShipmentItem(
                id = shipment.id,
                clientName = shipment.clientName,
                status = ShipmentStatus.values().getOrNull(shipment.statusIndex)?.label ?: "Unknown",
                statusIndex = shipment.statusIndex,
                totalPrice = shipment.amountDue,
                createdAt = shipment.created.time,
                productsCount = shipment.products.size
            )
        }
    }
}

/**
 * Performance-optimized remember function that tracks composition count
 */
@Composable
fun <T> rememberWithLogging(key: Any?, computation: () -> T): T {
    val recompositionCount = remember { mutableIntStateOf(0) }
    
    return remember(key) {
        recompositionCount.intValue++
        if (recompositionCount.intValue > 1) {
            Timber.d("Recomposition #${recompositionCount.intValue} for key: $key")
        }
        computation()
    }
}

/**
 * Optimized derivedStateOf for filtering lists without unnecessary recomposition
 */
@Composable
fun <T> rememberDerivedListFilter(
    list: List<T>,
    filterQuery: String,
    predicate: (T, String) -> Boolean
): List<T> {
    return remember(list, filterQuery) {
        derivedStateOf {
            if (filterQuery.isEmpty()) {
                list
            } else {
                list.filter { item -> predicate(item, filterQuery.lowercase()) }
            }
        }
    }.value
}

/**
 * Optimized derivedStateOf for sorting lists
 */
@Composable
fun <T> rememberDerivedListSorter(
    list: List<T>,
    sortKey: String,
    comparator: Comparator<T>
): List<T> {
    return remember(list, sortKey) {
        derivedStateOf {
            list.sortedWith(comparator)
        }
    }.value
}

/**
 * Performance monitoring composable that logs recomposition frequency
 */
@Composable
fun PerformanceMonitor(
    name: String,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val recompositionCount = remember { mutableIntStateOf(0) }
    val startTime = remember { System.currentTimeMillis() }
    
    SideEffect {
        recompositionCount.intValue++
        val elapsed = System.currentTimeMillis() - startTime
        
        if (recompositionCount.intValue % 10 == 0) { // Log every 10th recomposition
            Timber.d("$name: ${recompositionCount.intValue} recompositions in ${elapsed}ms")
        }
    }
    
    content()
}

/**
 * Stable wrapper for command items with optimized equality checks
 */
@Stable
class StableCommandWrapper(
    private val command: Command
) {
    private val stableItem = StableCommandItem.from(command)
    
    fun getStableItem(): StableCommandItem = stableItem
    fun getOriginalCommand(): Command = command
    
    override fun equals(other: Any?): Boolean {
        return when (other) {
            is StableCommandWrapper -> stableItem == other.stableItem
            else -> false
        }
    }
    
    override fun hashCode(): Int = stableItem.hashCode()
}

/**
 * Extension functions for creating stable wrappers
 */
fun List<Command>.toStableWrappers(): List<StableCommandWrapper> {
    return map { StableCommandWrapper(it) }
}

fun List<Cargo>.toStableCargoItems(): List<StableCargoItem> {
    return map { StableCargoItem.from(it) }
}

fun List<Shipment>.toStableShipmentItems(): List<StableShipmentItem> {
    return map { StableShipmentItem.from(it) }
}

/**
 * Composable key generator for list items to improve LazyColumn performance
 */
object ComposableKeys {
    
    fun commandItemKey(command: Command): String {
        return "command_${command.id}_${command.commandStepIndex}_${command.products.sumOf { it.quantity * it.unitSellingPrice }}"
    }
    
    fun cargoItemKey(cargo: Cargo): String {
        return "cargo_${cargo.id}_${cargo.statusIndex}_${cargo.origin}"
    }
    
    fun shipmentItemKey(shipment: Shipment): String {
        return "shipment_${shipment.id}_${shipment.statusIndex}_${shipment.amountDue}"
    }
    
    fun clientSuggestionKey(clientName: String): String {
        return "client_$clientName"
    }
    
    fun searchSuggestionKey(searchTerm: String, searchType: String): String {
        return "search_${searchType}_$searchTerm"
    }
}

/**
 * Performance-optimized memo for expensive calculations
 */
@Composable
fun <T> expensiveCalculation(
    key1: Any?,
    key2: Any? = null,
    key3: Any? = null,
    calculation: () -> T
): T {
    return remember(key1, key2, key3) {
        Timber.d("Performing expensive calculation with keys: $key1, $key2, $key3")
        calculation()
    }
}
