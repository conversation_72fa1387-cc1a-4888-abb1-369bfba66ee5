package com.tfkcolin.josyandchris.ui.screens.cargodetails

import android.app.DatePickerDialog
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.UserRole
import com.tfkcolin.josyandchris.ui.components.JACShipmentItem
import com.tfkcolin.josyandchris.ui.components.Title
import com.tfkcolin.josyandchris.ui.components.foundation.BoxWithLoading
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import kotlinx.coroutines.launch
import java.text.DateFormat
import java.util.*

@Composable
fun CargoDetailsScreen(
    modifier: Modifier = Modifier,
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onNavigateToShipmentDetailsScreen: (String, String) -> Unit,
    onTitleDescriptionChange: (String) -> Unit
) {
    val viewModel: CargoDetailsViewModel = hiltViewModel()
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    // Load cargo data from ViewModel
    val currentCargo by viewModel.currentCargo.collectAsState()
    var cargo by remember { mutableStateOf(Cargo()) }
    var cargoShipments by remember { mutableStateOf<List<Shipment>>(emptyList()) }
    
    // Load initial data
    LaunchedEffect(viewModel.cargoId) {
        if (!viewModel.isCreate) {
            viewModel.startLiveUpdates()
            cargo = viewModel.getCurrentCargo() ?: Cargo()
        }
    }
    
    // Update cargo when live data changes
    LaunchedEffect(currentCargo) {
        currentCargo?.let {
            cargo = it
        }
    }

    var editedCargo by remember(cargo) { mutableStateOf(cargo) }
    var loading by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    LaunchedEffect(cargo) {
        onTitleDescriptionChange(
            if (viewModel.isCreate) "Nouveau cargo" else "Cargo ${cargo.id}"
        )
    }

    // Delete Confirmation Dialog
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Supprimer le cargo") },
            text = { Text("Êtes-vous sûr de vouloir supprimer ce cargo et toutes ses expéditions? Cette action ne peut pas être annulée.") },
            confirmButton = {
                Button(
                    onClick = {
                        scope.launch {
                            loading = true
                            viewModel.deleteCargo()
                            showDeleteConfirmation = false
                            loading = false
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Supprimer")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text("Annuler")
                }
            }
        )
    }

    BoxWithLoading(
        modifier = modifier.fillMaxSize(),
        loading = loading
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp)
        ) {
            item {
                Title(
                    text = if (viewModel.isCreate) "Nouveau cargo" else "Détails du cargo",
                    color = Color.Black.lighten(.2f)
                )
                
                // Live mode switch and indicator
                val isLiveMode by viewModel.isLiveMode.collectAsState()
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Switch(
                        checked = isLiveMode,
                        onCheckedChange = { viewModel.toggleLiveMode() }
                    )
                    if (isLiveMode) {
                        Text(
                            text = "LIVE", 
                            color = MaterialTheme.colorScheme.primary, 
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Origin field
                OutlinedTextField(
                    value = editedCargo.origin,
                    onValueChange = { editedCargo = editedCargo.copy(origin = it) },
                    label = { Text("Origine") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Destination field
                OutlinedTextField(
                    value = editedCargo.destination,
                    onValueChange = { editedCargo = editedCargo.copy(destination = it) },
                    label = { Text("Destination") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Status selection
                Text(
                    text = "Statut",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    CargoStatus.values().forEach { status ->
                        FilterChip(
                            selected = editedCargo.statusIndex == status.ordinal,
                            onClick = { editedCargo = editedCargo.copy(statusIndex = status.ordinal) },
                            label = { Text(status.label) }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Date fields
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Departure date
                    Button(
                        onClick = {
                            val calendar = Calendar.getInstance()
                            editedCargo.departureDate?.let { calendar.time = it }

                            DatePickerDialog(
                                context,
                                { _, year, month, day ->
                                    val newDate = Calendar.getInstance().apply {
                                        set(Calendar.YEAR, year)
                                        set(Calendar.MONTH, month)
                                        set(Calendar.DAY_OF_MONTH, day)
                                    }.time
                                    editedCargo = editedCargo.copy(departureDate = newDate)
                                },
                                calendar.get(Calendar.YEAR),
                                calendar.get(Calendar.MONTH),
                                calendar.get(Calendar.DAY_OF_MONTH)
                            ).show()
                        }
                    ) {
                        Text(
                            text = if (editedCargo.departureDate != null) {
                                "Départ: ${DateFormat.getDateInstance(DateFormat.MEDIUM).format(editedCargo.departureDate!!)}"
                            } else {
                                "Définir date de départ"
                            }
                        )
                    }

                    // Estimated arrival date
                    Button(
                        onClick = {
                            val calendar = Calendar.getInstance()
                            editedCargo.estimatedArrivalDate?.let { calendar.time = it }

                            DatePickerDialog(
                                context,
                                { _, year, month, day ->
                                    val newDate = Calendar.getInstance().apply {
                                        set(Calendar.YEAR, year)
                                        set(Calendar.MONTH, month)
                                        set(Calendar.DAY_OF_MONTH, day)
                                    }.time
                                    editedCargo = editedCargo.copy(estimatedArrivalDate = newDate)
                                },
                                calendar.get(Calendar.YEAR),
                                calendar.get(Calendar.MONTH),
                                calendar.get(Calendar.DAY_OF_MONTH)
                            ).show()
                        }
                    ) {
                        Text(
                            text = if (editedCargo.estimatedArrivalDate != null) {
                                "Arrivée est.: ${DateFormat.getDateInstance(DateFormat.MEDIUM).format(editedCargo.estimatedArrivalDate!!)}"
                            } else {
                                "Définir date d'arrivée est."
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Actual arrival date
                Button(
                    onClick = {
                        val calendar = Calendar.getInstance()
                        editedCargo.actualArrivalDate?.let { calendar.time = it }

                        DatePickerDialog(
                            context,
                            { _, year, month, day ->
                                val newDate = Calendar.getInstance().apply {
                                    set(Calendar.YEAR, year)
                                    set(Calendar.MONTH, month)
                                    set(Calendar.DAY_OF_MONTH, day)
                                }.time
                                editedCargo = editedCargo.copy(actualArrivalDate = newDate)
                            },
                            calendar.get(Calendar.YEAR),
                            calendar.get(Calendar.MONTH),
                            calendar.get(Calendar.DAY_OF_MONTH)
                        ).show()
                    }
                ) {
                    Text(
                        text = if (editedCargo.actualArrivalDate != null) {
                            "Arrivée réelle: ${DateFormat.getDateInstance(DateFormat.MEDIUM).format(editedCargo.actualArrivalDate!!)}"
                        } else {
                            "Définir date d'arrivée réelle"
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Save button - Only for ADMIN and EMPLOYEE roles
                if (role == UserRole.ADMIN.role || role == UserRole.EMPLOYEE.role) {
                    Button(
                        onClick = {
                            scope.launch {
                                loading = true
                                viewModel.saveCargo(editedCargo)
                                if (viewModel.isCreate) {
                                    editedCargo = Cargo()
                                }
                                loading = false
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Enregistrer")
                    }

                    // Delete button (only for existing cargos and ADMIN role)
                    if (!viewModel.isCreate && role == UserRole.ADMIN.role) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = { showDeleteConfirmation = true },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("Supprimer")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Shipments section
                if (!viewModel.isCreate) {
                    Title(
                        text = "Expéditions",
                        color = Color.Black.lighten(.2f)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Add shipment button
                    Button(
                        onClick = {
                            onNavigateToShipmentDetailsScreen("", cargo.id)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Ajouter une expédition")
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            }

            // Shipment list
            if (!viewModel.isCreate && cargoShipments.isNotEmpty()) {
                items(cargoShipments) { shipment ->
                    JACShipmentItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                            .height(IntrinsicSize.Min),
                        shipment = shipment,
                        onClick = {
                            onNavigateToShipmentDetailsScreen(shipment.id, cargo.id)
                        }
                    )
                }
            } else if (!viewModel.isCreate) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Aucune expédition trouvée",
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun CargoDetailsScreenPreview() {
    JACTheme {
        CargoDetailsScreen(
            setHideAppbar = {},
            role = null,
            trySuspendFunction = { _, _ -> },
            onShowSnackbar = {},
            onNavigateToShipmentDetailsScreen = { _, _ -> },
            onTitleDescriptionChange = {}
        )
    }
}
