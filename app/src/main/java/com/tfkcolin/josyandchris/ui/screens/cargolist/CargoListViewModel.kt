package com.tfkcolin.josyandchris.ui.screens.cargolist

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.tfkcolin.josyandchris.ui.base.BaseViewModel
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.repository.CargoRepository
import com.tfkcolin.josyandchris.remote.datasource.FirebaseCargoDataSource
import com.tfkcolin.josyandchris.ui.data.CargoUiFilterState
import com.tfkcolin.josyandchris.util.query.QueryFilters
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import java.util.*
import javax.inject.Inject

@HiltViewModel
class CargoListViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val cargoRepository: CargoRepository,
    private val cargoDataSource: FirebaseCargoDataSource
) : BaseViewModel() {
    private val arg = CargoArgs(savedStateHandle).index
    private val initialStatus = if (arg >= 0 && arg < CargoStatus.values().size) CargoStatus.values()[arg] else null
    
    // UI filter state management - consistent pattern
    private val _uiFilterState = MutableStateFlow(CargoUiFilterState(selectedStatus = initialStatus))
    val uiFilterState: StateFlow<CargoUiFilterState> = _uiFilterState.asStateFlow()
    
    // Pagination data - consistent pattern
    val cargosPagingData: Flow<PagingData<Cargo>> = _uiFilterState
        .flatMapLatest { filterState ->
            cargoRepository.getCargosPagingDataWithFilters(filterState.toQueryFilters())
        }
        .cachedIn(viewModelScope)
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: CargoUiFilterState) {
        _uiFilterState.value = filterState
    }
    
}

class CargoArgs(savedStateHandle: SavedStateHandle) {
    val index: Int = savedStateHandle[cargoStatusIndexArgs] ?: -1
}
