package com.tfkcolin.josyandchris.ui.screens.countrylist

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.repository.CountryRepository
import com.tfkcolin.josyandchris.ui.data.CountryUiFilterState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CountryListViewModel @Inject constructor(
    private val countryRepository: CountryRepository
): ViewModel() {
    
    // UI filter state management
    private val _uiFilterState = MutableStateFlow(CountryUiFilterState())
    val uiFilterState: StateFlow<CountryUiFilterState> = _uiFilterState.asStateFlow()
    
    // Real-time listeners state for selective listening
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null
    
    // Pagination data - this is the main source of truth for paginated countries
    val countriesPagingData: Flow<PagingData<CountryData>> = _uiFilterState
        .flatMapLatest { filterState ->
            countryRepository.getCountriesPagingDataWithFilters(filterState.toQueryFilters())
        }
        .cachedIn(viewModelScope)
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: CountryUiFilterState) {
        _uiFilterState.value = filterState
    }
    
    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
        Log.i("CountryListVM", "CountryListViewModel cleared")
    }
    
    /**
     * Start listening for real-time updates
     */
    private fun startLiveUpdates() {
        if (_isLiveMode.value) return // Already listening
        
        _isLiveMode.value = true
        Log.i("CountryListVM", "Starting live updates for country list")
        
        // Cancel any existing live updates job
        liveUpdatesJob?.cancel()
        
        // Here you would implement real-time listening logic
        // For now, we'll just log the state change
        liveUpdatesJob = viewModelScope.launch {
            Log.i("CountryListVM", "Live updates started for country list")
        }
    }
    
    /**
     * Stop listening for real-time updates
     */
    private fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
        Log.i("CountryListVM", "Live updates stopped for country list")
    }
    
    /**
     * Toggle live mode on/off
     */
    fun toggleLiveMode() {
        if (_isLiveMode.value) {
            stopLiveUpdates()
        } else {
            startLiveUpdates()
        }
    }
}
