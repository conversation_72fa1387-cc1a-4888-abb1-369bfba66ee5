package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.window.PopupProperties
import com.tfkcolin.josyandchris.cache.database.CacheManager
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.DebouncedSearchState
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import timber.log.Timber

/**
 * Optimized AutoCompleteTextField with intelligent caching and debounced search
 * Uses Room database and in-memory LRU cache for better performance
 */
@OptIn(FlowPreview::class)
@Composable
fun OptimizedAutoCompleteTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    onSuggestionSelected: (String) -> Unit,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    enabled: Boolean = true,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    keyboardOptions: KeyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    debounceTime: Long = 300L,
    suggestionType: String = "client", // "client", "country", "search"
    maxSuggestions: Int = 10
) {
    val context = LocalContext.current
    val cacheManager = remember { CacheManager.getInstance(context) }
    
    var textFieldSize by remember { mutableStateOf(Size.Zero) }
    var expanded by remember { mutableStateOf(false) }
    
    // Use debounced search state for performance
    val searchState = remember { 
        DebouncedSearchState(initialQuery = value, debounceTimeMs = debounceTime) 
    }
    
    // Update search state when value changes
    LaunchedEffect(value) {
        if (searchState.searchQuery.value != value) {
            searchState.updateQuery(value)
        }
    }
    
    // Cached suggestions flow
    val suggestions by remember(suggestionType) {
        searchState.debouncedSearchQuery
            .filter { it.length >= 2 || it.isEmpty() }
            .distinctUntilChanged()
            .flatMapLatest { query ->
                flow {
                    try {
                        val cachedSuggestions = when (suggestionType) {
                            "client" -> cacheManager.getClientSuggestions(query)
                            "search" -> cacheManager.searchHistoryDao.getSuggestedSearchTerms(
                                "command", query, maxSuggestions
                            )
                            else -> emptyList()
                        }
                        emit(cachedSuggestions.take(maxSuggestions))
                    } catch (e: Exception) {
                        Timber.e(e, "Error fetching suggestions for type: $suggestionType")
                        emit(emptyList<String>())
                    }
                }
            }
            .catch { 
                Timber.e(it, "Error in suggestions flow")
                emit(emptyList()) 
            }
    }.collectAsState(initial = emptyList())
    
    // Update expanded state based on suggestions and input
    LaunchedEffect(suggestions, value) {
        expanded = suggestions.isNotEmpty() && value.isNotEmpty()
    }
    
    Box(modifier = modifier) {
        OutlinedTextField(
            modifier = Modifier
                .fillMaxWidth()
                .onGloballyPositioned { coordinates ->
                    textFieldSize = coordinates.size.toSize()
                },
            value = value,
            onValueChange = { newValue ->
                onValueChange(newValue)
                searchState.updateQuery(newValue)
                expanded = newValue.isNotEmpty()
            },
            label = label,
            placeholder = placeholder,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            isError = isError,
            enabled = enabled,
            singleLine = singleLine,
            maxLines = maxLines,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions
        )
        
        // Dropdown suggestions with performance optimizations
        if (expanded && suggestions.isNotEmpty()) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                properties = PopupProperties(focusable = false),
                modifier = Modifier
                    .width(with(LocalDensity.current) { textFieldSize.width.toDp() })
                    .heightIn(max = 200.dp)
            ) {
                LazyColumn {
                    items(
                        items = suggestions,
                        key = { suggestion -> suggestion } // Stable keys for better performance
                    ) { suggestion ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = suggestion,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            },
                            onClick = {
                                onSuggestionSelected(suggestion)
                                onValueChange(suggestion)
                                expanded = false
                                
                                // Cache the selection for future suggestions
                                // Note: This should be moved to a side effect in the parent composable
                            },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}

/**
 * Optimized client-specific autocomplete field
 */
@Composable
fun OptimizedClientAutoCompleteTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    onClientSelected: (String) -> Unit,
    label: @Composable (() -> Unit)? = { Text("Client Name") },
    placeholder: @Composable (() -> Unit)? = { Text("Enter client name...") },
    isError: Boolean = false,
    enabled: Boolean = true
) {
    OptimizedAutoCompleteTextField(
        modifier = modifier,
        value = value,
        onValueChange = onValueChange,
        onSuggestionSelected = onClientSelected,
        label = label,
        placeholder = placeholder,
        isError = isError,
        enabled = enabled,
        suggestionType = "client",
        debounceTime = 300L,
        maxSuggestions = 8
    )
}

/**
 * Optimized search autocomplete field
 */
@Composable
fun OptimizedSearchAutoCompleteTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    onSearchSelected: (String) -> Unit,
    searchType: String = "command",
    label: @Composable (() -> Unit)? = { Text("Search") },
    placeholder: @Composable (() -> Unit)? = { Text("Search...") },
    isError: Boolean = false,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val cacheManager = remember { CacheManager.getInstance(context) }
    
    // Custom suggestions for search history
    val searchSuggestions by remember(value, searchType) {
        if (value.length >= 2) {
            flow {
                try {
                    val suggestions = cacheManager.searchHistoryDao.getSuggestedSearchTerms(
                        searchType, value, 5
                    )
                    emit(suggestions)
                } catch (e: Exception) {
                    Timber.e(e, "Error fetching search suggestions")
                    emit(emptyList<String>())
                }
            }
        } else {
            flowOf(emptyList())
        }
    }.collectAsState(initial = emptyList())
    
    OptimizedAutoCompleteTextField(
        modifier = modifier,
        value = value,
        onValueChange = onValueChange,
        onSuggestionSelected = onSearchSelected,
        label = label,
        placeholder = placeholder,
        isError = isError,
        enabled = enabled,
        suggestionType = "search",
        debounceTime = 400L, // Slightly longer debounce for search
        maxSuggestions = 5
    )
}

@Preview(showBackground = true)
@Composable
fun OptimizedAutoCompleteTextFieldPreview() {
    var clientText by remember { mutableStateOf("") }
    var searchText by remember { mutableStateOf("") }
    
    JACTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OptimizedClientAutoCompleteTextField(
                value = clientText,
                onValueChange = { clientText = it },
                onClientSelected = { clientText = it }
            )
            
            OptimizedSearchAutoCompleteTextField(
                value = searchText,
                onValueChange = { searchText = it },
                onSearchSelected = { searchText = it },
                searchType = "command"
            )
        }
    }
}
