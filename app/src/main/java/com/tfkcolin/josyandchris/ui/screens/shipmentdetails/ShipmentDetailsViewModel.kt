package com.tfkcolin.josyandchris.ui.screens.shipmentdetails

import android.util.Log
import androidx.compose.runtime.*
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.tfkcolin.josyandchris.ui.base.BaseViewModel
import com.google.android.gms.tasks.Tasks
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.ShipmentProduct
// import removed
import com.tfkcolin.josyandchris.repository.ShipmentRepository
import com.tfkcolin.josyandchris.util.safeCall
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class ShipmentDetailsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @Named("SHIPMENT_DB") private val shipmentDB: CollectionReference,
    private val shipmentRepository: ShipmentRepository
) : BaseViewModel() {
    val shipmentId: String = ShipmentArgs(savedStateHandle).id
    val cargoId: String = ShipmentArgs(savedStateHandle).cargoId
    val isCreate = shipmentId.isEmpty()

    private val shipmentRef: MutableState<DocumentReference> = mutableStateOf(
        if (!isCreate) shipmentDB.document(shipmentId)
        else shipmentDB.document()
    )

    private val _changeMade = mutableStateOf(false)
    val changeMade = _changeMade as State<Boolean>

    var newProduct by mutableStateOf(ShipmentProduct())
        private set
        
    // State for holding the loaded shipment
    private val _shipmentState = MutableStateFlow<Shipment?>(null)
    val shipmentState: StateFlow<Shipment?> = _shipmentState.asStateFlow()
    
    init {
        if (!isCreate) {
            loadShipment()
        }
    }
    
    private fun loadShipment() = launchWithErrorHandling {
        safeCall { 
            shipmentRepository.getShipment(shipmentId) 
        }.handleResult(
            onSuccess = { result ->
                result.onSuccess { shipment ->
                    _shipmentState.value = shipment
                }
            }
        )
    }

    override fun onCleared() {
        super.onCleared()
        Log.i("vm", "data cleared")
    }

    fun setChangeMade(value: Boolean) {
        _changeMade.value = value
    }

    fun updateNewProduct(product: ShipmentProduct) {
        newProduct = product
    }

    fun resetNewProduct() {
        newProduct = ShipmentProduct()
    }

suspend fun saveShipment(shipment: Shipment): Result<Unit> = safeCall("Save shipment") {
    if (isCreate) {
        val newShipment = shipment.copy(
            id = shipmentRef.value.id,
            cargoId = cargoId
        )
        Tasks.await(shipmentRef.value.set(newShipment), 15, TimeUnit.MINUTES)
        shipmentRef.value = shipmentDB.document()
    } else {
        Tasks.await(shipmentRef.value.set(shipment), 15, TimeUnit.MINUTES)
    }
}

suspend fun deleteShipment(): Result<Unit> = safeCall("Delete shipment") {
    Tasks.await(shipmentRef.value.delete(), 15, TimeUnit.MINUTES)
}
}

class ShipmentArgs(savedStateHandle: SavedStateHandle) {
    val id: String = savedStateHandle[shipmentIdArgs] ?: ""
    val cargoId: String = savedStateHandle[cargoIdArgs] ?: ""
}
