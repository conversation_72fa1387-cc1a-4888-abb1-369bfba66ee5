package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import kotlinx.coroutines.launch

@Composable
fun NetworkErrorSnackbar(
    snackbarHostState: SnackbarHostState,
    imageOperationState: ImageOperationState,
    onRetry: (() -> Unit)? = null,
    onDismiss: () -> Unit
) {
    val scope = rememberCoroutineScope()
    
    LaunchedEffect(imageOperationState) {
        when (imageOperationState) {
            is ImageOperationState.Error -> {
                val message = when {
                    imageOperationState.message != null -> imageOperationState.message
                    imageOperationState.exception.message?.contains("network", ignoreCase = true) == true -> 
                        "Network error. Please check your connection."
                    imageOperationState.exception.message?.contains("timeout", ignoreCase = true) == true -> 
                        "Request timed out. Please try again."
                    else -> "An error occurred. Please try again."
                }
                
                scope.launch {
                    val result = snackbarHostState.showSnackbar(
                        message = message,
                        actionLabel = if (onRetry != null) "Retry" else "Dismiss",
                        duration = SnackbarDuration.Long
                    )
                    
                    when (result) {
                        SnackbarResult.ActionPerformed -> {
                            if (onRetry != null) {
                                onRetry()
                            } else {
                                onDismiss()
                            }
                        }
                        SnackbarResult.Dismissed -> {
                            onDismiss()
                        }
                    }
                }
            }
            is ImageOperationState.Success -> {
                imageOperationState.message?.let { message ->
                    scope.launch {
                        snackbarHostState.showSnackbar(
                            message = message,
                            duration = SnackbarDuration.Short
                        )
                        onDismiss()
                    }
                }
            }
            else -> {}
        }
    }
}

@Composable
fun LoadingOverlay(
    isLoading: Boolean,
    message: String = "Loading..."
) {
    if (isLoading) {
        AlertDialog(
            onDismissRequest = { },
            title = null,
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Spacer(
                        modifier = Modifier.height(16.dp)
                    )
                    Text(text = message)
                }
            },
            confirmButton = { },
            dismissButton = null
        )
    }
}
