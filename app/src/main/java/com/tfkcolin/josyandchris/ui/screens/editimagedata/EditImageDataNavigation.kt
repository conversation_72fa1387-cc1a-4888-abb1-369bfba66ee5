package com.tfkcolin.josyandchris.ui.screens.editimagedata

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptions
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.LaunchedEffect
import com.tfkcolin.josyandchris.ui.states.ImageOperationState

private const val imageDataIdArgs = "imageDataId"

internal class ImageDataIdArgs(val id: String) {
    constructor(savedStateHandle: SavedStateHandle) :
            this(checkNotNull(savedStateHandle[imageDataIdArgs]) as String)
}

fun NavGraphBuilder.editImageDataScreen(
    onShowSnackbar: (SnackbarState) -> Unit,
    onNavigateBack: () -> Unit
) {
    composable(
        route = "${Screen.EditImageDataScreen.route}/{$imageDataIdArgs}",
        arguments = listOf(
            navArgument(imageDataIdArgs) {
                type = NavType.StringType
            }
        )
    ){
        val viewModel = hiltViewModel<EditImageDataViewModel>()
        val uiState by viewModel.uiState.collectAsState()

        LaunchedEffect(uiState.imageOperationState) {
            when (val state = uiState.imageOperationState) {
                is ImageOperationState.Success -> {
                    onShowSnackbar(SnackbarState(message = state.message ?: "Opération réussie"))
                    if (state.message == "Image deleted successfully" || state.message == "Image data updated") {
                        onNavigateBack()
                    }
                    viewModel.clearState()
                }
                is ImageOperationState.Error -> {
                    onShowSnackbar(SnackbarState(message = state.message ?: "Erreur", type = SnackbarType.ERROR))
                }
                else -> {}
            }
        }

        EditImageDataScreen(
            onUpdate = {
                viewModel.updateImageData()
            },
            imageData = viewModel.currentImageData,
            onImageDataChanged = { viewModel.imageDataChanged(it) },
            onDelete = {
                val path = viewModel.currentImageData.path
                    ?: "product/images/${viewModel.imageDataId}"
                viewModel.deleteImage(path)
            },
            errorMessage = (uiState.imageOperationState as? ImageOperationState.Error)?.message,
            onDismissError = { viewModel.dismissError() },
            isLoading = uiState.imageOperationState is ImageOperationState.Loading
        )
    }
}

fun NavController.navigateToEditImageDataScreen(imageDataId: String, navOptions: NavOptions? = null){
    this.navigate("${Screen.EditImageDataScreen.route}/$imageDataId", navOptions)
}
