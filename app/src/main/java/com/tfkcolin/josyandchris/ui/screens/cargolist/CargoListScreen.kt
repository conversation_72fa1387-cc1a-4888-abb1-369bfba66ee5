package com.tfkcolin.josyandchris.ui.screens.cargolist

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.LoadState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.ui.components.JACCargoItem
import com.tfkcolin.josyandchris.ui.components.LoadingItem
import com.tfkcolin.josyandchris.ui.components.ErrorItem
import com.tfkcolin.josyandchris.ui.components.EmptyStateItem
import com.tfkcolin.josyandchris.ui.components.FullScreenLoadingItem
import com.tfkcolin.josyandchris.ui.components.FullScreenErrorItem

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CargoListScreen(
    modifier: Modifier = Modifier,
    viewModel: CargoListViewModel = hiltViewModel(),
    onNavigateToCargoDetail: (String) -> Unit
) {
    // Collect the initial filter state from ViewModel
    val initialFilterState by viewModel.uiFilterState.collectAsState()
    
    // UI filter state - initialized from ViewModel to respect navigation parameters
    var uiFilterState by remember { mutableStateOf(initialFilterState) }
    
    // Update local state when ViewModel state changes (important for initial load)
    LaunchedEffect(initialFilterState) {
        uiFilterState = initialFilterState
    }
    
    // Collect paging data
    val pager = viewModel.cargosPagingData.collectAsLazyPagingItems()
    
    // Update filters when UI state changes
    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Top app bar
        TopAppBar(
            title = { Text("Cargo List") }
        )

        // Search bar
        OutlinedTextField(
            value = uiFilterState.searchTerm,
            onValueChange = { uiFilterState = uiFilterState.copy(searchTerm = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            placeholder = { Text("Rechercher un cargo...") },
            singleLine = true,
            leadingIcon = {
                Icon(Icons.Default.Search, contentDescription = "Search")
            },
            trailingIcon = {
                if (uiFilterState.searchTerm.isNotEmpty()) {
                    IconButton(onClick = { uiFilterState = uiFilterState.copy(searchTerm = "") }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear search")
                    }
                }
            }
        )

        // Status filter chips
        ScrollableTabRow(
            selectedTabIndex = if (uiFilterState.selectedStatus == null) 0 else (uiFilterState.selectedStatus as CargoStatus).ordinal + 1,
            modifier = Modifier.fillMaxWidth(),
            edgePadding = 0.dp
        ) {
            Tab(
                selected = uiFilterState.selectedStatus == null,
                onClick = { uiFilterState = uiFilterState.copy(selectedStatus = null) },
                text = {
                    Text(
                        text = "All",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            )

            CargoStatus.entries.forEach { status ->
                Tab(
                    selected = uiFilterState.selectedStatus == status,
                    onClick = { uiFilterState = uiFilterState.copy(selectedStatus = status) },
                    text = {
                        Text(
                            text = status.label,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                )
            }
        }

        // Cargo list with Paging 3
        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(pager.itemCount) { index ->
                val cargo = pager[index]
                cargo?.let {
                    JACCargoItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .height(IntrinsicSize.Min),
                        cargo = it,
                        onClick = { onNavigateToCargoDetail(it.id) }
                    )
                }
            }
            
            // Handle paging states
            handlePagingStates(pager)
        }
    }
}

private fun LazyListScope.handlePagingStates(pager: androidx.paging.compose.LazyPagingItems<Cargo>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    FullScreenLoadingItem("Loading cargos...")
                }
            }
            loadState.append is LoadState.Loading -> {
                item {
                    LoadingItem(message = "Loading more cargos...")
                }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    FullScreenErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item {
                    EmptyStateItem("No cargos found")
                }
            }
        }
    }
}
