import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

const val numberOfDots = 7
val dotSize = 10.dp
const val delayUnit = 200
const val duration = numberOfDots * delayUnit
val spaceBetween = 2.dp

val colors = listOf(
    Color(0xFF315DA8),
    Color(0xFFFFFFFF),
    Color(0xFFD8E2FF),
    Color(0xFF001A41),
    Color(0xFF646100),
    Color(0xFFFFFFFF),
    Color(0xFFEEE831),
    Color(0xFF1E1D00),
    Color(0xFF9B4428),
    Color(0xFFFFFFFF),
    Color(0xFFFFDBD0),
    Color(0xFF3A0A00),
    Color(0xFFBA1A1A),
    Color(0xFFFFDAD6),
    Color(0xFFFFFFFF),
    Color(0xFF410002),
    Color(0xFFFEFBFF),
    Color(0xFF1B1B1F),
    Color(0xFFFEFBFF),
    Color(0xFF1B1B1F),
    Color(0xFFE1E2EC),
    Color(0xFF44474F),
    Color(0xFF75777F),
    Color(0xFFF2F0F4),
    Color(0xFF303033),
    Color(0xFFADC6FF),
    Color(0xFF000000),
    Color(0xFF315DA8),
    Color(0xFFADC6FF),
    Color(0xFF002E69),
    Color(0xFF0F448E),
    Color(0xFFD8E2FF),
    Color(0xFFD1CC00),
    Color(0xFF333200),
    Color(0xFF4B4900),
    Color(0xFFEEE831),
    Color(0xFFFFB59F),
    Color(0xFF5E1701),
    Color(0xFF7C2D14),
    Color(0xFFFFDBD0),
    Color(0xFFFFB4AB),
    Color(0xFF93000A),
    Color(0xFF690005),
    Color(0xFFFFDAD6),
    Color(0xFF1B1B1F),
    Color(0xFFE3E2E6),
    Color(0xFF1B1B1F),
    Color(0xFFE3E2E6),
    Color(0xFF44474F),
    Color(0xFFC4C6D0),
    Color(0xFF8E9099),
    Color(0xFF1B1B1F),
    Color(0xFFE3E2E6),
    Color(0xFF315DA8),
    Color(0xFF000000),
    Color(0xFFADC6FF),
    Color(0xFF00204D),
)

@Composable
fun DotsPulsing(
    modifier: Modifier = Modifier,
    numberOfDots: Int = 7
) {
    val dotColors = remember {
        (0 until numberOfDots).map { colors.random() }
    }
    @Composable
    fun Dot(color: Color, scale: Float) {
        Spacer(
            Modifier
                .size(dotSize)
                .scale(scale)
                .background(
                    color = color,
                    shape = CircleShape
                )
        )
    }

    val infiniteTransition = rememberInfiniteTransition()

    @Composable
    fun animateScaleWithDelay(delay: Int) = infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = delayUnit * numberOfDots
            0f at delay with LinearEasing
            1f at delay + delayUnit with LinearEasing
            0f at delay + duration
        })
    )

    val scales = arrayListOf<State<Float>>()

    for (i in 0 until numberOfDots) {
        scales.add(animateScaleWithDelay(delay = i * delayUnit))
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        scales.forEachIndexed { i, state ->
            Dot(color = dotColors[i], state.value)
            Spacer(Modifier.width(spaceBetween))
        }
    }
}


@Composable
fun DotsElastic(
    modifier: Modifier = Modifier,
    numberOfDots: Int = 7
) {
    val minScale = 0.6f
    val dotColors = remember {
        (0 until numberOfDots).map { colors.random() }
    }
    @Composable
    fun Dot(color: Color, scale: Float) {
        Spacer(
            Modifier
                .size(dotSize)
                .scale(scaleX = minScale, scaleY = scale)
                .background(
                    color = color,
                    shape = CircleShape
                )
        )
    }

    val infiniteTransition = rememberInfiniteTransition()

    @Composable
    fun animateScaleWithDelay(delay: Int) = infiniteTransition.animateFloat(
        initialValue = minScale,
        targetValue = minScale,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = duration
            minScale at delay with LinearEasing
            1f at delay + delayUnit with LinearEasing
            minScale at delay + duration
        })
    )

    val scales = arrayListOf<State<Float>>()
    for (i in 0 until numberOfDots) {
        scales.add(animateScaleWithDelay(delay = i * delayUnit))
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        scales.forEachIndexed { i, state ->
            Dot(color = dotColors[i], state.value)
            Spacer(Modifier.width(spaceBetween))
        }
    }
}

@Composable
fun DotsFlashing(
    modifier: Modifier = Modifier,
    numberOfDots: Int = 7
) {
    val minAlpha = 0.1f
    val dotColors = remember {
        (0 until numberOfDots).map { colors.random() }
    }
    @Composable
    fun Dot(color: Color, alpha: Float) = Spacer(
        Modifier
            .size(dotSize)
            .alpha(alpha)
            .background(
                color = color, shape = CircleShape
            )
    )

    val infiniteTransition = rememberInfiniteTransition()

    @Composable
    fun animateAlphaWithDelay(delay: Int) = infiniteTransition.animateFloat(
        initialValue = minAlpha,
        targetValue = minAlpha,
        animationSpec = infiniteRepeatable(
            animation = keyframes {
                durationMillis = duration
                minAlpha at delay with LinearEasing
                1f at delay + delayUnit with LinearEasing
                minAlpha at delay + duration
            }
        )
    )

    val alphas = arrayListOf<State<Float>>()
    for (i in 0 until numberOfDots) {
        alphas.add(animateAlphaWithDelay(delay = i * delayUnit))
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        alphas.forEachIndexed { i, state ->
            Dot(color = dotColors[i], state.value)
            Spacer(Modifier.width(spaceBetween))
        }
    }
}

@Composable
fun DotsTyping(
    modifier: Modifier = Modifier,
    numberOfDots: Int = 7
) {
    val maxOffset = (numberOfDots * 2).toFloat()
    val dotColors = remember {
        (0 until numberOfDots).map { colors.random() }
    }

    @Composable
    fun Dot(color: Color, offset: Float) {
        Spacer(
            Modifier
                .size(dotSize)
                .offset(y = -offset.dp)
                .background(
                    color = color,
                    shape = CircleShape
                )
        )
    }

    val infiniteTransition = rememberInfiniteTransition()

    @Composable
    fun animateOffsetWithDelay(delay: Int) = infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = duration
            0f at delay with LinearEasing
            maxOffset at delay + delayUnit with LinearEasing
            0f at delay + (duration/2)
        })
    )

    val offsets = arrayListOf<State<Float>>()
    for (i in 0 until numberOfDots) {
        offsets.add(animateOffsetWithDelay(delay = i * delayUnit))
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = modifier.padding(top = maxOffset.dp)
    ) {
        offsets.forEachIndexed { i, state ->
            Dot(color = dotColors[i], state.value)
            Spacer(Modifier.width(spaceBetween))
        }
    }
}

@Composable
fun DotsCollision(
    modifier: Modifier = Modifier,
    numberOfDots: Int = 7
) {
    val maxOffset = 30f
    val delayUnit = 500
    val dotColors = remember {
        (0 until numberOfDots).map { colors.random() }
    }

    @Composable
    fun Dot(
        color: Color,
        offset: Float
    ) {
        Spacer(
            Modifier
                .size(dotSize)
                .offset(x = offset.dp)
                .background(
                    color = color,
                    shape = CircleShape
                )
        )
    }

    val infiniteTransition = rememberInfiniteTransition()

    val offsetLeft by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = delayUnit * 3
            0f at 0 with LinearEasing
            -maxOffset at delayUnit / 2 with LinearEasing
            0f at delayUnit
        })
    )
    val offsetRight by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = delayUnit * 3
            0f at delayUnit with LinearEasing
            maxOffset at delayUnit + delayUnit / 2 with LinearEasing
            0f at delayUnit * 2
        })
    )

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = modifier.padding(horizontal = maxOffset.dp)
    ) {
        Dot(color = dotColors[numberOfDots - 2] , offset = offsetLeft)
        Spacer(Modifier.width(spaceBetween))
        for (i in 0 until numberOfDots - 2) {
            Dot(color = dotColors[i], offset = offsetLeft)
            Spacer(Modifier.width(spaceBetween))
        }
        Dot(color = dotColors[numberOfDots - 1], offset = offsetRight)
    }
}


@Preview(showBackground = true)
@Composable
fun DotsPreview() = MaterialTheme {
    Column(modifier = Modifier.padding(4.dp)) {
        val spaceSize = 16.dp

        Text(
            text = "Dots pulsing", style = MaterialTheme.typography.h5
        )
        DotsPulsing()

        Spacer(Modifier.height(spaceSize))

        Text(
            text = "Dots elastic", style = MaterialTheme.typography.h5
        )
        DotsElastic()

        Spacer(Modifier.height(spaceSize))

        Text(
            text = "Dots flashing", style = MaterialTheme.typography.h5
        )
        DotsFlashing()

        Spacer(Modifier.height(spaceSize))

        Text(
            text = "Dots typing", style = MaterialTheme.typography.h5
        )
        DotsTyping()

        Spacer(Modifier.height(spaceSize))

        Text(
            text = "Dots collision", style = MaterialTheme.typography.h5
        )
        DotsCollision()
    }
}