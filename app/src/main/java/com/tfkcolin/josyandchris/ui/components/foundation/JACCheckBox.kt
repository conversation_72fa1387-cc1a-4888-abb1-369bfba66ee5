package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun JACCheckBox(
    modifier: Modifier = Modifier,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    label: @Composable (() -> Unit)? = null,
    helperText: String? = null
){
    var expanded by remember { mutableStateOf(false) }
    Row(
        modifier = modifier.padding(2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        label?.invoke()
        Checkbox(
            modifier = Modifier
                .padding(start = 5.dp),
            checked = checked,
            onCheckedChange = onCheckedChange
        )
        helperText?.let{ text ->
            Box {
                IconButton(onClick = { expanded = !expanded }) {
                    Icon(
                        imageVector = Icons.Filled.Info,
                        contentDescription = "click icon to show the helper text"
                    )
                }
                DropdownMenu(expanded = expanded, onDismissRequest = { expanded = false }) {
                    Surface(
                        modifier = Modifier.padding(2.dp),
                        shape = RoundedCornerShape(
                            topEnd = 10.dp,
                            bottomStart = 10.dp,
                            bottomEnd = 10.dp
                        )
                    ) {
                        Text(
                            modifier = Modifier.widthIn(max = 200.dp),
                            text = text,
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
            }
        }
    }
}