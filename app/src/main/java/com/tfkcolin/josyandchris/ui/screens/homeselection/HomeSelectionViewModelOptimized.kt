package com.tfkcolin.josyandchris.ui.screens.homeselection

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.repository.CommandRepository
import com.tfkcolin.josyandchris.repository.CargoRepository
import com.tfkcolin.josyandchris.repository.CountryRepository
import com.tfkcolin.josyandchris.repository.FinancialTransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Data classes for UI state
 */
data class CommandCounts(
    val pending: Int,
    val inProgress: Int,
    val delivered: Int,
    val total: Int
)


data class CountryStatistics(
    val country: CountryData,
    val commandCount: Int,
    val cargoCount: Int,
    val totalAmount: Double
)

/**
 * Optimized ViewModel for HomeSelection screen that uses aggregate queries
 * to efficiently load statistics without loading all documents
 */
@HiltViewModel
class HomeSelectionViewModelOptimized @Inject constructor(
    private val commandRepository: CommandRepository,
    private val cargoRepository: CargoRepository,
    private val countryRepository: CountryRepository,
    private val transactionRepository: FinancialTransactionRepository
) : ViewModel() {
    
    // Command counts by status - Raw index-based counts for direct display
    private val _commandCountsByIndex = MutableStateFlow<Map<Int, Long>>(emptyMap())
    val commandCountsByIndex: StateFlow<Map<Int, Long>> = _commandCountsByIndex.asStateFlow()
    
    // Cargo counts by status
    private val _cargoCounts = MutableStateFlow<Map<CargoStatus, Int>>(emptyMap())
    val cargoCounts: StateFlow<Map<CargoStatus, Int>> = _cargoCounts.asStateFlow()
    
    // Country statistics
    private val _countryStats = MutableStateFlow<Map<String, CountryStats>>(emptyMap())
    val countryStats: StateFlow<Map<String, CountryStats>> = _countryStats.asStateFlow()
    
    // Loading states
    private val _isLoadingCommands = MutableStateFlow(false)
    val isLoadingCommands: StateFlow<Boolean> = _isLoadingCommands.asStateFlow()
    
    private val _isLoadingCargos = MutableStateFlow(false)
    val isLoadingCargos: StateFlow<Boolean> = _isLoadingCargos.asStateFlow()
    
    private val _isLoadingCountries = MutableStateFlow(false)
    val isLoadingCountries: StateFlow<Boolean> = _isLoadingCountries.asStateFlow()
    
    // Error states
    private val _commandsError = MutableStateFlow<String?>(null)
    val commandsError: StateFlow<String?> = _commandsError.asStateFlow()
    
    private val _cargosError = MutableStateFlow<String?>(null)
    val cargosError: StateFlow<String?> = _cargosError.asStateFlow()
    
    private val _countriesError = MutableStateFlow<String?>(null)
    val countriesError: StateFlow<String?> = _countriesError.asStateFlow()
    
    init {
        loadCommandCounts()
        loadCargoCounts()
        loadCountryStatistics()
    }
    
    /**
     * Load command counts using aggregate queries (efficient)
     */
    private fun loadCommandCounts() {
        viewModelScope.launch {
            _isLoadingCommands.value = true
            _commandsError.value = null
            try {
                commandRepository.getCommandsCountByAllStatuses().fold(
                    onSuccess = { counts ->
                        Timber.d("Raw command counts from repository: $counts")
                        // Just use the raw counts directly
                        _commandCountsByIndex.value = counts
                        Timber.d("Raw command counts for UI: $counts")
                        _commandsError.value = null
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Error loading command counts")
                        _commandsError.value = exception.message ?: "Error loading command statistics"
                        _commandCountsByIndex.value = emptyMap()
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error loading command counts")
                _commandsError.value = e.message ?: "Unexpected error"
                _commandCountsByIndex.value = emptyMap()
            } finally {
                _isLoadingCommands.value = false
            }
        }
    }
    
    /**
     * Load cargo counts using aggregate queries (efficient)
     */
    private fun loadCargoCounts() {
        viewModelScope.launch {
            _isLoadingCargos.value = true
            _cargosError.value = null
            try {
                cargoRepository.getCargosCountByAllStatuses().fold(
                    onSuccess = { counts ->
                        val cargoStatusCounts = counts.mapKeys { (statusIndex, _) ->
                            CargoStatus.values().getOrNull(statusIndex) ?: CargoStatus.LOADING
                        }.mapValues { it.value.toInt() }
                        _cargoCounts.value = cargoStatusCounts
                        _cargosError.value = null
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Error loading cargo counts")
                        _cargosError.value = exception.message ?: "Error loading cargo statistics"
                        _cargoCounts.value = emptyMap()
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error loading cargo counts")
                _cargosError.value = e.message ?: "Unexpected error"
                _cargoCounts.value = emptyMap()
            } finally {
                _isLoadingCargos.value = false
            }
        }
    }
    
    /**
     * Load country statistics using aggregate queries
     */
    private fun loadCountryStatistics() {
        viewModelScope.launch {
            _isLoadingCountries.value = true
            _countriesError.value = null
            try {
                // Use loadCountries instead of getCountriesFlow to avoid listener exceptions
                countryRepository.loadCountries(limit = 100).fold(
                    onSuccess = { countries ->
                        val stats = mutableMapOf<String, CountryStats>()
                        
                        // For each country, get transaction statistics
                        countries.forEach { country ->
                            try {
                                // Get transaction totals for this country
                                val inputTotal = transactionRepository.getTransactionsTotalByCountryAndType(
                                    country.name,
                                    0 // INPUT type
                                ).getOrDefault(0.0)
                                
                                val outputTotal = transactionRepository.getTransactionsTotalByCountryAndType(
                                    country.name,
                                    1 // OUTPUT type
                                ).getOrDefault(0.0)
                                
                                stats[country.name] = CountryStats(
                                    country = country,
                                    inputTotal = inputTotal,
                                    outputTotal = outputTotal,
                                    balance = inputTotal - outputTotal
                                )
                            } catch (e: Exception) {
                                Timber.e(e, "Error loading stats for country ${country.name}")
                                // Continue with next country instead of crashing
                            }
                        }
                        
                        _countryStats.value = stats
                        _countriesError.value = null // Clear error if successful
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Error loading countries")
                        _countriesError.value = exception.message ?: "Error loading countries"
                        _countryStats.value = emptyMap()
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Error in country statistics coroutine")
                _countriesError.value = e.message ?: "Error loading country statistics"
                _countryStats.value = emptyMap() // Set empty map on error
            } finally {
                _isLoadingCountries.value = false
            }
        }
    }
    
    /**
     * Refresh all data
     */
    fun refresh() {
        loadCommandCounts()
        loadCargoCounts()
        loadCountryStatistics()
    }
    
    /**
     * Add a new country
     */
    fun addCountry(
        countryData: CountryData,
        onSuccess: (id: String) -> Unit,
        onError: (err: String) -> Unit
    ) {
        viewModelScope.launch {
            countryRepository.createCountry(countryData).fold(
                onSuccess = { id ->
                    onSuccess(id)
                    loadCountryStatistics() // Refresh statistics
                },
                onFailure = { exception ->
                    onError(exception.message ?: "Unknown error")
                }
            )
        }
    }
    
    /**
     * Delete a country
     */
    fun deleteCountry(
        countryData: CountryData,
        onSuccess: () -> Unit,
        onError: (err: String) -> Unit
    ) {
        viewModelScope.launch {
            countryRepository.deleteCountry(countryData.id).fold(
                onSuccess = {
                    onSuccess()
                    loadCountryStatistics() // Refresh statistics
                },
                onFailure = { exception ->
                    onError(exception.message ?: "Unknown error")
                }
            )
        }
    }
    
    /**
     * Data class to hold country statistics
     */
    data class CountryStats(
        val country: CountryData,
        val inputTotal: Double,
        val outputTotal: Double,
        val balance: Double
    )
}
