package com.tfkcolin.josyandchris.ui.components

import android.util.Log
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.MiniProduct
import com.tfkcolin.josyandchris.ui.components.foundation.JACSelect
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun JACProductItem(
    modifier: Modifier = Modifier,
    color: Color = Color.Red,
    product: MiniProduct,
    onProductChange: (MiniProduct) -> Unit,
    soldOutEditable: Boolean = true
    //onShowImagePreview: () -> Unit
) {
    //val gain = remember { (product.unitSellingPrice - product.unitBuyingPrice) * product.quantity }
    var showDescription by remember { mutableStateOf(false) }
    var expand by remember { mutableStateOf(false) }
    LaunchedEffect(product){
        Log.i("product", product.toString())
    }
    Box(modifier = modifier){
        OutlinedCard(
            modifier = Modifier.clickable { showDescription = !showDescription },
            border = BorderStroke(width = 1.dp, color = color),
            shape = RoundedCornerShape(5)
        ) {
            Column(
                modifier = Modifier
                    .padding(3.dp)
                    .fillMaxSize()
                    .animateContentSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                JACImage(
                    modifier = Modifier.height(200.dp),
                    data = product.productImage
                )
                Text(
                    text = "${product.quantity} ${product.name}",
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = if(showDescription) 10 else 1,
                    overflow = TextOverflow.Ellipsis
                )
                if(showDescription){
                    JACSelect(
                        modifier = Modifier.padding(2.dp),
                        selected = product.productEvolutionStep ?: "",
                        onExpandedChange = { expand = it },
                        expanded = expand
                    ) {
                        CommandStep.values().forEach { step ->
                            TextButton(
                                onClick = {
                                    onProductChange(product.copy(productEvolutionStep = step.step))
                                    expand = false
                                }
                            ) {
                                Text(text = step.step)
                            }
                        }
                    }
                    Row(
                        modifier = Modifier.padding(3.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            modifier = Modifier.weight(2f),
                            text = "Rupture de stock ?"
                        )
                        Checkbox(
                            modifier = Modifier.weight(1f),
                            checked = product.soldOut ?: false,
                            enabled = soldOutEditable,
                            onCheckedChange = { checked ->
                                onProductChange(product.copy(soldOut = checked))
                            }
                        )
                    }
                    Text(
                        modifier = Modifier.padding(bottom = 3.dp),
                        text = product.description,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACProductItemPreview(){
    JACTheme {
        JACProductItem(
            modifier = Modifier
                .size(width = 200.dp, height = 400.dp),
            product = MiniProduct(
                name = "sac",
                quantity = 5,
                unitSellingPrice = 2000,
                unitBuyingPrice = 1200,
                description = "un beau sac pour les femmes de plus de 30 ans"
            ),
            onProductChange = {},
            //onShowImagePreview = {},
        )
    }
}