package com.tfkcolin.josyandchris.ui.screens.addimagedatascreen

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptions
import androidx.navigation.compose.composable
import androidx.paging.compose.collectAsLazyPagingItems
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.delay

fun NavGraphBuilder.addImageDataScreen(
    onShowSnackbar: (SnackbarState) -> Unit,
    onNavigateToEditImageScreen: (String) -> Unit
){
    composable(route = Screen.AddProductScreen.route){
        val viewModel = hiltViewModel<AddImageDataScreenViewModel>()
        
        // Get paginated images
        val paginatedImages = viewModel.paginatedImages.collectAsLazyPagingItems()
        
        // Observe UI state
        val uiState by viewModel.uiState.collectAsState()
        val distinctCategories by viewModel.distinctCategories.collectAsState()
        val distinctGenres by viewModel.distinctGenres.collectAsState()
        val filters by viewModel.filters.collectAsState()

        LaunchedEffect(uiState.imageOperationState) {
            when (val state = uiState.imageOperationState) {
                is ImageOperationState.Success -> {
                    onShowSnackbar(SnackbarState(message = state.message ?: "Success", type = SnackbarType.SUCCESS))
                    delay(2000)
                    viewModel.clearState()
                }
                is ImageOperationState.Error -> {
                    if (state.message != null) {
                        onShowSnackbar(SnackbarState(message = state.message, type = SnackbarType.ERROR))
                        delay(2000)
                        viewModel.clearState()
                    }
                }
                else -> {}
            }
        }

        AddImageDataScreen(
            paginatedImages = paginatedImages,
            uiState = uiState,
            onNavigateToEditImageScreen = onNavigateToEditImageScreen,
            onAddImageData = { imageData ->
                viewModel.addImagesData(imageData)
            },
            onUploadImage = { name, id, url ->
                viewModel.uploadImage(name, id, url)
            },
            imageData = viewModel.currentImageData.value,
            onImageDataChanged = { viewModel.currentImageData.value = it },
            onFilterChange = { category, genre, uploaded ->
                viewModel.updateFilters(category, genre, uploaded)
            },
            onDismissError = {
                viewModel.clearState()
            },
            distinctCategories = distinctCategories,
            distinctGenres = distinctGenres,
            filters = filters
        )
    }
}

fun NavController.navigateToAddImageDataScreen(navOptions: NavOptions? = null){
    this.navigate(Screen.AddProductScreen.route, navOptions)
}
