package com.tfkcolin.josyandchris.ui.screens.commandlist

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.*
import androidx.navigation.compose.composable
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.data.Screen

// Navigation argument constants
private const val CLIENT_FILTER_ARG = "clientFilter"
private const val STEP_FILTER_ARG = "stepFilter"

/**
 * CommandArgs class with support for pre-applied filters
 */
internal class CommandArgs(savedStateHandle: SavedStateHandle) {
    val clientFilter: String? = savedStateHandle[CLIENT_FILTER_ARG]
    val stepFilterIndex: Int = savedStateHandle[STEP_FILTER_ARG] ?: -1

    val preAppliedStepFilter: CommandStep? = if (stepFilterIndex >= 0 && stepFilterIndex < CommandStep.entries.size) {
        CommandStep.entries[stepFilterIndex]
    } else null
}

/**
 * Navigation setup for the CommandListScreen
 */
fun NavGraphBuilder.commandListNavigation(
    onNavigateToCommandDetail: (String) -> Unit,
    onNavigateToCreateCommand: () -> Unit
) {
    composable(
        route = buildRoute(),
        arguments = buildArguments()
    ) { backStackEntry ->
        val args = CommandArgs(backStackEntry.savedStateHandle)
        val viewModel = hiltViewModel<CommandListViewModel>()

        CommandListScreen(
            onNavigateToCommandDetail = onNavigateToCommandDetail,
            onNavigateToCreateCommand = onNavigateToCreateCommand,
            preAppliedClientFilter = args.clientFilter,
            preAppliedStepFilter = args.preAppliedStepFilter,
            viewModel = viewModel
        )
    }
}

/**
 * Navigation function to the CommandListScreen with optional pre-applied filters
 */
fun NavController.navigateToCommandScreen(
    clientFilter: String? = null,
    stepFilter: CommandStep? = null,
    navOptions: NavOptionsBuilder.() -> Unit = {
        popUpTo(Screen.CountrySelectionScreen.route) { inclusive = false }
    }
) {
    val route = buildNavigationRoute(
        clientFilter = clientFilter,
        stepFilter = stepFilter
    )

    navigate(route) {
        navOptions()
    }
}

// Private helper functions
private fun buildRoute(): String {
    return "${Screen.CommandScreen.route}" +
           "?$CLIENT_FILTER_ARG={$CLIENT_FILTER_ARG}" +
           "&$STEP_FILTER_ARG={$STEP_FILTER_ARG}"
}

private fun buildArguments(): List<NamedNavArgument> {
    return listOf(
        navArgument(CLIENT_FILTER_ARG) {
            type = NavType.StringType
            nullable = true
            defaultValue = null
        },
        navArgument(STEP_FILTER_ARG) {
            type = NavType.IntType
            defaultValue = -1
        }
    )
}

private fun buildNavigationRoute(
    clientFilter: String? = null,
    stepFilter: CommandStep? = null
): String {
    val baseRoute = Screen.CommandScreen.route
    val queryParams = mutableListOf<String>()

    clientFilter?.let { filter ->
        queryParams.add("$CLIENT_FILTER_ARG=${filter.replace(" ", "%20")}")
    }

    stepFilter?.let { filter ->
        queryParams.add("$STEP_FILTER_ARG=${filter.ordinal}")
    }

    return if (queryParams.isNotEmpty()) {
        "$baseRoute?${queryParams.joinToString("&")}"
    } else {
        baseRoute
    }
}
