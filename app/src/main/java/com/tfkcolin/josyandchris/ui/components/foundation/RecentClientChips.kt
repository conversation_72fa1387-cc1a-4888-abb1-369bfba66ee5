package com.tfkcolin.josyandchris.ui.components.foundation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecentClientChips(
    modifier: Modifier = Modifier,
    recentClients: List<String>,
    onClientSelected: (String) -> Unit,
    title: String = "Recent Clients"
) {
    if (recentClients.isNotEmpty()) {
        Column(
            modifier = modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(recentClients) { clientName ->
                    AssistChip(
                        onClick = { onClientSelected(clientName) },
                        label = { 
                            Text(
                                text = clientName,
                                style = MaterialTheme.typography.labelMedium
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun RecentClientChipsPreview() {
    val sampleClients = listOf(
        "John Doe",
        "Jane Smith", 
        "Alice Johnson",
        "Bob Wilson",
        "Carol Brown"
    )
    
    JACTheme {
        Surface {
            RecentClientChips(
                modifier = Modifier.padding(16.dp),
                recentClients = sampleClients,
                onClientSelected = { /* Handle selection */ }
            )
        }
    }
}
