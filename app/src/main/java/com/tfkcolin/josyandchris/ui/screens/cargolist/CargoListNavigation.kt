package com.tfkcolin.josyandchris.ui.screens.cargolist

import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.ui.data.Screen

const val cargoStatusIndexArgs = "cargoStatusIndex"

fun NavGraphBuilder.cargoListNavigation(
    onNavigateToCargoDetail: (String) -> Unit
) {
    composable(
        route = "${Screen.CargoScreen.route}?$cargoStatusIndexArgs={$cargoStatusIndexArgs}",
        arguments = listOf(navArgument(cargoStatusIndexArgs) {
            type = NavType.IntType
            defaultValue = -1
        })
    ) {
        CargoListScreen(
            onNavigateToCargoDetail = onNavigateToCargoDetail
        )
    }
}

fun NavController.navigateToCargoScreen(
    statusIndex: Int = -1,
    navOptions: NavOptionsBuilder.() -> Unit = {
        this.popUpTo(Screen.CountrySelectionScreen.route) { inclusive = false }
    }
) {
    this.navigate(
        "${Screen.CargoScreen.route}?$cargoStatusIndexArgs=$statusIndex",
        navOptions
    )
}
