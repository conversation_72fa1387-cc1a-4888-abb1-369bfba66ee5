package com.tfkcolin.josyandchris.ui.components.foundation

import DotsTyping
import androidx.compose.animation.Crossfade
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import com.tfkcolin.josyandchris.ui.theme.JACTheme

@Composable
fun ButtonWithLoading(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    loading: Boolean = false,
    colors: ButtonColors = ButtonDefaults.buttonColors(),
    onClick: () -> Unit,
    content: @Composable () -> Unit
){
    Button(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        colors = colors,
        content = {
            Crossfade(targetState = loading, label = "typing") {
                if(it) DotsTyping()
                else content()
            }
        }
    )
}

@Preview
@Composable
private fun ButtonWithLoadingPreview(){
    JACTheme {
        ButtonWithLoading(
            onClick = {},
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Green,
                contentColor = Color.White
            )
        ){
            Text(text = "Oui")
        }
    }
}