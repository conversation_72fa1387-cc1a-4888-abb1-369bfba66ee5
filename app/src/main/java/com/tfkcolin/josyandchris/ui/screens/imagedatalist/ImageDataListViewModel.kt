package com.tfkcolin.josyandchris.ui.screens.imagedatalist

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.repository.ImageDataRepository
import com.tfkcolin.josyandchris.ui.data.ImageDataUiFilterState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ImageDataUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isRefreshing: Boolean = false
)

@HiltViewModel
class ImageDataListViewModel @Inject constructor(
    private val imageDataRepository: ImageDataRepository
): ViewModel() {
    
    // UI state management
    private val _uiState = MutableStateFlow(ImageDataUiState())
    val uiState: StateFlow<ImageDataUiState> = _uiState.asStateFlow()
    
    // UI filter state management
    private val _uiFilterState = MutableStateFlow(ImageDataUiFilterState())
    val uiFilterState: StateFlow<ImageDataUiFilterState> = _uiFilterState.asStateFlow()
    
    // Real-time listeners state for selective listening
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null
    
    // Pagination data - this is the main source of truth for paginated image data
    val imageDataPagingData: Flow<PagingData<ImageData>> = _uiFilterState
        .flatMapLatest { filterState ->
            imageDataRepository.getImageDataPagingDataWithFilters(filterState.toQueryFilters())
        }
        .cachedIn(viewModelScope)
    
    /**
     * Update UI filter state which will trigger pagination refresh
     */
    fun updateUiFilterState(filterState: ImageDataUiFilterState) {
        _uiFilterState.value = filterState
    }
    
    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
        Log.i("ImageDataListVM", "ImageDataListViewModel cleared")
    }
    
    /**
     * Start listening for real-time updates
     */
    private fun startLiveUpdates() {
        if (_isLiveMode.value) return // Already listening
        
        _isLiveMode.value = true
        Log.i("ImageDataListVM", "Starting live updates for image data list")
        
        // Cancel any existing live updates job
        liveUpdatesJob?.cancel()
        
        // Here you would implement real-time listening logic
        // For now, we'll just log the state change
        liveUpdatesJob = viewModelScope.launch {
            Log.i("ImageDataListVM", "Live updates started for image data list")
        }
    }
    
    /**
     * Stop listening for real-time updates
     */
    private fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
        Log.i("ImageDataListVM", "Live updates stopped for image data list")
    }
    
    /**
     * Toggle live mode on/off
     */
    fun toggleLiveMode() {
        if (_isLiveMode.value) {
            stopLiveUpdates()
        } else {
            startLiveUpdates()
        }
    }
}
