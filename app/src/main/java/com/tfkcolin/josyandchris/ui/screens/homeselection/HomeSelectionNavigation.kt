package com.tfkcolin.josyandchris.ui.screens.homeselection

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.compose.composable
import com.tfkcolin.josyandchris.auth.AuthManager
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationState
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue

// Adds conversation screen to `this` NavGraphBuilder
fun NavGraphBuilder.countrySelectionScreen(
    onShowSnackbar: (SnackbarState) -> Unit,
    countries: List<CountryData>,
    role: String?,
    authManager: AuthManager,
    // Navigation events are exposed to the caller to be handled at a higher level
    onNavigateToCountry: (country: CountryData) -> Unit,
    onNavigateToCommandScreen: (commandStepIndex: Int) -> Unit,
    onNavigateToAddImageScreen: () -> Unit,
    onNavigateToCargoScreen: (cargoStatusIndex: Int) -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit
) {
    composable(route = Screen.CountrySelectionScreen.route) {
        // Using the optimized ViewModel that uses aggregate queries
        val viewModel = hiltViewModel<HomeSelectionViewModelOptimized>()
        val user = authManager.currentUser?.email ?: "Anonyme"
        
        // Collect the optimized statistics from the ViewModel
        val commandCountsByIndex by viewModel.commandCountsByIndex.collectAsState()
        val cargoCountsMap by viewModel.cargoCounts.collectAsState()
        val countryStatsMap by viewModel.countryStats.collectAsState()
        val isLoadingCommands by viewModel.isLoadingCommands.collectAsState()
        val isLoadingCargos by viewModel.isLoadingCargos.collectAsState()
        val isLoadingCountries by viewModel.isLoadingCountries.collectAsState()
        
        // Convert Long counts to Int for the UI
        val commandCountsByStepInt = commandCountsByIndex.mapValues { it.value.toInt() }
        
        // Calculate totals for the original CommandCounts type (still needed for non-updated UI components)
        val totalCommands = commandCountsByIndex.values.sumOf { it }.toInt()
        val commandCounts = CommandCounts(
            pending = (commandCountsByIndex[0]?.toInt() ?: 0) + (commandCountsByIndex[1]?.toInt() ?: 0),
            inProgress = (commandCountsByIndex[2]?.toInt() ?: 0) + (commandCountsByIndex[3]?.toInt() ?: 0),
            delivered = (commandCountsByIndex[4]?.toInt() ?: 0) + (commandCountsByIndex[5]?.toInt() ?: 0),
            total = totalCommands
        )
        
        // Pass the raw cargoCountsMap directly
        val cargoCountsByStatus = cargoCountsMap
        
        val countryStatistics = countryStatsMap.values.map { stats ->
            CountryStatistics(
                country = stats.country,
                commandCount = 0, // Not used in UI
                cargoCount = 0, // Not used in UI
                totalAmount = stats.balance
            )
        }

            HomeSelection(
            commandCountsByStep = commandCountsByStepInt,
            cargoCountsByStatus = cargoCountsByStatus,
            countryStats = countryStatistics,
            isLoadingCommands = isLoadingCommands,
            isLoadingCargos = isLoadingCargos,
            isLoadingCountries = isLoadingCountries,
            role = role,
            user = user,
            onNavigateToCountry = onNavigateToCountry,
            onNavigateToCommandScreen = onNavigateToCommandScreen,
            onDeleteCountry = {
                viewModel.deleteCountry(it,
                    onSuccess = {
                        onShowSnackbar(
                            SnackbarState(
                                type = SnackbarType.SUCCESS,
                                message = "Le pays à bien été retiré"
                            )
                        )
                    },
                    onError = { err ->
                        onShowSnackbar(
                            SnackbarState(
                                type = SnackbarType.ERROR,
                                message = err
                            )
                        )
                    }
                )
            },
            onAddCountry = { country ->
                viewModel
                    .addCountry(
                        country,
                        onSuccess = {
                            onShowSnackbar(
                                SnackbarState(
                                    type = SnackbarType.SUCCESS,
                                    message = "Le pays à bien été ajouté"
                                )
                            )
                        },
                        onError = { err ->
                            onShowSnackbar(
                                SnackbarState(
                                    type = SnackbarType.ERROR,
                                    message = err
                                )
                            )
                        }
                    )
            },
            onNavigateToAddImageScreen = onNavigateToAddImageScreen,
            onNavigateToCargoScreen = onNavigateToCargoScreen,
            onShowConfirmationDialog = onShowConfirmationDialog
        )
    }
}

fun NavController.navigateToCountrySelectionScreen(
    navOptions: NavOptionsBuilder.() -> Unit = {
        this.popUpTo(Screen.CountrySelectionScreen.route){ inclusive = false }
    }
) {
    this.navigate(Screen.CountrySelectionScreen.route, navOptions)
}
