package com.tfkcolin.josyandchris.ui.screens.countrylist

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.LoadState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.ui.components.JACCountryItem
import com.tfkcolin.josyandchris.ui.components.LoadingItem
import com.tfkcolin.josyandchris.ui.components.ErrorItem
import com.tfkcolin.josyandchris.ui.components.EmptyStateItem
import com.tfkcolin.josyandchris.ui.components.FullScreenLoadingItem
import com.tfkcolin.josyandchris.ui.components.FullScreenErrorItem
import com.tfkcolin.josyandchris.ui.data.CountryUiFilterState

private fun LazyListScope.handleCountryPagingStates(pager: androidx.paging.compose.LazyPagingItems<CountryData>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    FullScreenLoadingItem("Loading countries...")
                }
            }
            loadState.append is LoadState.Loading -> {
                item {
                    LoadingItem(message = "Loading more countries...")
                }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    FullScreenErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item {
                    EmptyStateItem("No countries found")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CountryListScreen(
    modifier: Modifier = Modifier,
    viewModel: CountryListViewModel = hiltViewModel()
) {
    var uiFilterState by remember { mutableStateOf(CountryUiFilterState()) }

    // Collect paging data
    val pager = viewModel.countriesPagingData.collectAsLazyPagingItems()

    // Update filters when UI state changes
    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Top app bar
        TopAppBar(
            title = { Text("Country List") },
            actions = {
                val isLiveMode by viewModel.isLiveMode.collectAsState()
                Switch(
                    checked = isLiveMode,
                    onCheckedChange = { viewModel.toggleLiveMode() }
                )
                if (isLiveMode) {
                    Text("LIVE", color = MaterialTheme.colorScheme.primary, modifier = Modifier.padding(start = 8.dp))
                }
            }
        )

        // Search bar
        OutlinedTextField(
            value = uiFilterState.searchTerm,
            onValueChange = { uiFilterState = uiFilterState.copy(searchTerm = it) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            placeholder = { Text("Search countries...") },
            singleLine = true
        )

        // Country list with Paging 3
        LazyColumn(
            contentPadding = PaddingValues(vertical = 8.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(pager.itemCount) { index ->
                val country = pager[index]
                country?.let {
                    JACCountryItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp, vertical = 4.dp),
                        country = it
                    )
                }
            }

            // Handle paging states
            handleCountryPagingStates(pager)
        }
    }
}
