package com.tfkcolin.josyandchris.ui.screens.accounting

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.*
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.josyandchris.data.CountryData
import com.tfkcolin.josyandchris.data.FinancialTransaction
import com.tfkcolin.josyandchris.ui.components.dialog.JACAddTransactionDialog
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.data.SnackbarType

private const val countryIdArgs = "countryName"

internal class CountryArgs(val countryName: String) {
    constructor(savedStateHandle: SavedStateHandle) :
            this(checkNotNull(savedStateHandle[countryIdArgs]) as String)
}

fun NavGraphBuilder.accountingScreen(
    countriesData: List<CountryData>,
    showAddTransactionDialog: Boolean,
    onShowAddTransactionDialogChanged: () -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onTitleDescriptionChange: (String) -> Unit
){
    composable(
        route = "${Screen.AccountingScreen.route}/{$countryIdArgs}",
        arguments = listOf(
            navArgument(countryIdArgs) {
                type = NavType.StringType
            }
        )
    ){
        val viewModel = hiltViewModel<AccountingScreenViewModel>()
        val country by remember(countriesData) {
            derivedStateOf { countriesData.first { it.name == viewModel.currentCountryName } }
        }
        var newTransaction by remember { mutableStateOf(FinancialTransaction(country = country.name)) }
        DisposableEffect(Unit){
            onTitleDescriptionChange(country.name)
            onDispose {
                onTitleDescriptionChange("")
            }
        }
        Box {
            AccountingScreen(
                //countryData = country,
            )
            AnimatedVisibility(visible = showAddTransactionDialog) {
                JACAddTransactionDialog(
                    transaction = newTransaction,
                    onTransactionChanged = { newTransaction = it },
                    onDismissRequest = onShowAddTransactionDialogChanged,
                    onAddTransaction = {
                        viewModel.createTransaction(
                            newTransaction,
                            onSuccess = {
                                onShowSnackbar(
                                    SnackbarState(
                                        message = "la transaction a bien été ajouté",
                                        type = SnackbarType.SUCCESS
                                    )
                                )
                                newTransaction = FinancialTransaction(country = country.name)
                            },
                            onError = { err ->
                                onShowSnackbar(
                                    SnackbarState(
                                        message = err,
                                        type = SnackbarType.ERROR
                                    )
                                )
                            }
                        )
                    },
                    devise = country.devise
                )
            }
        }
    }
}

fun NavController.navigateToAccountingScreen(
    countryName: String,
    navOptions: NavOptionsBuilder.() -> Unit = {
        this.popUpTo(Screen.CountrySelectionScreen.route){ inclusive = false }
    }
){
    this.navigate("${Screen.AccountingScreen.route}/$countryName", navOptions)
}