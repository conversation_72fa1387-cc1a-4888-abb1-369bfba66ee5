package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.formatToPrice
import com.tfkcolin.josyandchris.util.lighten

enum class TransactionTypeColor (val color: Color){
    INPUT(Color(19, 107, 19).lighten(.2f)),
    OUTPUT(Color(168, 19, 19).lighten(.2f))
}

@Composable
fun JACTotalPriceDisplay(
    modifier: Modifier = Modifier,
    label: String,
    total: Int,
    devise: String,
    typeColor: TransactionTypeColor = TransactionTypeColor.INPUT
){
    Box(
        modifier = modifier
            .background(
                brush = Brush.sweepGradient(
                    0f to typeColor.color,
                    .5f to typeColor.color.copy(alpha = .8f),
                    1f to typeColor.color),
                shape = RoundedCornerShape(5)
            )
            .border(
                width = 1.dp,
                color = Color.Black,
                shape = RoundedCornerShape(5)
            )
    ) {
        Column(
            modifier = Modifier
                .padding(5.dp)
                .fillMaxWidth()
                .heightIn(min = 130.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Title(
                text = label,
                color = Color.Black.lighten(.2f)
            )
            Text(
                text = total.formatToPrice(),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = devise,
                style = MaterialTheme.typography.titleSmall
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun JACTotalPriceDisplayPreview(){
    JACTheme {
        Row {
            JACTotalPriceDisplay(
                modifier = Modifier
                    .weight(1f)
                    .padding(2.dp),
                label = "Entrées totals",
                total = 1000000,
                devise = "FCFA",
                typeColor = TransactionTypeColor.INPUT
            )
            JACTotalPriceDisplay(
                modifier = Modifier
                    .weight(1f)
                    .padding(2.dp),
                label = "Sorties totals",
                total = 100000,
                devise = "FCFA",
                typeColor = TransactionTypeColor.OUTPUT
            )
        }
    }
}