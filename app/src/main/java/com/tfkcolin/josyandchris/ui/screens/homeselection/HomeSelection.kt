package com.tfkcolin.josyandchris.ui.screens.homeselection

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Create
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.*
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.auth.domain.model.UserRole
import com.tfkcolin.josyandchris.ui.components.JACCargoOverview
import com.tfkcolin.josyandchris.ui.components.JACCommandOverview
import com.tfkcolin.josyandchris.ui.components.JACCountryTransactionsResultItem
import com.tfkcolin.josyandchris.ui.components.JACTopAppBar
import com.tfkcolin.josyandchris.ui.components.modern.ModernCommandOverview
import com.tfkcolin.josyandchris.ui.components.modern.ModernCargoOverview
import com.tfkcolin.josyandchris.ui.components.dialog.ConfirmationState
import com.tfkcolin.josyandchris.ui.components.dialog.JACAddCountryDialog
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay

@Composable
fun HomeSelection(
    modifier: Modifier = Modifier,
    commandCountsByStep: Map<Int, Int>, // Raw counts by command step index
    cargoCountsByStatus: Map<CargoStatus, Int>, // Raw counts by cargo status
    countryStats: List<CountryStatistics>,
    isLoadingCommands: Boolean,
    isLoadingCargos: Boolean,
    isLoadingCountries: Boolean,
    user: String,
    role: String?,
    onNavigateToCountry: (country: CountryData) -> Unit,
    onNavigateToCommandScreen: (commandStepIndex: Int) -> Unit,
    onNavigateToAddImageScreen: () -> Unit,
    onNavigateToCargoScreen: (cargoStatusIndex: Int) -> Unit,
    onAddCountry: (CountryData) -> Unit,
    onDeleteCountry: (CountryData) -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit
){
    var newCountryData by remember { mutableStateOf(CountryData()) }
    var showAddCountryDialog by remember { mutableStateOf(false) }
    var isContentVisible by remember { mutableStateOf(false) }

    val admin = remember(role) { role == UserRole.ADMIN.role }
    val deliverer = remember(role) { role == UserRole.DELIVERER.role }
    val employee = remember(role) { role == UserRole.EMPLOYEE.role }
    
    // Animate content visibility
    LaunchedEffect(Unit) {
        delay(150)
        isContentVisible = true
    }

    AnimatedVisibility(
        visible = isContentVisible,
        enter = fadeIn(animationSpec = tween(800)) + slideInVertically(animationSpec = tween(800)),
        modifier = modifier
    ) {
        if (admin) {
            AdminHomeView(
                user = user,
                commandCountsByStep = commandCountsByStep,
                cargoCountsByStatus = cargoCountsByStatus,
                countryStats = countryStats,
                isLoadingCommands = isLoadingCommands,
                isLoadingCargos = isLoadingCargos,
                isLoadingCountries = isLoadingCountries,
                onNavigateToCommandScreen = onNavigateToCommandScreen,
                onNavigateToCargoScreen = onNavigateToCargoScreen,
                onNavigateToCountry = onNavigateToCountry,
                onNavigateToAddImageScreen = onNavigateToAddImageScreen,
                onDeleteCountry = onDeleteCountry,
                onShowConfirmationDialog = onShowConfirmationDialog,
                showAddCountryDialog = showAddCountryDialog,
                onShowAddCountryDialog = { showAddCountryDialog = true },
                onHideAddCountryDialog = { showAddCountryDialog = false },
                newCountryData = newCountryData,
                onCountryDataChanged = { newCountryData = it },
                onAddCountry = onAddCountry
            )
        } else if (deliverer || employee) {
            UserHomeView(
                user = user,
                isEmployee = employee,
                commandCountsByStep = commandCountsByStep,
                cargoCountsByStatus = cargoCountsByStatus,
                isLoadingCommands = isLoadingCommands,
                isLoadingCargos = isLoadingCargos,
                onNavigateToCommandScreen = onNavigateToCommandScreen,
                onNavigateToCargoScreen = onNavigateToCargoScreen,
                onNavigateToAddImageScreen = onNavigateToAddImageScreen
            )
        } else {
            UnauthorizedView()
        }
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun AdminHomeView(
    user: String,
    commandCountsByStep: Map<Int, Int>,
    cargoCountsByStatus: Map<CargoStatus, Int>,
    countryStats: List<CountryStatistics>,
    isLoadingCommands: Boolean,
    isLoadingCargos: Boolean,
    isLoadingCountries: Boolean,
    onNavigateToCommandScreen: (Int) -> Unit,
    onNavigateToCargoScreen: (Int) -> Unit,
    onNavigateToCountry: (CountryData) -> Unit,
    onNavigateToAddImageScreen: () -> Unit,
    onDeleteCountry: (CountryData) -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit,
    showAddCountryDialog: Boolean,
    onShowAddCountryDialog: () -> Unit,
    onHideAddCountryDialog: () -> Unit,
    newCountryData: CountryData,
    onCountryDataChanged: (CountryData) -> Unit,
    onAddCountry: (CountryData) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    )
                )
            )
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 5.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // User Header
            item {
                UserHeader(user = user)
            }
            
            // Commands Section
            item {
                AnimatedContent(
                    targetState = isLoadingCommands,
                    transitionSpec = {
                        fadeIn(animationSpec = tween(300)) with
                        fadeOut(animationSpec = tween(300))
                    }
                ) { loading ->
                    if (loading) {
                        ShimmerLoadingCard()
                    } else {
                        ModernCommandOverview(
                            commandCountsByStep = commandCountsByStep,
                            onCommandStepClicked = onNavigateToCommandScreen
                        )
                    }
                }
            }
            
            // Cargo Section
            item {
                AnimatedContent(
                    targetState = isLoadingCargos,
                    transitionSpec = {
                        fadeIn(animationSpec = tween(300)) with
                        fadeOut(animationSpec = tween(300))
                    }
                ) { loading ->
                    if (loading) {
                        ShimmerLoadingCard()
                    } else {
                        ModernCargoOverview(
                            cargoCountsByStatus = cargoCountsByStatus,
                            onCargoStatusClicked = onNavigateToCargoScreen
                        )
                    }
                }
            }
            
            // Action Buttons
            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    ModernActionButton(
                        text = "Gérer les cargos",
                        onClick = { onNavigateToCargoScreen(-1) },
                        modifier = Modifier.weight(1f),
                        containerColor = MaterialTheme.colorScheme.secondaryContainer,
                        contentColor = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    
                    ModernActionButton(
                        text = "Ajouter un pays",
                        onClick = onShowAddCountryDialog,
                        icon = Icons.Filled.Add,
                        modifier = Modifier.weight(1f),
                        containerColor = MaterialTheme.colorScheme.primaryContainer,
                        contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
            
            // Countries Section Header
            item {
                SectionHeader(
                    title = "Pays enregistrés",
                    isEmpty = countryStats.isEmpty()
                )
            }
            
            // Countries List
            if (isLoadingCountries) {
                items(3) {
                    ShimmerCountryCard()
                }
            } else {
                itemsIndexed(
                    items = countryStats,
                    key = { _, item -> item.country.id }
                ) { index, stats ->
                    val animationDelay = index * 50
                    AnimatedCountryStatsCard(
                        statistics = stats,
                        animationDelay = animationDelay,
                        onNavigate = { onNavigateToCountry(stats.country) },
                        onDelete = { onDeleteCountry(stats.country) },
                        onShowConfirmationDialog = onShowConfirmationDialog
                    )
                }
            }
            
            // Register Product Button
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    ModernActionButton(
                        text = "Enregistrer un produit",
                        onClick = onNavigateToAddImageScreen,
                        icon = Icons.Filled.Create,
                        modifier = Modifier.fillMaxWidth(),
                        containerColor = MaterialTheme.colorScheme.tertiary,
                        contentColor = MaterialTheme.colorScheme.onTertiary
                    )
                }
            }
            
            // Bottom Spacing
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
        
        // Add Country Dialog
        AnimatedVisibility(
            visible = showAddCountryDialog,
            enter = fadeIn() + scaleIn(initialScale = 0.8f),
            exit = fadeOut() + scaleOut(targetScale = 0.8f)
        ) {
            JACAddCountryDialog(
                onDismissRequest = onHideAddCountryDialog,
                countryData = newCountryData,
                onCountryDataChanged = onCountryDataChanged,
                onAddCountry = {
                    onAddCountry(newCountryData)
                    onHideAddCountryDialog()
                }
            )
        }
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun UserHomeView(
    user: String,
    isEmployee: Boolean,
    commandCountsByStep: Map<Int, Int>,
    cargoCountsByStatus: Map<CargoStatus, Int>,
    isLoadingCommands: Boolean,
    isLoadingCargos: Boolean,
    onNavigateToCommandScreen: (Int) -> Unit,
    onNavigateToCargoScreen: (Int) -> Unit,
    onNavigateToAddImageScreen: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    )
                )
            )
            .padding(5.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        UserHeader(user = user)
        
        AnimatedContent(
            targetState = isLoadingCommands,
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) with
                fadeOut(animationSpec = tween(300))
            }
        ) { loading ->
            if (loading) {
                ShimmerLoadingCard()
            } else {
                ModernCommandOverview(
                    commandCountsByStep = commandCountsByStep,
                    onCommandStepClicked = onNavigateToCommandScreen
                )
            }
        }
        
        AnimatedContent(
            targetState = isLoadingCargos,
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) with
                fadeOut(animationSpec = tween(300))
            }
        ) { loading ->
            if (loading) {
                ShimmerLoadingCard()
            } else {
                ModernCargoOverview(
                    cargoCountsByStatus = cargoCountsByStatus,
                    onCargoStatusClicked = onNavigateToCargoScreen
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        ModernActionButton(
            text = "Gérer les cargos",
            onClick = { onNavigateToCargoScreen(-1) },
            modifier = Modifier.fillMaxWidth(0.8f),
            containerColor = MaterialTheme.colorScheme.primary,
            contentColor = MaterialTheme.colorScheme.onPrimary
        )
        
        if (isEmployee) {
            ModernActionButton(
                text = "Enregistrer un produit",
                onClick = onNavigateToAddImageScreen,
                icon = Icons.Filled.Create,
                modifier = Modifier.fillMaxWidth(0.8f),
                containerColor = MaterialTheme.colorScheme.secondary,
                contentColor = MaterialTheme.colorScheme.onSecondary
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
    }
}

@Composable
private fun UnauthorizedView() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f),
                        MaterialTheme.colorScheme.surface
                    ),
                    radius = 800f
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.85f)
                .animateContentSize(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Lock,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.error
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Accès refusé",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Vous n'avez pas les droits nécessaires pour utiliser cette application.",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// UI Components
@Composable
private fun UserHeader(user: String) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = user.firstOrNull()?.uppercase() ?: "U",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column {
                Text(
                    text = "Bienvenue,",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = user,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun SectionHeader(
    title: String,
    isEmpty: Boolean
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        if (isEmpty) {
            Text(
                text = "Aucun pays enregistré pour le moment",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ModernActionButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    icon: ImageVector? = null,
    containerColor: Color = MaterialTheme.colorScheme.primary,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )
    
    Card(
        onClick = onClick,
        modifier = modifier
            .scale(animatedScale)
            .height(56.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = containerColor),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp,
            pressedElevation = 2.dp
        ),
        interactionSource = interactionSource
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            icon?.let {
                Icon(
                    imageVector = it,
                    contentDescription = null,
                    tint = contentColor,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = text,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = contentColor
            )
        }
    }
}

@Composable
private fun ShimmerLoadingCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(180.dp)
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.1f),
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    )
                )
        )
    }
}

@Composable
private fun ShimmerCountryCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.1f),
                            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    )
                )
        )
    }
}

@Composable
private fun AnimatedCountryStatsCard(
    statistics: CountryStatistics,
    animationDelay: Int,
    onNavigate: () -> Unit,
    onDelete: () -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn() + slideInHorizontally(initialOffsetX = { it / 2 })
    ) {
        CountryStatsCard(
            statistics = statistics,
            onNavigate = onNavigate,
            onDelete = onDelete,
            onShowConfirmationDialog = onShowConfirmationDialog
        )
    }
}

@Composable
private fun CountryStatsCard(
    statistics: CountryStatistics,
    onNavigate: () -> Unit,
    onDelete: () -> Unit,
    onShowConfirmationDialog: (ConfirmationState) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        onClick = onNavigate
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = statistics.country.name,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "${statistics.commandCount} commandes",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = "${statistics.cargoCount} cargaisons",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
                if (statistics.totalAmount > 0) {
                    Text(
                        text = "Total: ${statistics.totalAmount} XAF",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Row {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                    contentDescription = "Naviguer",
                    tint = MaterialTheme.colorScheme.primary
                )
                
                IconButton(
                    onClick = {
                        onShowConfirmationDialog(
                            ConfirmationState(
                                action = onDelete,
                                title = "Supprimer le pays",
                                content = "Voulez vous vraiment supprimer ce pays? Cette action est irreversible et elle ne supprimera pas les transactions qui sont associées à ce pays."
                            )
                        )
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Supprimer",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun HomeSelectionPreview(){
    JACTheme {
        Scaffold(
            topBar = {
                JACTopAppBar(onIconClick = { /*TODO*/ }, isLogIn = true) {}
            }
        ){
            HomeSelection(
                modifier = Modifier
                    .padding(it)
                    .padding(horizontal = 5.dp),
                commandCountsByStep = mapOf(
                    0 to 4,  // RECORD
                    1 to 2,  // BUYING
                    2 to 1,  // RECEIVED
                    3 to 3,  // DELIVERED
                    4 to 0,  // READY
                    5 to 5   // OK
                ),
                cargoCountsByStatus = mapOf(
                    CargoStatus.LOADING to 1,
                    CargoStatus.IN_TRANSIT to 1,
                    CargoStatus.ARRIVED to 1,
                    CargoStatus.UNLOADING to 0,
                    CargoStatus.COMPLETED to 0
                ),
                countryStats = listOf(
                    CountryStatistics(
                        country = CountryData(id = "1", name = "Cameroun", devise = "fcfa"),
                        commandCount = 2,
                        cargoCount = 1,
                        totalAmount = 60000.0
                    ),
                    CountryStatistics(
                        country = CountryData(id = "2", name = "Chine", devise = "yuan"),
                        commandCount = 0,
                        cargoCount = 0,
                        totalAmount = 10000.0
                    ),
                    CountryStatistics(
                        country = CountryData(id = "3", name = "France", devise = "euro"),
                        commandCount = 1,
                        cargoCount = 2,
                        totalAmount = 0.0
                    )
                ),
                isLoadingCommands = false,
                isLoadingCargos = false,
                isLoadingCountries = false,
                onNavigateToCommandScreen = {},
                onNavigateToCountry = {},
                onAddCountry = {},
                onNavigateToAddImageScreen = {},
                onNavigateToCargoScreen = {},
                onDeleteCountry = {},
                role = UserRole.EMPLOYEE.role,
                onShowConfirmationDialog = {},
                user = "Ananonyme"
            )
        }
    }
}