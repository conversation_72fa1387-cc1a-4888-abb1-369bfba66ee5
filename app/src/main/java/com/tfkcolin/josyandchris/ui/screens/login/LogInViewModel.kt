package com.tfkcolin.josyandchris.ui.screens.login

import android.content.Context
import android.content.Intent
import android.content.IntentSender
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import android.util.Patterns
import com.tfkcolin.josyandchris.auth.AuthManager
import com.tfkcolin.josyandchris.auth.domain.model.AuthResult
import kotlinx.coroutines.delay

data class LoginUiState(
    val email: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val passwordVisible: Boolean = false,
    val errorMessage: String? = null,
    val isEmailValid: Boolean = true,
    val isPasswordValid: Boolean = true,
    val rememberMe: Boolean = false,
    val isBiometricAvailable: Boolean = false,
    val isBiometricEnabled: Boolean = false
)

sealed class LoginEvent {
    object NavigateToHome : LoginEvent()
    object NavigateToRegistration : LoginEvent()
    data class ShowError(val message: String) : LoginEvent()
    object PasswordResetEmailSent : LoginEvent()
    object BiometricAuthSuccess : LoginEvent()
    object BiometricAuthFailed : LoginEvent()
}

@HiltViewModel
class LogInViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val authManager: AuthManager
): ViewModel() {
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    private val _loginEvents = MutableStateFlow<LoginEvent?>(null)
    val loginEvents: StateFlow<LoginEvent?> = _loginEvents.asStateFlow()

    init {
        // Check if biometric authentication is available
        checkBiometricAvailability()

        // Load saved email if "remember me" was enabled
        loadSavedCredentials()
    }

    private fun checkBiometricAvailability() {
        viewModelScope.launch {
            val isAvailable = authManager.isBiometricAvailable()
            val isEnabled = authManager.isBiometricEnabled()
            _uiState.update { it.copy(
                isBiometricAvailable = isAvailable,
                isBiometricEnabled = isEnabled
            ) }
        }
    }

    private fun loadSavedCredentials() {
        viewModelScope.launch {
            val (email, rememberMe) = authManager.getSavedCredentials()
            _uiState.update { it.copy(
                email = email,
                rememberMe = rememberMe
            ) }
        }
    }

    fun updateEmail(email: String) {
        _uiState.update { currentState ->
            currentState.copy(
                email = email,
                isEmailValid = isValidEmail(email) || email.isEmpty(),
                errorMessage = null
            )
        }
    }

    fun updatePassword(password: String) {
        _uiState.update { currentState ->
            currentState.copy(
                password = password,
                isPasswordValid = isValidPassword(password) || password.isEmpty(),
                errorMessage = null
            )
        }
    }

    fun toggleRememberMe() {
        _uiState.update { it.copy(rememberMe = !it.rememberMe) }
    }

    fun toggleBiometricEnabled() {
        _uiState.update { it.copy(isBiometricEnabled = !it.isBiometricEnabled) }
    }

    fun togglePasswordVisibility() {
        _uiState.update { currentState ->
            currentState.copy(passwordVisible = !currentState.passwordVisible)
        }
    }

    fun clearEvent() {
        _loginEvents.value = null
    }

    private fun isValidEmail(email: String): Boolean {
        return Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    private fun isValidPassword(password: String): Boolean {
        return password.length >= 6
    }

    fun validateAndSignIn() {
        val email = _uiState.value.email
        val password = _uiState.value.password

        val isEmailValid = isValidEmail(email)
        val isPasswordValid = isValidPassword(password)

        if (!isEmailValid || !isPasswordValid) {
            _uiState.update { currentState ->
                currentState.copy(
                    isEmailValid = isEmailValid,
                    isPasswordValid = isPasswordValid,
                    errorMessage = "Please correct the form errors"
                )
            }
            return
        }

        signInWithEmailPassword()
    }

    private fun signInWithEmailPassword() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, errorMessage = null) }

            // Add a small delay to make the loading animation visible
            delay(800)

            when (val result = authManager.signInWithEmailAndPassword(
                _uiState.value.email,
                _uiState.value.password
            )) {
                is AuthResult.Success -> {
                    // Save credentials if "remember me" is enabled
                    if (_uiState.value.rememberMe) {
                        authManager.saveCredentials(_uiState.value.email, true)
                    }
                    _loginEvents.value = LoginEvent.NavigateToHome
                }
                is AuthResult.Error -> {
                    val errorMessage = mapAuthErrorToUserMessage(result.exception)
                    _uiState.update { it.copy(errorMessage = errorMessage) }
                }
                else -> Unit
            }

            _uiState.update { it.copy(isLoading = false) }
        }
    }
    
    private fun mapAuthErrorToUserMessage(exception: Exception): String {
        return when {
            exception.message?.contains("no user record") == true -> "No account found with this email"
            exception.message?.contains("password is invalid") == true -> "Incorrect password"
            exception.message?.contains("network error") == true -> "Network error. Please check your connection"
            exception.message?.contains("Email cannot be empty") == true -> "Please enter your email"
            exception.message?.contains("Password cannot be empty") == true -> "Please enter your password"
            exception.message?.contains("Invalid email format") == true -> "Please enter a valid email address"
            exception.message?.contains("Password must be at least 6 characters") == true -> "Password must be at least 6 characters"
            else -> exception.message ?: "Authentication failed"
        }
    }


    suspend fun launchOneTapSignIn(): IntentSender? {
        return when (val result = authManager.beginGoogleOneTapSignIn()) {
            is AuthResult.Success -> result.data
            else -> null
        }
    }

    fun onOneTapResult(data: Intent?) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, errorMessage = null) }

            when (val result = authManager.signInWithGoogleOneTap(data)) {
                is AuthResult.Success -> {
                    _loginEvents.value = LoginEvent.NavigateToHome
                }
                is AuthResult.Error -> {
                    val errorMessage = when {
                        result.exception.message?.contains("network error") == true -> "Network error. Please check your connection"
                        else -> result.exception.message ?: "Authentication failed"
                    }
                    _uiState.update { it.copy(errorMessage = errorMessage) }
                }
                else -> Unit
            }

            _uiState.update { it.copy(isLoading = false) }
        }
    }

    fun authenticateWithBiometrics() {
        // This would trigger biometric authentication
        // For now, we'll just simulate success
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            delay(1000) // Simulate biometric authentication
            _uiState.update { it.copy(isLoading = false) }
            _loginEvents.value = LoginEvent.BiometricAuthSuccess
            delay(100)
            _loginEvents.value = LoginEvent.NavigateToHome
        }
    }

    fun sendPasswordResetEmail() {
        val email = _uiState.value.email

        if (!isValidEmail(email)) {
            _uiState.update { it.copy(
                isEmailValid = false,
                errorMessage = "Please enter a valid email to reset your password"
            ) }
            return
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, errorMessage = null) }

            // Add a small delay to make the loading animation visible
            delay(800)

            when (val result = authManager.sendPasswordResetEmail(email)) {
                is AuthResult.Success -> {
                    _loginEvents.value = LoginEvent.PasswordResetEmailSent
                }
                is AuthResult.Error -> {
                    val errorMessage = when {
                        result.exception.message?.contains("no user record") == true -> "No account found with this email"
                        result.exception.message?.contains("network error") == true -> "Network error. Please check your connection"
                        else -> result.exception.message ?: "Failed to send password reset email"
                    }
                    _uiState.update { it.copy(errorMessage = errorMessage) }
                }
                else -> Unit
            }

            _uiState.update { it.copy(isLoading = false) }
        }
    }

    fun navigateToRegistration() {
        _loginEvents.value = LoginEvent.NavigateToRegistration
    }

}