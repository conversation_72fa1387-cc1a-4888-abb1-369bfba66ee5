package com.tfkcolin.josyandchris.ui.theme

import androidx.compose.ui.graphics.Color

val md_theme_light_primary = Color(0xFF315DA8)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFFD8E2FF)
val md_theme_light_onPrimaryContainer = Color(0xFF001A41)
val md_theme_light_secondary = Color(0xFF646100)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFEEE831)
val md_theme_light_onSecondaryContainer = Color(0xFF1E1D00)
val md_theme_light_tertiary = Color(0xFF9B4428)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFFFDBD0)
val md_theme_light_onTertiaryContainer = Color(0xFF3A0A00)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xF9FAFBFF)
val md_theme_light_onBackground = Color(0xFF1B1B1F)
val md_theme_light_surface = Color(0xFFFEFBFF)
val md_theme_light_onSurface = Color(0xFF1B1B1F)
val md_theme_light_surfaceVariant = Color(0xFFE1E2EC)
val md_theme_light_onSurfaceVariant = Color(0xFF44474F)
val md_theme_light_outline = Color(0xFF75777F)
val md_theme_light_inverseOnSurface = Color(0xFFF2F0F4)
val md_theme_light_inverseSurface = Color(0xFF303033)
val md_theme_light_inversePrimary = Color(0xFFADC6FF)
//val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = Color(0xFF315DA8)

val md_theme_dark_primary = Color(0xFFADC6FF)
val md_theme_dark_onPrimary = Color(0xFF002E69)
val md_theme_dark_primaryContainer = Color(0xFF0F448E)
val md_theme_dark_onPrimaryContainer = Color(0xFFD8E2FF)
val md_theme_dark_secondary = Color(0xFFD1CC00)
val md_theme_dark_onSecondary = Color(0xFF333200)
val md_theme_dark_secondaryContainer = Color(0xFF4B4900)
val md_theme_dark_onSecondaryContainer = Color(0xFFEEE831)
val md_theme_dark_tertiary = Color(0xFFFFB59F)
val md_theme_dark_onTertiary = Color(0xFF5E1701)
val md_theme_dark_tertiaryContainer = Color(0xFF7C2D14)
val md_theme_dark_onTertiaryContainer = Color(0xFFFFDBD0)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF1B1B1F)
val md_theme_dark_onBackground = Color(0xFFE3E2E6)
val md_theme_dark_surface = Color(0xFF1B1B1F)
val md_theme_dark_onSurface = Color(0xFFE3E2E6)
val md_theme_dark_surfaceVariant = Color(0xFF44474F)
val md_theme_dark_onSurfaceVariant = Color(0xFFC4C6D0)
val md_theme_dark_outline = Color(0xFF8E9099)
val md_theme_dark_inverseOnSurface = Color(0xFF1B1B1F)
val md_theme_dark_inverseSurface = Color(0xFFE3E2E6)
val md_theme_dark_inversePrimary = Color(0xFF315DA8)
//val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFFADC6FF)


//val seed = Color(0xFF00204D)

// Extended Material 3 color tokens for JAC Express
val success_light = Color(0xFF1B5E20)
val success_dark = Color(0xFF4CAF50)
val warning_light = Color(0xFFE65100)
val warning_dark = Color(0xFFFF9800)
val info_light = Color(0xFF0277BD)
val info_dark = Color(0xFF29B6F6)

// Surface containers for better hierarchy
val surface_container_lowest_light = Color(0xFFFFFFFF)
val surface_container_low_light = Color(0xFFF7F2FA)
val surface_container_light = Color(0xFFF1ECF4)
val surface_container_high_light = Color(0xFFECE6F0)
val surface_container_highest_light = Color(0xFFE6E0E9)

val surface_container_lowest_dark = Color(0xFF0F0D13)
val surface_container_low_dark = Color(0xFF1D1B20)
val surface_container_dark = Color(0xFF211F26)
val surface_container_high_dark = Color(0xFF2B2930)
val surface_container_highest_dark = Color(0xFF36343B)

// Status specific colors
val status_pending = Color(0xFFFFF3C4)
val status_in_progress = Color(0xFFE3F2FD)
val status_completed = Color(0xFFE8F5E8)
val status_cancelled = Color(0xFFFFEBEE)
