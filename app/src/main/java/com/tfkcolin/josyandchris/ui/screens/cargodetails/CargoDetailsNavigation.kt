package com.tfkcolin.josyandchris.ui.screens.cargodetails

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.data.SnackbarState

const val cargoIdArgs = "cargoId"
const val isCreateArgs = "isCreate"

fun NavGraphBuilder.cargoDetailsScreen(
    setHideAppbar: (Boolean) -> Unit,
    role: String?,
    trySuspendFunction: suspend (succeedMessage: String?, suspend () -> Unit) -> Unit,
    onShowSnackbar: (SnackbarState) -> Unit,
    onNavigateToShipmentDetailsScreen: (String, String) -> Unit,
    onTitleDescriptionChange: (String) -> Unit
) {
    composable(
        route = "${Screen.CargoDetailsScreen.route}?$cargoIdArgs={$cargoIdArgs}&$isCreateArgs={$isCreateArgs}",
        arguments = listOf(
            navArgument(cargoIdArgs) {
                nullable = true
                defaultValue = null
                type = NavType.StringType
            },
            navArgument(isCreateArgs) {
                defaultValue = true
                type = NavType.BoolType
            }
        )
    ) {
        CargoDetailsScreen(
            setHideAppbar = setHideAppbar,
            role = role,
            trySuspendFunction = trySuspendFunction,
            onShowSnackbar = onShowSnackbar,
            onNavigateToShipmentDetailsScreen = onNavigateToShipmentDetailsScreen,
            onTitleDescriptionChange = onTitleDescriptionChange
        )
    }
}

fun NavController.navigateToCargoDetailsScreen(
    id: String? = null,
    isCreate: Boolean = true,
    navOptions: NavOptionsBuilder.() -> Unit = {}
) {
    this.navigate(
        "${Screen.CargoDetailsScreen.route}?$cargoIdArgs=$id&$isCreateArgs=$isCreate",
        navOptions
    )
}
