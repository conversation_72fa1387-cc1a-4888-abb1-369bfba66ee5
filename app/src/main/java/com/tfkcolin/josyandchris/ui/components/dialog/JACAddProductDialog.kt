package com.tfkcolin.josyandchris.ui.components.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.josyandchris.data.ImageData
import com.tfkcolin.josyandchris.data.MiniProduct
import com.tfkcolin.josyandchris.ui.components.JACImage
import com.tfkcolin.josyandchris.ui.components.foundation.JACSelect
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun JACAddProductDialog(
    modifier: Modifier = Modifier,
    product: MiniProduct,
    validateText: String = "Ajouter",
    images: List<ImageData>,
    onProductChanged: (MiniProduct) -> Unit,
    onAddImageData: () -> Unit,
    onDismissRequest: () -> Unit,
    onValidate: () -> Unit
) {
    // Category and genre filtering removed - these should be handled by repository
    val categories = listOf("tous")
    val genres = listOf("tous")
    
    var genreExpanded by remember { mutableStateOf(false) }
    var categoryExpanded by remember { mutableStateOf(false) }

    var selectedCategory by remember { mutableStateOf("tous") }
    var selectedGenre by remember { mutableStateOf("tous") }

    // Client-side filtering removed - filtering should be handled by repository
    val selectedImages = images

    val nameFocus = remember { FocusRequester() }
    val quantityFocus = remember { FocusRequester() }
    val descriptionFocus = remember { FocusRequester() }

    LaunchedEffect(Unit){ nameFocus.requestFocus() }
    val keyboard = LocalSoftwareKeyboardController.current

    Dialog(onDismissRequest = onDismissRequest){
        ElevatedCard(modifier = modifier) {
            BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
                Column(
                    modifier = Modifier
                        .verticalScroll(state = rememberScrollState())
                        .padding(2.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier.padding(2.dp),
                        text = "Creer un Produit",
                        style = MaterialTheme.typography.headlineMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        modifier = Modifier.padding(top = 2.dp , bottom = 5.dp),
                        text = "Entrer les informations du produit",
                        style = MaterialTheme.typography.labelMedium
                    )
                    OutlinedTextField(
                        modifier = Modifier
                            .focusRequester(nameFocus)
                            .padding(bottom = 4.dp),
                        value = product.name,
                        onValueChange = { onProductChanged(product.copy(name = it)) },
                        label = {
                            Text(text = "Nom")
                        },
                        keyboardActions = KeyboardActions(
                            onNext = { quantityFocus.requestFocus() }
                        ),
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Next,
                            capitalization = KeyboardCapitalization.Sentences
                        )
                    )
                    OutlinedTextField(
                        modifier = Modifier
                            .focusRequester(quantityFocus)
                            .padding(bottom = 4.dp),
                        value = if(product.quantity == 0) "" else "${product.quantity}",
                        onValueChange = {
                            onProductChanged(product.copy(quantity = it.toIntOrNull() ?: 0))
                        },
                        label = {
                            Text(text = "Quantité")
                        },
                        keyboardActions = KeyboardActions(
                            onNext = { descriptionFocus.requestFocus() }
                        ),
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Next,
                            keyboardType = KeyboardType.Number
                        )
                    )
                    OutlinedTextField(
                        modifier = Modifier
                            .padding(bottom = 4.dp)
                            .focusRequester(descriptionFocus)
                            .heightIn(min = TextFieldDefaults.MinHeight.times(2)),
                        value = product.description,
                        onValueChange = {
                            onProductChanged(product.copy(description = it))
                        },
                        label = {
                            Text(text = "Description")
                        },
                        keyboardActions = KeyboardActions(
                            onDone = { keyboard?.hide() }
                        ),
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Done,
                            capitalization = KeyboardCapitalization.Sentences
                        )
                    )
                    Text(
                        modifier = Modifier.padding(5.dp),
                        text = " S'électionner l'image du produit",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.tertiary
                    )
                    Row(horizontalArrangement = Arrangement.Center) {
                        JACSelect(
                            modifier = Modifier.weight(1f),
                            selected = selectedCategory,
                            expanded = categoryExpanded,
                            onExpandedChange = { categoryExpanded = it }
                        ) {
                            categories.forEach {
                                DropdownMenuItem(
                                    text = {
                                        Text(text = it)
                                    },
                                    onClick = {
                                        selectedCategory = it
                                        categoryExpanded = false
                                    }
                                )
                            }
                        }
                        JACSelect(
                            modifier = Modifier.weight(1f),
                            selected = selectedGenre,
                            expanded = genreExpanded,
                            onExpandedChange = { genreExpanded = it }
                        ) {
                            genres.forEach {
                                DropdownMenuItem(
                                    text = {
                                        Text(text = it)
                                    },
                                    onClick = {
                                        selectedGenre = it
                                        genreExpanded = false
                                    }
                                )
                            }
                        }
                    }
                    OutlinedButton(
                        onClick = onAddImageData,
                        shape = RoundedCornerShape(10)
                    ) {
                        Icon(
                            modifier = Modifier.padding(end = 5.dp),
                            imageVector = Icons.Default.Add,
                            contentDescription = null
                        )
                        Text(text = "Ajouter une image")
                    }
                    if (selectedImages.isEmpty()) {
                        Box(modifier = Modifier.heightIn(min = 300.dp)) {
                            Text(
                                modifier = Modifier.align(Alignment.Center),
                                text = "Aucun produit trouvée",
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }
                    else {
                        val columnCount = 2
                        LazyVerticalGrid(
                            modifier = Modifier
                                .height(
                                    <EMAIL>,
                                ),
                            columns = GridCells.Fixed(if(selectedImages.size > 1) columnCount else 1)
                        ) {
                            items(selectedImages){image ->
                                ImageDataItem(
                                    modifier = Modifier
                                        .padding(3.dp)
                                        .clickable {
                                            onProductChanged(product.copy(productImage = image.url))
                                        },
                                    image = image,
                                    selected = image.url == product.productImage
                                )
                            }
                        }
                        Box(modifier = Modifier.height(ButtonDefaults.MinHeight.plus(10.dp)))
                    }
                }
                Surface(
                    modifier = Modifier.align(Alignment.BottomStart),
                    color = MaterialTheme.colorScheme.background
                ){
                    Row {
                        TextButton(
                            modifier = Modifier.weight(1f),
                            onClick = onDismissRequest
                        ) {
                            Text(text = "Annuler")
                        }
                        TextButton(
                            modifier = Modifier.weight(1f),
                            onClick = onValidate
                        ) {
                            Text(text = validateText)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ImageDataItem(
    modifier: Modifier = Modifier,
    image: ImageData,
    selected: Boolean = false
) {
    OutlinedCard(
        modifier = modifier
            .widthIn(max = 300.dp)
            .padding(2.dp),
        border = BorderStroke(
            width = 1.dp,
            color = if(selected) MaterialTheme.colorScheme.secondary
            else MaterialTheme.colorScheme.primary
        ),
        elevation = CardDefaults.outlinedCardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(vertical = 5.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                modifier = Modifier.padding(2.dp),
                text = "Ajouter le: ${
                    SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(
                        image.created
                    )
                }",
                style = MaterialTheme.typography.labelSmall,
                maxLines = 1,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .5f),
                overflow = TextOverflow.Ellipsis
            )
            JACImage(
                modifier = Modifier.height(200.dp),
                data = image.url
            )
            if (image.category.isNotEmpty() || image.genre.isNotEmpty()) {
                Text(
                    modifier = Modifier.padding(2.dp),
                    text = "${image.category}${if (image.genre.isNotEmpty()) "," else ""}${" " + image.genre}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun JACAddProductDialogPreview(){
    JACTheme {
        JACAddProductDialog(
            product = MiniProduct(name = "sac à main", quantity = 100),
            onProductChanged = {},
            images = listOf(
                ImageData(
                    id = "0",
                    category = "sac",
                    genre = "femme"
                ),
                ImageData(
                    id = "2",
                    category = "montre",
                    genre = "homme"
                )
            ),
            onAddImageData = {},
            onDismissRequest = {},
            onValidate = {}
        )
    }
}