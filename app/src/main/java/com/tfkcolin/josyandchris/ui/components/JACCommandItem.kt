package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.ClientData
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.MiniProduct
import com.tfkcolin.josyandchris.ui.components.foundation.JACAvatar
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import java.text.DateFormat

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACCommandItem(
    modifier: Modifier = Modifier,
    command: Command,
    onClick: () -> Unit
){
    val created = remember {
        val format = DateFormat.getInstance()
        format.format(command.created)
    }
    Box(modifier = modifier){
        OutlinedCard(
            modifier = Modifier
                .fillMaxSize(),
            onClick = onClick,
            shape = RoundedCornerShape(5),
            border = BorderStroke(
                width = 1.dp,
                color = MaterialTheme.colorScheme.primary
            ),
            elevation = CardDefaults.outlinedCardElevation(
                defaultElevation = 1.dp
            )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp) // Increased internal padding
                ) {
                    Row(
                        modifier = Modifier.padding(bottom = 8.dp), // Increased bottom padding
                        horizontalArrangement = Arrangement.SpaceBetween, // Distribute space
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = command.client.name,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = created,
                        style = MaterialTheme.typography.labelMedium,
                        textAlign = TextAlign.End,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .7f) // Increased alpha for better readability
                    )
                }
                Row(
                    modifier = Modifier.padding(bottom = 8.dp), // Increased bottom padding
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${command.client.tel}, ${command.client.city}, ${command.client.country}",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .7f) // Increased alpha for better readability
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Surface(
                        color = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f), // Slightly reduced alpha for less visual interference
                        shape = MaterialTheme.shapes.small // Added shape for better visual
                    ) {
                        Text(
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp), // Adjusted padding
                            text = CommandStep.values()[command.commandStepIndex].step,
                            style = MaterialTheme.typography.titleSmall, // Changed to titleSmall for better fit
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                Row(
                    modifier = Modifier.padding(bottom = 8.dp), // Increased bottom padding
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    command.products.filterIndexed{ i, _ -> i < 3 }.forEach { product ->
                        JACAvatar(
                            modifier = Modifier.size(40.dp), // Slightly reduced size for better fit
                            image = product.productImage
                        )
                        Spacer(modifier = Modifier.width(4.dp)) // Added spacing between avatars
                    }
                    if(command.products.size > 3){
                        Text(
                            modifier = Modifier.padding(start = 6.dp), // Adjusted padding
                            text = "...",
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                Row(modifier = Modifier.padding(bottom = 4.dp)) { // Added bottom padding
                    Text(
                        modifier = Modifier.weight(1f),
                        text = if(command.paymentProofImageUrl == null) "preuve de paiement indisponible"
                        else "Preuve de paiement disponible",
                        color = if(command.paymentProofImageUrl == null) MaterialTheme.colorScheme.error // Using theme error color
                        else MaterialTheme.colorScheme.tertiary, // Using theme tertiary color for success
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        style = MaterialTheme.typography.labelSmall
                    )
                    Text(
                        modifier = Modifier.weight(1f),
                        text = if(command.proofUploaded) "Uploaded" // Capitalized for consistency
                        else "Not uploaded",
                        color = if(command.proofUploaded) MaterialTheme.colorScheme.tertiary // Using theme tertiary color for success
                        else MaterialTheme.colorScheme.error, // Using theme error color
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        style = MaterialTheme.typography.labelSmall,
                        textAlign = TextAlign.End
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun JACCommandItemPreview(){
    JACTheme {
        JACCommandItem(
            command = Command(
                client = ClientData(name = "Tambo", tel = "656728667", country = "Cameroun", city = "yaoundé"),
                products = listOf(
                    MiniProduct(name = "Sac", productImage = ""),
                    MiniProduct(name = "Pantallon", productImage = "")
                )
            ),
            onClick = {}
        )
    }
}
