package com.tfkcolin.josyandchris.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.ShipmentProduct
import com.tfkcolin.josyandchris.data.ShipmentStatus
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import java.text.DateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JACShipmentItem(
    modifier: Modifier = Modifier,
    shipment: Shipment,
    onClick: () -> Unit
) {
    val created = remember {
        val format = DateFormat.getInstance()
        format.format(shipment.created)
    }
    
    Box(modifier = modifier) {
        OutlinedCard(
            modifier = Modifier.fillMaxSize(),
            onClick = onClick,
            shape = RoundedCornerShape(5),
            border = BorderStroke(
                width = 1.dp,
                color = MaterialTheme.colorScheme.primary
            ),
            elevation = CardDefaults.outlinedCardElevation(
                defaultElevation = 1.dp
            )
        ) {
            Column(
                modifier = Modifier.padding(4.dp)
            ) {
                Row(
                    modifier = Modifier.padding(bottom = 4.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = shipment.clientName,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        modifier = Modifier.weight(1f),
                        text = created,
                        style = MaterialTheme.typography.labelMedium,
                        textAlign = TextAlign.End,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .5f)
                    )
                }
                Row(
                    modifier = Modifier.padding(bottom = 2.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Tel: ${shipment.clientPhone}",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = .5f)
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Surface(color = MaterialTheme.colorScheme.primaryContainer) {
                        Text(
                            modifier = Modifier.padding(2.dp),
                            text = ShipmentStatus.values()[shipment.statusIndex].status,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                
                // Display weight and volume
                Row(
                    modifier = Modifier.padding(top = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Weight: ${shipment.weightKg} KG",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = "Volume: ${shipment.volumeCbm} CBM",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.weight(1f)
                    )
                }
                
                // Display amount due
                Text(
                    text = "Amount Due: ${shipment.amountDue}",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                // Display products if any
                if (shipment.products.isNotEmpty()) {
                    Text(
                        text = "Products: ${shipment.products.size}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun JACShipmentItemPreview() {
    JACTheme {
        JACShipmentItem(
            shipment = Shipment(
                clientName = "John Doe",
                clientPhone = "+237 656 123 456",
                weightKg = 25.5,
                volumeCbm = 0.75,
                amountDue = 150.0,
                statusIndex = ShipmentStatus.IN_CARGO.ordinal,
                products = listOf(
                    ShipmentProduct(
                        name = "Laptop",
                        quantity = 1,
                        description = "MacBook Pro 16\""
                    ),
                    ShipmentProduct(
                        name = "Smartphone",
                        quantity = 2,
                        description = "iPhone 15 Pro"
                    )
                )
            ),
            onClick = {}
        )
    }
}
