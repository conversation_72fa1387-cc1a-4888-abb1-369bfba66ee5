package com.tfkcolin.josyandchris.ui.screens.accounting

import android.os.Build
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.Scaffold
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.LoadState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.josyandchris.data.*
import com.tfkcolin.josyandchris.logic.isMonth
import com.tfkcolin.josyandchris.logic.isYear
import com.tfkcolin.josyandchris.logic.toLocalDate
import com.tfkcolin.josyandchris.ui.components.*
import com.tfkcolin.josyandchris.ui.components.foundation.JACSelect
import com.tfkcolin.josyandchris.ui.data.TransactionUiFilterState
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import com.tfkcolin.josyandchris.util.lighten
import java.util.Calendar
import java.util.Date

private fun LazyListScope.handleTransactionPagingStates(pager: androidx.paging.compose.LazyPagingItems<FinancialTransaction>) {
    pager.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                item {
                    FullScreenLoadingItem("Loading transactions...")
                }
            }
            loadState.append is LoadState.Loading -> {
                item {
                    LoadingItem(message = "Loading more transactions...")
                }
            }
            loadState.refresh is LoadState.Error -> {
                val error = loadState.refresh as LoadState.Error
                item {
                    FullScreenErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.append is LoadState.Error -> {
                val error = loadState.append as LoadState.Error
                item {
                    ErrorItem(
                        message = error.error.localizedMessage ?: "Unknown error occurred",
                        onRetry = { retry() }
                    )
                }
            }
            loadState.refresh is LoadState.NotLoading && itemCount == 0 -> {
                item {
                    EmptyStateItem("No transactions found")
                }
            }
        }
    }
}

@Composable
fun AccountingScreen(
    //countryData: CountryData,
    modifier: Modifier = Modifier,
    viewModel: AccountingScreenViewModel = hiltViewModel(),
){
    val months = listOf(
        "Janvier", "Février", "Mars",
        "Avril", "Mai", "Juin",
        "Juillet", "Aout", "Septembre",
        "Octobre", "Novembre", "Decembre"
    )
    
    val years = listOf(2020, 2021, 2022, 2023, 2024) // Example years
    
    var uiFilterState by remember { mutableStateOf(TransactionUiFilterState()) }
    var monthExpanded by remember { mutableStateOf(false) }
    var yearExpanded by remember { mutableStateOf(false) }
    
    // Collect the paginated transactions as LazyPagingItems
    val paginatedTransactions = viewModel.transactionsPagingData.collectAsLazyPagingItems()
    
    // Collect totals from ViewModel StateFlow
    val inputTotal by viewModel.inputTotal.collectAsState()
    val outputTotal by viewModel.outputTotal.collectAsState()
    
    // Update filters when UI state changes
    LaunchedEffect(uiFilterState) {
        viewModel.updateUiFilterState(uiFilterState)
    }
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row {
            JACSelect(
                modifier = Modifier.weight(1f),
                selected = if(uiFilterState.selectedMonth != null) months[uiFilterState.selectedMonth!!] else "tous",
                expanded = monthExpanded,
                onExpandedChange = { monthExpanded = it }
            ) {
                DropdownMenuItem(
                    text = {
                        Text(text = "tous")
                    },
                    onClick = {
                        uiFilterState = uiFilterState.copy(selectedMonth = null)
                        monthExpanded = false
                    }
                )
                months.forEachIndexed { index, month ->
                    DropdownMenuItem(
                        text = {
                            Text(text = month)
                        },
                        onClick = {
                            uiFilterState = uiFilterState.copy(selectedMonth = index)
                            monthExpanded = false
                        }
                    )
                }
            }
            JACSelect(
                modifier = Modifier.weight(1f),
                selected = if(uiFilterState.selectedYear != null) "${uiFilterState.selectedYear}" else "tous",
                expanded = yearExpanded,
                onExpandedChange = { yearExpanded = it }
            ) {
                DropdownMenuItem(
                    text = {
                        Text(text = "tous")
                    },
                    onClick = {
                        uiFilterState = uiFilterState.copy(selectedYear = null)
                        yearExpanded = false
                    }
                )
                years.forEach { year ->
                    DropdownMenuItem(
                        text = {
                            Text(text = "$year")
                        },
                        onClick = {
                            uiFilterState = uiFilterState.copy(selectedYear = year)
                            yearExpanded = false
                        }
                    )
                }
            }
        }
        // Total calculations properly handled through ViewModel StateFlow for unidirectional data flow
        Row {
            JACTotalPriceDisplay(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 10.dp),
                label = "Entrées totals",
                total = inputTotal.toInt(),
                devise = "FCFA",
                typeColor = TransactionTypeColor.INPUT
            )
            JACTotalPriceDisplay(
                modifier = Modifier
                    .weight(1f),
                label = "Sorties totals",
                total = outputTotal.toInt(),
                devise = "FCFA",
                typeColor = TransactionTypeColor.OUTPUT
            )
        }
        Title(
            text = "Transaction du mois",
            color = Color.Black.lighten(.25f),
        )
        LazyColumn {
            items(paginatedTransactions.itemCount) { index ->
                val transaction = paginatedTransactions[index]
                transaction?.let {
                    JACTransactionItem(
                        modifier = Modifier.padding(top = 5.dp),
                        transaction = it,
                        onClick = {}
                    )
                }
            }
            
            // Handle paging states
            handleTransactionPagingStates(paginatedTransactions)
            
            item {
                Box(modifier = Modifier.padding(bottom = ButtonDefaults.MinHeight))
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AccountingScreenPreview(){
    JACTheme {
        Scaffold(
            topBar = {
                JACTopAppBar(onIconClick = { /*TODO*/ }, isLogIn = true) {}
            }
        ) {
            AccountingScreen(
                modifier = Modifier
                    .padding(start = 5.dp, end = 5.dp)
                    .padding(it)
                //countryData = CountryData(name = "Cameroun", devise = "fcfa"),
            )
        }
    }
}