package com.tfkcolin.josyandchris.ui.states

/**
 * Represents the state of an image operation (upload, delete, replace)
 */
sealed class ImageOperationState {
    object Idle : ImageOperationState()
    object Loading : ImageOperationState()
    data class Success(val message: String? = null) : ImageOperationState()
    data class Error(val exception: Throwable, val message: String? = null) : ImageOperationState()
}

/**
 * Represents the overall UI state for image screens
 */
data class ImageScreenUiState(
    val imageOperationState: ImageOperationState = ImageOperationState.Idle,
    val isNetworkAvailable: Boolean = true,
    val showRetrySnackbar: Boolean = false,
    val uploadingImageId: String? = null
)
