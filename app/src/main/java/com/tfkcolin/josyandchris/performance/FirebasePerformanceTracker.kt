package com.tfkcolin.josyandchris.performance

import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.perf.metrics.Trace
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase Performance tracking utilities for monitoring app performance
 * Tracks cold starts, warm starts, and critical user operations
 */
@Singleton
class FirebasePerformanceTracker @Inject constructor() {
    
    private val firebasePerformance = FirebasePerformance.getInstance()
    private val activeTraces = mutableMapOf<String, Trace>()
    
    companion object {
        // App startup traces
        const val TRACE_APP_COLD_START = "app_cold_start"
        const val TRACE_APP_WARM_START = "app_warm_start"
        const val TRACE_APP_SPLASH_SCREEN = "splash_screen_duration"
        
        // Screen loading traces
        const val TRACE_HOME_SCREEN_LOAD = "home_screen_load"
        const val TRACE_COMMAND_LIST_LOAD = "command_list_load"
        const val TRACE_CARGO_LIST_LOAD = "cargo_list_load"
        const val TRACE_SHIPMENT_LIST_LOAD = "shipment_list_load"
        
        // Search performance traces
        const val TRACE_SEARCH_QUERY = "search_query_performance"
        const val TRACE_AUTOCOMPLETE_LOAD = "autocomplete_suggestions_load"
        
        // Database operation traces
        const val TRACE_CACHE_QUERY = "cache_database_query"
        const val TRACE_FIRESTORE_QUERY = "firestore_query_performance"
        
        // User interaction traces
        const val TRACE_FILTER_APPLY = "filter_application"
        const val TRACE_PAGINATION_LOAD = "pagination_next_page"
        
        // Custom metrics
        const val METRIC_SEARCH_RESULTS_COUNT = "search_results_count"
        const val METRIC_CACHE_HIT_RATE = "cache_hit_rate"
        const val METRIC_RECOMPOSITION_COUNT = "recomposition_count"
    }
    
    /**
     * Start tracking a performance trace
     */
    fun startTrace(traceName: String): Trace? {
        return try {
            val trace = firebasePerformance.newTrace(traceName)
            trace.start()
            activeTraces[traceName] = trace
            Timber.d("Started Firebase performance trace: $traceName")
            trace
        } catch (e: Exception) {
            Timber.e(e, "Failed to start trace: $traceName")
            null
        }
    }
    
    /**
     * Stop tracking a performance trace
     */
    fun stopTrace(traceName: String) {
        try {
            activeTraces[traceName]?.let { trace ->
                trace.stop()
                activeTraces.remove(traceName)
                Timber.d("Stopped Firebase performance trace: $traceName")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop trace: $traceName")
        }
    }
    
    /**
     * Add a custom metric to an active trace
     */
    fun putMetric(traceName: String, metricName: String, value: Long) {
        try {
            activeTraces[traceName]?.putMetric(metricName, value)
        } catch (e: Exception) {
            Timber.e(e, "Failed to add metric $metricName to trace $traceName")
        }
    }
    
    /**
     * Increment a metric in an active trace
     */
    fun incrementMetric(traceName: String, metricName: String, incrementBy: Long = 1L) {
        try {
            activeTraces[traceName]?.incrementMetric(metricName, incrementBy)
        } catch (e: Exception) {
            Timber.e(e, "Failed to increment metric $metricName in trace $traceName")
        }
    }
    
    /**
     * Add custom attributes to a trace
     */
    fun putAttribute(traceName: String, attribute: String, value: String) {
        try {
            activeTraces[traceName]?.putAttribute(attribute, value)
        } catch (e: Exception) {
            Timber.e(e, "Failed to add attribute $attribute to trace $traceName")
        }
    }
    
    /**
     * Track app cold start performance
     */
    fun trackColdStart() {
        startTrace(TRACE_APP_COLD_START)
        putAttribute(TRACE_APP_COLD_START, "start_type", "cold")
    }
    
    /**
     * Track app warm start performance
     */
    fun trackWarmStart() {
        startTrace(TRACE_APP_WARM_START)
        putAttribute(TRACE_APP_WARM_START, "start_type", "warm")
    }
    
    /**
     * Complete cold start tracking
     */
    fun completeColdStart() {
        stopTrace(TRACE_APP_COLD_START)
    }
    
    /**
     * Complete warm start tracking
     */
    fun completeWarmStart() {
        stopTrace(TRACE_APP_WARM_START)
    }
    
    /**
     * Track splash screen duration
     */
    fun trackSplashScreen() {
        startTrace(TRACE_APP_SPLASH_SCREEN)
    }
    
    /**
     * Complete splash screen tracking
     */
    fun completeSplashScreen() {
        stopTrace(TRACE_APP_SPLASH_SCREEN)
    }
    
    /**
     * Track screen loading performance
     */
    fun trackScreenLoad(screenName: String) {
        val traceName = "${screenName}_load"
        startTrace(traceName)
        putAttribute(traceName, "screen_name", screenName)
    }
    
    /**
     * Complete screen loading tracking
     */
    fun completeScreenLoad(screenName: String, itemCount: Int = 0) {
        val traceName = "${screenName}_load"
        if (itemCount > 0) {
            putMetric(traceName, "items_loaded", itemCount.toLong())
        }
        stopTrace(traceName)
    }
    
    /**
     * Track search performance
     */
    fun trackSearchPerformance(query: String, searchType: String) {
        startTrace(TRACE_SEARCH_QUERY)
        putAttribute(TRACE_SEARCH_QUERY, "search_type", searchType)
        putAttribute(TRACE_SEARCH_QUERY, "query_length", query.length.toString())
        putAttribute(TRACE_SEARCH_QUERY, "is_cached", "false") // Will be updated if cache hit
    }
    
    /**
     * Complete search tracking with results
     */
    fun completeSearchPerformance(resultCount: Int, fromCache: Boolean = false) {
        putMetric(TRACE_SEARCH_QUERY, METRIC_SEARCH_RESULTS_COUNT, resultCount.toLong())
        putAttribute(TRACE_SEARCH_QUERY, "is_cached", fromCache.toString())
        stopTrace(TRACE_SEARCH_QUERY)
    }
    
    /**
     * Track autocomplete performance
     */
    fun trackAutocompleteLoad() {
        startTrace(TRACE_AUTOCOMPLETE_LOAD)
    }
    
    /**
     * Complete autocomplete tracking
     */
    fun completeAutocompleteLoad(suggestionCount: Int, fromCache: Boolean = false) {
        putMetric(TRACE_AUTOCOMPLETE_LOAD, "suggestion_count", suggestionCount.toLong())
        putAttribute(TRACE_AUTOCOMPLETE_LOAD, "from_cache", fromCache.toString())
        stopTrace(TRACE_AUTOCOMPLETE_LOAD)
    }
    
    /**
     * Track cache performance
     */
    fun trackCacheQuery(queryType: String) {
        startTrace(TRACE_CACHE_QUERY)
        putAttribute(TRACE_CACHE_QUERY, "query_type", queryType)
    }
    
    /**
     * Complete cache query tracking
     */
    fun completeCacheQuery(hitCount: Int, missCount: Int) {
        val totalQueries = hitCount + missCount
        val hitRate = if (totalQueries > 0) ((hitCount.toDouble() / totalQueries) * 100).toInt() else 0
        
        putMetric(TRACE_CACHE_QUERY, "cache_hits", hitCount.toLong())
        putMetric(TRACE_CACHE_QUERY, "cache_misses", missCount.toLong())
        putMetric(TRACE_CACHE_QUERY, METRIC_CACHE_HIT_RATE, hitRate.toLong())
        stopTrace(TRACE_CACHE_QUERY)
    }
    
    /**
     * Track Firestore query performance
     */
    fun trackFirestoreQuery(collection: String, queryType: String) {
        startTrace(TRACE_FIRESTORE_QUERY)
        putAttribute(TRACE_FIRESTORE_QUERY, "collection", collection)
        putAttribute(TRACE_FIRESTORE_QUERY, "query_type", queryType)
    }
    
    /**
     * Complete Firestore query tracking
     */
    fun completeFirestoreQuery(documentCount: Int) {
        putMetric(TRACE_FIRESTORE_QUERY, "document_count", documentCount.toLong())
        stopTrace(TRACE_FIRESTORE_QUERY)
    }
    
    /**
     * Track filter application performance
     */
    fun trackFilterApplication(filterType: String) {
        startTrace(TRACE_FILTER_APPLY)
        putAttribute(TRACE_FILTER_APPLY, "filter_type", filterType)
    }
    
    /**
     * Complete filter application tracking
     */
    fun completeFilterApplication(resultCount: Int) {
        putMetric(TRACE_FILTER_APPLY, "filtered_results", resultCount.toLong())
        stopTrace(TRACE_FILTER_APPLY)
    }
    
    /**
     * Track recomposition performance
     */
    fun trackRecomposition(composableName: String, recompositionCount: Int) {
        if (recompositionCount > 5) { // Only track if excessive recomposition
            val traceName = "recomposition_$composableName"
            if (!activeTraces.containsKey(traceName)) {
                startTrace(traceName)
                putAttribute(traceName, "composable_name", composableName)
            }
            putMetric(traceName, METRIC_RECOMPOSITION_COUNT, recompositionCount.toLong())
        }
    }
    
    /**
     * Cleanup all active traces (call in onDestroy)
     */
    fun cleanup() {
        activeTraces.keys.toList().forEach { traceName ->
            stopTrace(traceName)
        }
        activeTraces.clear()
    }
}

/**
 * Extension functions for easier performance tracking
 */
inline fun <T> FirebasePerformanceTracker.trackOperation(
    traceName: String,
    attributes: Map<String, String> = emptyMap(),
    operation: () -> T
): T {
    startTrace(traceName)
    attributes.forEach { (key, value) ->
        putAttribute(traceName, key, value)
    }
    
    return try {
        operation()
    } finally {
        stopTrace(traceName)
    }
}

suspend inline fun <T> FirebasePerformanceTracker.trackSuspendOperation(
    traceName: String,
    attributes: Map<String, String> = emptyMap(),
    crossinline operation: suspend () -> T
): T {
    startTrace(traceName)
    attributes.forEach { (key, value) ->
        putAttribute(traceName, key, value)
    }
    
    return try {
        operation()
    } finally {
        stopTrace(traceName)
    }
}
