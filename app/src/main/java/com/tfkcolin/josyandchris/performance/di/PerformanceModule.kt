package com.tfkcolin.josyandchris.performance.di

import com.tfkcolin.josyandchris.performance.FirebasePerformanceTracker
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing performance tracking dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object PerformanceModule {
    
    /**
     * Provides the Firebase performance tracker singleton
     */
    @Provides
    @Singleton
    fun provideFirebasePerformanceTracker(): FirebasePerformanceTracker {
        return FirebasePerformanceTracker()
    }
}
