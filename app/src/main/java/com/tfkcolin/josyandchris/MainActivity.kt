package com.tfkcolin.josyandchris

import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.view.WindowCompat
import com.tfkcolin.josyandchris.performance.FirebasePerformanceTracker
import com.tfkcolin.josyandchris.ui.screens.App
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var performanceTracker: FirebasePerformanceTracker
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Track cold/warm start based on savedInstanceState
        if (savedInstanceState == null) {
            performanceTracker.trackColdStart()
        } else {
            performanceTracker.trackWarmStart()
        }
        
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        setContent {
            JACTheme {
                App()
            }
        }
        
        // Complete startup tracking
        if (savedInstanceState == null) {
            performanceTracker.completeColdStart()
        } else {
            performanceTracker.completeWarmStart()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        performanceTracker.cleanup()
    }
}
