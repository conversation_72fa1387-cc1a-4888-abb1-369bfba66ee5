package com.tfkcolin.josyandchris.data

import android.os.Parcelable
import com.google.firebase.firestore.ServerTimestamp
import kotlinx.parcelize.Parcelize
import java.util.Calendar
import java.util.Date

@Parcelize
data class Shipment(
    val id: String = "",
    val cargoId: String = "",
    val clientName: String = "",
    val clientPhone: String = "",
    val weightKg: Double = 0.0,
    val volumeCbm: Double = 0.0,
    val amountDue: Double = 0.0,
    val statusIndex: Int = 0,
    val products: List<ShipmentProduct> = listOf(),
    val searchIndex: List<String> = listOf(), // For better search functionality with prefix tokens
    @ServerTimestamp val created: Date = Calendar.getInstance().time
) : Parcelable