package com.tfkcolin.josyandchris.data

import android.os.Parcelable
import com.google.firebase.firestore.ServerTimestamp
import kotlinx.parcelize.Parcelize
import java.util.*

@Parcelize
data class Command(
    val id: String = "",
    val client: ClientData = ClientData(),
    val products: List<MiniProduct> = listOf(),
    val commandStepIndex: Int = 0,
    val observation: List<String> = listOf(),
    val paymentProofImageUrl: String? = null,
    val proofUploaded: Boolean = false,
    @ServerTimestamp val created: Date = Calendar.getInstance().time,
    val searchIndex: List<String> = listOf() // Array for text search tokens
) : Parcelable