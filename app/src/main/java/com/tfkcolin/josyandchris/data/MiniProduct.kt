package com.tfkcolin.josyandchris.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.UUID

@Parcelize
data class MiniProduct(
    val name: String = "",
    val quantity: Int = 0,
    val unitSellingPrice: Int = 0,
    val unitBuyingPrice: Int = 0,
    val description: String = "",
    val productEvolutionStep: String? = CommandStep.RECORD.step,
    val productImage: String? = null,
    val previewsPathName: String? = UUID.randomUUID().toString(),
    val soldOut: Boolean? = false
) : Parcelable