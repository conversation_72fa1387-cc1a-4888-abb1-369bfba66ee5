package com.tfkcolin.josyandchris.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore for managing recent client selections for autocomplete suggestions
 */
@Singleton
class ClientPreferencesDataStore @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val CLIENT_PREFERENCES_NAME = "client_preferences"
        private val KEY_RECENT_CLIENTS = stringSetPreferencesKey("recent_clients")
        private const val MAX_RECENT_CLIENTS = 10
        
        private val Context.clientDataStore: DataStore<Preferences> by preferencesDataS<PERSON>(
            name = CLIENT_PREFERENCES_NAME
        )
    }
    
    private val dataStore: DataStore<Preferences> = context.clientDataStore
    
    /**
     * Add a client to recent clients list
     * Maintains a maximum of MAX_RECENT_CLIENTS and puts most recent at the front
     */
    suspend fun addRecentClient(clientName: String) {
        if (clientName.isBlank()) return
        
        dataStore.edit { preferences ->
            val currentClients = preferences[KEY_RECENT_CLIENTS]?.toMutableList() ?: mutableListOf()
            
            // Remove if already exists to avoid duplicates
            currentClients.remove(clientName)
            
            // Add to front
            currentClients.add(0, clientName)
            
            // Keep only MAX_RECENT_CLIENTS
            val trimmedClients = currentClients.take(MAX_RECENT_CLIENTS)
            
            preferences[KEY_RECENT_CLIENTS] = trimmedClients.toSet()
        }
    }
    
    /**
     * Get recent clients as a Flow, ordered by most recent first
     */
    fun getRecentClientsFlow(): Flow<List<String>> {
        return dataStore.data.map { preferences ->
            val clientsSet = preferences[KEY_RECENT_CLIENTS] ?: emptySet()
            // Convert back to ordered list (DataStore Set loses order, so we need to implement ordering)
            clientsSet.toList()
        }
    }
    
    /**
     * Get recent clients as list
     */
    suspend fun getRecentClients(): List<String> {
        return dataStore.data.map { preferences ->
            val clientsSet = preferences[KEY_RECENT_CLIENTS] ?: emptySet()
            clientsSet.toList()
        }.first() // Get the first emission
    }
    
    /**
     * Clear all recent clients
     */
    suspend fun clearRecentClients() {
        dataStore.edit { preferences ->
            preferences.remove(KEY_RECENT_CLIENTS)
        }
    }
}
