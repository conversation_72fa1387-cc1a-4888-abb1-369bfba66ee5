package com.tfkcolin.josyandchris.data

import android.os.Parcelable
import com.google.firebase.firestore.ServerTimestamp
import kotlinx.parcelize.Parcelize
import java.util.*

@Parcelize
data class Cargo(
    val id: String = "",
    val origin: String = "",
    val destination: String = "",
    val statusIndex: Int = 0,
    val departureDate: Date? = null,
    val estimatedArrivalDate: Date? = null,
    val actualArrivalDate: Date? = null,
    val searchIndex: List<String> = listOf(), // For better search functionality with prefix tokens
    @ServerTimestamp val created: Date = Calendar.getInstance().time
) : Parcelable
