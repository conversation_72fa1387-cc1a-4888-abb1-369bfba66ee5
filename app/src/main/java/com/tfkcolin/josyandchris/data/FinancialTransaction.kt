package com.tfkcolin.josyandchris.data

import java.util.*

data class FinancialTransaction(
    val id: String = "0",
    val label: String = "",
    val price: Int = 0,
    val commandId: String? = null,
    val commandStepIndex: Int? = null,
    val country: String = "", // country where the transaction occurred
    val transactionTypeIndex: Int = 0,
    val marked: Boolean = false,
    /*@ServerTimestamp*/ val created: Date = Calendar.getInstance().time
) //transaction here are load and input
