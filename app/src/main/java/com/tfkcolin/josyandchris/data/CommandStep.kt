package com.tfkcolin.josyandchris.data

import androidx.annotation.DrawableRes

enum class CommandStep(val step: String, val label: String, @DrawableRes val iconResId: Int){
    RECORD(
        "Enregistrement",
        "Enregistrement du produit",
        com.tfkcolin.josyandchris.R.drawable.ic_record_24
    ),
    BUYING(
        "Commander",
        "Commander le produit",
        com.tfkcolin.josyandchris.R.drawable.ic_buying_24
    ),
    RECEIVED(
        "Reçue",
        "Produit reçue",
        com.tfkcolin.josyandchris.R.drawable.ic_received_24
    ),
    DELIVERED(
        "Expédié",
        "Produit expédié",
        com.tfkcolin.josyandchris.R.drawable.ic_delivery_boat_24
    ),
    READY(
        "Prêt",
        "Produit prêt à être livré",
        com.tfkcolin.josyandchris.R.drawable.ic_home_24
    ),
    OK(
        "OK",
        "produit livré au client",
        com.tfkcolin.josyandchris.R.drawable.ic_ok_24
    );

    fun next(): CommandStep?{
        return if(this.ordinal + 1 >= entries.size) null
        else entries[this.ordinal + 1]
    }
    fun previous(): CommandStep?{
        return if(this.ordinal - 1 < 0) null
        else entries[this.ordinal - 1]
    }
}
