package com.tfkcolin.josyandchris.data

import androidx.annotation.DrawableRes

enum class ShipmentStatus(val status: String, val label: String, @DrawableRes val iconResId: Int) {
    RECEIVED_AT_ORIGIN(
        "Received at Origin",
        "Reçu à l'origine",
        com.tfkcolin.josyandchris.R.drawable.ic_record_24
    ),
    IN_CARGO(
        "In Cargo",
        "Dans le cargo",
        com.tfkcolin.josyandchris.R.drawable.ic_buying_24
    ),
    READY_FOR_PICKUP(
        "Ready for Pickup",
        "Prêt pour la collecte",
        com.tfkcolin.josyandchris.R.drawable.ic_received_24
    ),
    COLLECTED(
        "Collected",
        "Collecté",
        com.tfkcolin.josyandchris.R.drawable.ic_ok_24
    );

    fun next(): ShipmentStatus? {
        return if (this.ordinal + 1 >= values().size) null
        else values()[this.ordinal + 1]
    }

    fun previous(): ShipmentStatus? {
        return if (this.ordinal - 1 < 0) null
        else values()[this.ordinal - 1]
    }
}
