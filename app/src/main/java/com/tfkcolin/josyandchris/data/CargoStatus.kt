package com.tfkcolin.josyandchris.data

import androidx.annotation.DrawableRes

enum class CargoStatus(val status: String, val label: String, @DrawableRes val iconResId: Int) {
    LOADING(
        "Loading",
        "Chargement en cours",
        com.tfkcolin.josyandchris.R.drawable.ic_record_24
    ),
    IN_TRANSIT(
        "In Transit",
        "En transit",
        com.tfkcolin.josyandchris.R.drawable.ic_delivery_boat_24
    ),
    ARRIVED(
        "Arrived",
        "Arrivé",
        com.tfkcolin.josyandchris.R.drawable.ic_received_24
    ),
    UNLOADING(
        "Unloading",
        "Déchargement en cours",
        com.tfkcolin.josyandchris.R.drawable.ic_buying_24
    ),
    COMPLETED(
        "Completed",
        "Terminé",
        com.tfkcolin.josyandchris.R.drawable.ic_ok_24
    );

    fun next(): CargoStatus? {
        return if (this.ordinal + 1 >= entries.size) null
        else entries[this.ordinal + 1]
    }

    fun previous(): CargoStatus? {
        return if (this.ordinal - 1 < 0) null
        else entries[this.ordinal - 1]
    }
}
