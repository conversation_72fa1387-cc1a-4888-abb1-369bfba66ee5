package com.tfkcolin.josyandchris.data

import java.util.*

/**
 * Represents image metadata in the application.
 * 
 * This class stores information about images including their URLs, categories, genres,
 * and upload status. It also includes searchIndex field for efficient text search operations.
 * 
 * @property id Unique identifier for the image
 * @property url Remote URL where the image is hosted
 * @property category Category classification of the image
 * @property genre Genre classification of the image
 * @property path Local file path to the image
 * @property name Display name of the image
 * @property upload Flag indicating whether the image has been uploaded to remote storage
 * @property created Timestamp when the image record was created
 * @property searchIndex A string of search tokens generated from image metadata for search optimization
 */
data class ImageData(
    val id: String = "",
    val url: String? = null,
    val category: String = "",
    val genre: String = "",
    val path: String? = null,
    val name: String? = null,
    val upload: Boolean = false,
    val created: Date = Calendar.getInstance().time,
    val searchIndex: String = ""
)
