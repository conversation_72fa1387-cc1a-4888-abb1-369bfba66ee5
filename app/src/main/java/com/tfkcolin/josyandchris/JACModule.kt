package com.tfkcolin.josyandchris

import android.content.Context
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import com.tfkcolin.josyandchris.repository.CargoRepository
import com.tfkcolin.josyandchris.repository.FirebaseCargoRepositoryImpl
import com.tfkcolin.josyandchris.repository.FinancialTransactionRepository
import com.tfkcolin.josyandchris.repository.FirebaseFinancialTransactionRepositoryImpl
import com.tfkcolin.josyandchris.repository.ShipmentRepository
import com.tfkcolin.josyandchris.repository.FirebaseShipmentRepositoryImpl
import com.tfkcolin.josyandchris.repository.CountryRepository
import com.tfkcolin.josyandchris.repository.FirebaseCountryRepositoryImpl
import com.tfkcolin.josyandchris.repository.CommandRepository
import com.tfkcolin.josyandchris.repository.FirebaseCommandRepositoryImpl
import com.tfkcolin.josyandchris.repository.ImageDataRepository
import com.tfkcolin.josyandchris.repository.FirebaseImageDataRepositoryImpl
import com.tfkcolin.josyandchris.repository.StorageRepository
import com.tfkcolin.josyandchris.repository.StorageRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object JACModule {
    @Singleton
    @Provides
    @Named("PRODUCTS_DB") // collectionRef can be stored (via injection) in ViewModel
    fun provideProductCollection() = FirebaseFirestore.getInstance().collection("products")

    @Singleton
    @Provides
    @Named("TRANSACTION_DB")
    fun provideTransactionCollection() = FirebaseFirestore.getInstance().collection("transactions")

    @Singleton
    @Provides
    @Named("COMMAND_DB")
    fun provideCommandCollection() = FirebaseFirestore.getInstance().collection("commands")

    @Singleton
    @Provides
    @Named("COUNTRY_DB")
    fun provideCountryCollection() = FirebaseFirestore.getInstance().collection("countries")

    @Singleton
    @Provides
    @Named("IMAGES_DB")
    fun provideImageListCollection() = FirebaseFirestore.getInstance().collection("images_data")

    @Singleton
    @Provides
    @Named("CARGO_DB")
    fun provideCargoCollection() = FirebaseFirestore.getInstance().collection("cargos")

    @Singleton
    @Provides
    @Named("SHIPMENT_DB")
    fun provideShipmentCollection() = FirebaseFirestore.getInstance().collection("shipments")

    @Singleton
    @Provides
    @Named("PRODUCT_IMAGES_STORAGE")
    fun provideFBImagesStorage() = FirebaseStorage.getInstance().reference.child("product/images_v2")

    @Singleton
    @Provides
    @Named("PRODUCT_IMAGES_STORAGE_V1")
    fun provideFBImagesStorageV1() = FirebaseStorage.getInstance().reference.child("product/images")

    @Singleton
    @Provides
    @Named("STORAGE")
    fun provideFBStorage() = FirebaseStorage.getInstance().reference

    @Singleton
    @Provides
    fun provideMoshi(): Moshi {
        return Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
    }
    
    @Singleton
    @Provides
    fun provideFirebaseFirestore(): FirebaseFirestore {
        return FirebaseFirestore.getInstance()
    }


    // Note: All authentication dependencies are now provided by the AuthModule
    // This ensures the new Auth domain is the single source of truth
}

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    @Binds
    @Singleton
    abstract fun bindCargoRepository(
        firebaseCargoRepositoryImpl: FirebaseCargoRepositoryImpl
    ): CargoRepository
    
    @Binds
    @Singleton
    abstract fun bindFinancialTransactionRepository(
        firebaseFinancialTransactionRepositoryImpl: FirebaseFinancialTransactionRepositoryImpl
    ): FinancialTransactionRepository
    
    @Binds
    @Singleton
    abstract fun bindShipmentRepository(
        firebaseShipmentRepositoryImpl: FirebaseShipmentRepositoryImpl
    ): ShipmentRepository
    
    @Binds
    @Singleton
    abstract fun bindCountryRepository(
        firebaseCountryRepositoryImpl: FirebaseCountryRepositoryImpl
    ): CountryRepository
    
    @Binds
    @Singleton
    abstract fun bindCommandRepository(
        firebaseCommandRepositoryImpl: FirebaseCommandRepositoryImpl
    ): CommandRepository
    
    @Binds
    @Singleton
    abstract fun bindImageDataRepository(
        impl: FirebaseImageDataRepositoryImpl
    ): ImageDataRepository
    
    @Binds
    @Singleton
    abstract fun bindStorageRepository(
        impl: StorageRepositoryImpl
    ): StorageRepository
}
