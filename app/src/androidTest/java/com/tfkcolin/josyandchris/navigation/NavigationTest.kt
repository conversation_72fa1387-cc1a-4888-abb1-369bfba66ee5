package com.tfkcolin.josyandchris.navigation

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.MainActivity
import com.tfkcolin.josyandchris.ui.data.Screen
import com.tfkcolin.josyandchris.ui.screens.NavigationGraph
import com.tfkcolin.josyandchris.ui.screens.accounting.navigateToAccountingScreen
import com.tfkcolin.josyandchris.ui.screens.addimagedatascreen.navigateToAddImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.cargodetails.navigateToCargoDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.cargolist.navigateToCargoScreen
import com.tfkcolin.josyandchris.ui.screens.commanddetails.navigateToCommandDetailsScreen
import com.tfkcolin.josyandchris.ui.screens.commandlist.navigateToCommandScreen
import com.tfkcolin.josyandchris.ui.screens.editimagedata.navigateToEditImageDataScreen
import com.tfkcolin.josyandchris.ui.screens.homeselection.navigateToCountrySelectionScreen
import com.tfkcolin.josyandchris.ui.screens.login.navigateToLoginScreen
import com.tfkcolin.josyandchris.ui.screens.shipmentdetails.navigateToShipmentDetailsScreen
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class NavigationTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Test
    fun testDeepLinkToAccountingScreen() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                countries = emptyList(),
                transactions = emptyList(),
                commands = emptyList(),
                imagesData = emptyList(),
                cargos = emptyList(),
                shipments = emptyList(),
                setHideAppbar = {},
                role = null,
                authManager = mockk(),
                trySuspendFunction = { _, _ -> },
                showAddTransactionDialog = false,
                onShowAddTransactionDialogChanged = {},
                onShowSnackbar = {},
                onLoginResult = {},
                onTitleDescriptionChange = {},
                onShowConfirmationDialog = {}
            )
            
            // Test deep link
            navController.navigateToAccountingScreen("France")
        }
        
        // Verify we're on the accounting screen
        composeTestRule.waitForIdle()
        val currentRoute = composeTestRule.activity.findNavController().currentBackStackEntry?.destination?.route
        assert(currentRoute?.contains("accounting") == true)
    }

    @Test
    fun testBackStackFromCommandDetails() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                startDestination = Screen.CountrySelectionScreen.route,
                // ... other parameters
            )
            
            // Navigate through screens
            navController.navigateToCommandScreen(stepIndex = 0)
            navController.navigateToCommandDetailsScreen(id = "test-command")
        }
        
        // Press back
        composeTestRule.onBackPressed()
        
        // Verify we're back on command list
        val currentRoute = composeTestRule.activity.findNavController().currentBackStackEntry?.destination?.route
        assert(currentRoute?.contains("command_list") == true)
    }

    @Test
    fun testBackStackFromShipmentDetails() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                startDestination = Screen.CountrySelectionScreen.route,
                // ... other parameters
            )
            
            // Navigate through screens
            navController.navigateToCargoScreen(statusIndex = 0)
            navController.navigateToCargoDetailsScreen(id = "test-cargo", isCreate = false)
            navController.navigateToShipmentDetailsScreen(id = "test-shipment", cargoId = "test-cargo")
        }
        
        // Press back
        composeTestRule.onBackPressed()
        
        // Verify we're back on cargo details
        val currentRoute = composeTestRule.activity.findNavController().currentBackStackEntry?.destination?.route
        assert(currentRoute?.contains("cargo_details") == true)
    }

    @Test
    fun testDeepLinkToEditImageData() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                // ... parameters
            )
            
            // Test deep link
            navController.navigateToEditImageDataScreen("image-123")
        }
        
        // Verify we're on the edit image data screen
        composeTestRule.waitForIdle()
        val currentRoute = composeTestRule.activity.findNavController().currentBackStackEntry?.destination?.route
        assert(currentRoute?.contains("edit_image_data") == true)
    }

    @Test
    fun testNavigationWithNullableParameters() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                // ... parameters
            )
            
            // Test navigation with null shipmentId
            navController.navigateToShipmentDetailsScreen(id = null, cargoId = "cargo-123")
        }
        
        // Verify navigation succeeded
        composeTestRule.waitForIdle()
        val currentRoute = composeTestRule.activity.findNavController().currentBackStackEntry?.destination?.route
        assert(currentRoute?.contains("shipment_details") == true)
    }

    @Test
    fun testCompleteNavigationFlow() {
        composeTestRule.setContent {
            val navController = rememberNavController()
            NavigationGraph(
                navController = navController,
                startDestination = Screen.LoginScreen.route,
                // ... parameters
            )
            
            // Test complete flow
            navController.navigateToCountrySelectionScreen()
            navController.navigateToAccountingScreen("France")
            
            // Go back to home
            navController.popBackStack(Screen.CountrySelectionScreen.route, false)
            
            // Navigate to command flow
            navController.navigateToCommandScreen(stepIndex = 1)
            navController.navigateToCommandDetailsScreen(id = "cmd-123")
            
            // Navigate to add image
            navController.navigateToAddImageDataScreen()
        }
        
        // Verify we can navigate through the entire flow
        composeTestRule.waitForIdle()
        assert(true) // If we got here without crashes, navigation is working
    }
}
