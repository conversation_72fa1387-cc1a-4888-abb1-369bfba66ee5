package com.tfkcolin.josyandchris.ui.components

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.components.foundation.DateRangePresets
import com.tfkcolin.josyandchris.ui.data.CommandUiFilterState
import com.tfkcolin.josyandchris.ui.theme.JosyAndChrisTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Compose UI tests for FilterBottomSheet component
 * Tests filter panel interactions and state management
 */
@RunWith(AndroidJUnit4::class)
@OptIn(ExperimentalMaterial3Api::class)
class FilterBottomSheetTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun filterBottomSheet_displaysCorrectly() {
        // Arrange
        var currentFilterState = CommandUiFilterState()
        var onFilterStateChangeCalled = false
        var onApplyCalled = false
        var onClearCalled = false
        var onDismissCalled = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { 
                        currentFilterState = it
                        onFilterStateChangeCalled = true
                    },
                    onApply = { onApplyCalled = true },
                    onClear = { onClearCalled = true },
                    onDismiss = { onDismissCalled = true }
                )
            }
        }

        // Assert
        composeTestRule.onNodeWithText("Advanced Filters").assertIsDisplayed()
        composeTestRule.onNodeWithText("Quick Filters").assertIsDisplayed()
        composeTestRule.onNodeWithText("Client Filter").assertIsDisplayed()
        composeTestRule.onNodeWithText("Date Range").assertIsDisplayed()
        composeTestRule.onNodeWithText("Payment Status").assertIsDisplayed()
        composeTestRule.onNodeWithText("Command Status").assertIsDisplayed()
        composeTestRule.onNodeWithText("Apply Filters").assertIsDisplayed()
        composeTestRule.onNodeWithText("Clear").assertIsDisplayed()
    }

    @Test
    fun filterBottomSheet_presetFilters_clickable() {
        // Arrange
        var currentFilterState = CommandUiFilterState()
        var filterStateChanged = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { 
                        currentFilterState = it
                        filterStateChanged = true
                    },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Click on "Recent" preset filter
        composeTestRule.onNodeWithText("Recent").performClick()

        // Assert
        assert(filterStateChanged)
        assert(currentFilterState.dateRange != null)
    }

    @Test
    fun filterBottomSheet_clientNameFilter_textInput() {
        // Arrange
        var currentFilterState = CommandUiFilterState()
        val testClientName = "John Doe"

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Find client name input field and type
        composeTestRule.onNodeWithText("Client Name").performTextInput(testClientName)

        // Assert
        assert(currentFilterState.clientName == testClientName)
    }

    @Test
    fun filterBottomSheet_phoneNumberFilter_textInput() {
        // Arrange
        var currentFilterState = CommandUiFilterState()
        val testPhoneNumber = "1234567890"

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Find phone number input field and type
        composeTestRule.onNodeWithText("Phone Number").performTextInput(testPhoneNumber)

        // Assert
        assert(currentFilterState.clientPhone == testPhoneNumber)
    }

    @Test
    fun filterBottomSheet_paymentStatusToggle_interaction() {
        // Arrange
        var currentFilterState = CommandUiFilterState()

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Click on "Paid" payment status
        composeTestRule.onAllNodesWithText("Paid")[0].performClick()

        // Assert
        assert(currentFilterState.paymentStatus == true)

        // Click on "Unpaid" payment status
        composeTestRule.onAllNodesWithText("Unpaid")[0].performClick()

        // Assert
        assert(currentFilterState.paymentStatus == false)

        // Click on "All" payment status
        composeTestRule.onAllNodesWithText("All")[0].performClick()

        // Assert
        assert(currentFilterState.paymentStatus == null)
    }

    @Test
    fun filterBottomSheet_commandStepSelection_interaction() {
        // Arrange
        var currentFilterState = CommandUiFilterState()

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Click on "All Steps" first
        composeTestRule.onNodeWithText("All Steps").performClick()
        assert(currentFilterState.selectedStep == null)

        // Click on a specific command step (assuming BUYING step exists)
        composeTestRule.onNodeWithText(CommandStep.BUYING.step).performClick()

        // Assert
        assert(currentFilterState.selectedStep == CommandStep.BUYING)
    }

    @Test
    fun filterBottomSheet_clearButton_resetsState() {
        // Arrange
        var currentFilterState = CommandUiFilterState(
            clientName = "John Doe",
            selectedStep = CommandStep.BUYING,
            paymentStatus = true
        )
        var onClearCalled = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { onClearCalled = true },
                    onDismiss = { }
                )
            }
        }

        // Click clear button
        composeTestRule.onNodeWithText("Clear").performClick()

        // Assert
        assert(onClearCalled)
        // The filter state should be reset by the parent component
    }

    @Test
    fun filterBottomSheet_applyButton_triggersCallback() {
        // Arrange
        var onApplyCalled = false
        var appliedFilterState: CommandUiFilterState? = null

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = CommandUiFilterState(clientName = "Test Client"),
                    onFilterStateChange = { },
                    onApply = { 
                        onApplyCalled = true
                        appliedFilterState = it
                    },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Click apply button
        composeTestRule.onNodeWithText("Apply Filters").performClick()

        // Assert
        assert(onApplyCalled)
        assert(appliedFilterState != null)
        assert(appliedFilterState?.clientName == "Test Client")
    }

    @Test
    fun filterBottomSheet_autoCompleteTextField_showsSuggestions() {
        // Arrange
        val recentClientChips = listOf("John Doe", "Jane Smith", "Bob Johnson")
        var currentFilterState = CommandUiFilterState(
            recentClientChips = recentClientChips
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Type partial text in client name field
        composeTestRule.onNodeWithText("Client Name").performTextInput("John")

        // Wait for UI to update and check if suggestions appear
        composeTestRule.waitForIdle()

        // The suggestions should be available in the recent chips section
        composeTestRule.onNodeWithText("John Doe").assertIsDisplayed()
    }

    @Test
    fun filterBottomSheet_recentChips_clickable() {
        // Arrange
        val recentClientChips = listOf("John Doe", "1234567890")
        var currentFilterState = CommandUiFilterState(
            recentClientChips = recentClientChips
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Click on client name chip
        composeTestRule.onNodeWithText("John Doe").performClick()

        // Assert that the client name was applied
        assert(currentFilterState.clientName == "John Doe")

        // Reset state
        currentFilterState = CommandUiFilterState(recentClientChips = recentClientChips)

        // Click on phone number chip (contains digits)
        composeTestRule.onNodeWithText("1234567890").performClick()

        // Assert that the phone number was applied
        assert(currentFilterState.clientPhone == "1234567890")
    }

    @Test
    fun filterBottomSheet_countryAndCityFilters_textInput() {
        // Arrange
        var currentFilterState = CommandUiFilterState()
        val testCountry = "USA"
        val testCity = "New York"

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = currentFilterState,
                    onFilterStateChange = { currentFilterState = it },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Input country
        composeTestRule.onNodeWithText("Country").performTextInput(testCountry)
        assert(currentFilterState.clientCountry == testCountry)

        // Input city
        composeTestRule.onNodeWithText("City").performTextInput(testCity)
        assert(currentFilterState.clientCity == testCity)
    }

    @Test
    fun filterBottomSheet_selectedStepDetails_displayed() {
        // Arrange
        val filterState = CommandUiFilterState(selectedStep = CommandStep.BUYING)

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = filterState,
                    onFilterStateChange = { },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Assert that selected step details are shown
        composeTestRule.onNodeWithText(CommandStep.BUYING.label).assertIsDisplayed()
    }

    @Test
    fun filterBottomSheet_accessibilityLabels_present() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = CommandUiFilterState(),
                    onFilterStateChange = { },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Assert accessibility
        composeTestRule.onNodeWithContentDescription("Filter commands").assertExists()
        // Note: Add more accessibility assertions based on the actual content descriptions used
    }

    @Test
    fun filterBottomSheet_scrollable_content() {
        // Arrange
        val filterState = CommandUiFilterState(
            clientName = "Test Client",
            selectedStep = CommandStep.BUYING,
            paymentStatus = true
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                FilterBottomSheet(
                    filterState = filterState,
                    onFilterStateChange = { },
                    onApply = { },
                    onClear = { },
                    onDismiss = { }
                )
            }
        }

        // Perform scroll action to test scrollability
        composeTestRule.onNodeWithText("Advanced Filters")
            .assertIsDisplayed()

        // Scroll down to see apply button
        composeTestRule.onNodeWithText("Apply Filters")
            .performScrollTo()
            .assertIsDisplayed()
    }
}
