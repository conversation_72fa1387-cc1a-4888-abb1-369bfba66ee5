package com.tfkcolin.josyandchris.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import org.junit.Rule
import org.junit.Test

class JACCargoOverviewTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun cargoOverview_displaysSampleData_correctCounts() {
        // Arrange - Set up the component with the sample data
        composeTestRule.setContent {
            JACTheme {
                JACCargoOverview(
                    cargoCountsByStatus = mapOf(
                        CargoStatus.LOADING to 3,     // 0→3
                        CargoStatus.IN_TRANSIT to 5,  // 1→5
                        CargoStatus.ARRIVED to 2,     // 2→2
                        CargoStatus.UNLOADING to 1,   // 3→1
                        CargoStatus.COMPLETED to 4    // 4→4
                    ),
                    onCargoStatusClicked = {}
                )
            }
        }

        // Act & Assert - Check that each status shows the correct count
        // Check "Chargement en cours" label with 3 cargos
        composeTestRule.onNodeWithText("Chargement en cours")
            .assertExists()
        composeTestRule.onNodeWithText("3 cargos")
            .assertExists()

        // Check "En transit" label with 5 cargos
        composeTestRule.onNodeWithText("En transit")
            .assertExists()
        composeTestRule.onNodeWithText("5 cargos")
            .assertExists()

        // Check "Arrivé" label with 2 cargos
        composeTestRule.onNodeWithText("Arrivé")
            .assertExists()
        composeTestRule.onNodeWithText("2 cargos")
            .assertExists()

        // Check "Déchargement en cours" label with 1 cargo
        composeTestRule.onNodeWithText("Déchargement en cours")
            .assertExists()
        composeTestRule.onNodeWithText("1 cargo")
            .assertExists()

        // Check "Terminé" label with 4 cargos
        composeTestRule.onNodeWithText("Terminé")
            .assertExists()
        composeTestRule.onNodeWithText("4 cargos")
            .assertExists()
    }
}
