package com.tfkcolin.josyandchris.ui.components

import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import com.tfkcolin.josyandchris.domain.exception.DomainException
import com.tfkcolin.josyandchris.ui.states.ImageOperationState
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import java.io.IOException

@ExperimentalCoroutinesApi
class NetworkErrorHandlingTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun networkErrorSnackbarShouldDisplayCorrectly() {
        // Given - Set up a network exception
        val networkException = IOException("Network connection error")
        val errorState = ImageOperationState.Error(
            exception = networkException
        )
        
        // When - Display the error snackbar
        composeTestRule.setContent {
            val snackbarHostState = remember { SnackbarHostState() }
            val dismissed = remember { mutableStateOf(false) }
            
            NetworkErrorSnackbar(
                snackbarHostState = snackbarHostState,
                imageOperationState = errorState,
                onRetry = { /* Do nothing in test */ },
                onDismiss = { dismissed.value = true }
            )
        }

        // Then - Verify that the snackbar is displayed with the correct message
        composeTestRule
            .waitUntil(5000) { 
                composeTestRule
                    .onAllNodesWithText("Network error. Please check your connection.")
                    .fetchSemanticsNodes().size == 1
            }
        
        composeTestRule
            .onNodeWithText("Network error. Please check your connection.")
            .assertIsDisplayed()
            
        composeTestRule
            .onNodeWithText("Retry")
            .assertIsDisplayed()
    }
    
    @Test
    fun storageErrorSnackbarShouldDisplayCorrectly() {
        // Given - Set up a storage exception
        val storageException = DomainException.StorageException.UploadException()
        val errorState = ImageOperationState.Error(
            exception = storageException,
            message = "Failed to upload file"
        )
        
        // When - Display the error snackbar
        composeTestRule.setContent {
            val snackbarHostState = remember { SnackbarHostState() }
            val dismissed = remember { mutableStateOf(false) }
            
            NetworkErrorSnackbar(
                snackbarHostState = snackbarHostState,
                imageOperationState = errorState,
                onRetry = { /* Do nothing in test */ },
                onDismiss = { dismissed.value = true }
            )
        }

        // Then - Verify that the snackbar is displayed with the correct message
        composeTestRule
            .waitUntil(5000) { 
                composeTestRule
                    .onAllNodesWithText("Failed to upload file")
                    .fetchSemanticsNodes().size == 1
            }
        
        composeTestRule
            .onNodeWithText("Failed to upload file")
            .assertIsDisplayed()
            
        composeTestRule
            .onNodeWithText("Retry")
            .assertIsDisplayed()
    }
    
    @Test
    fun successMessageShouldDisplayCorrectly() {
        // Given - Set up a success state
        val successState = ImageOperationState.Success(
            data = Unit,
            message = "Operation completed successfully"
        )
        
        // When - Display the success snackbar
        composeTestRule.setContent {
            val snackbarHostState = remember { SnackbarHostState() }
            val dismissed = remember { mutableStateOf(false) }
            
            NetworkErrorSnackbar(
                snackbarHostState = snackbarHostState,
                imageOperationState = successState,
                onDismiss = { dismissed.value = true }
            )
        }

        // Then - Verify that the snackbar is displayed with the correct message
        composeTestRule
            .waitUntil(5000) { 
                composeTestRule
                    .onAllNodesWithText("Operation completed successfully")
                    .fetchSemanticsNodes().size == 1
            }
        
        composeTestRule
            .onNodeWithText("Operation completed successfully")
            .assertIsDisplayed()
    }
}
