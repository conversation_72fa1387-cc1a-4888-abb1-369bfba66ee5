package com.tfkcolin.josyandchris.ui.screens.shipmentdetails

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.ShipmentProduct
import com.tfkcolin.josyandchris.data.ShipmentStatus
import com.tfkcolin.josyandchris.data.UserRole
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ShipmentDetailsScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val testCargoId = "test_cargo_123"
    
    private val testShipment = Shipment(
        id = "test_shipment_1",
        cargoId = testCargoId,
        clientName = "Test Client",
        clientPhone = "+237 656 123 456",
        weightKg = 25.0,
        volumeCbm = 0.5,
        amountDue = 100.0,
        statusIndex = ShipmentStatus.IN_CARGO.ordinal,
        products = listOf(
            ShipmentProduct(
                name = "Test Product",
                quantity = 2,
                description = "Test description"
            )
        )
    )

    @Test
    fun testShipmentDetailsScreen_CreateMode() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = emptyList(),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify create mode UI
        composeTestRule.onNodeWithText("Nouvelle expédition").assertIsDisplayed()
        composeTestRule.onNodeWithText("Nom du client").assertIsDisplayed()
        composeTestRule.onNodeWithText("Téléphone du client").assertIsDisplayed()
        composeTestRule.onNodeWithText("Poids (KG)").assertIsDisplayed()
        composeTestRule.onNodeWithText("Volume (CBM)").assertIsDisplayed()
        composeTestRule.onNodeWithText("Montant dû").assertIsDisplayed()
        
        // Verify no products initially
        composeTestRule.onNodeWithText("Aucun produit ajouté").assertIsDisplayed()
    }

    @Test
    fun testShipmentDetailsScreen_EditMode() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify edit mode UI with data
        composeTestRule.onNodeWithText("Détails de l'expédition").assertIsDisplayed()
        composeTestRule.onNodeWithText("Test Client").assertIsDisplayed()
        composeTestRule.onNodeWithText("+237 656 123 456").assertIsDisplayed()
        composeTestRule.onNodeWithText("25.0").assertIsDisplayed()
        composeTestRule.onNodeWithText("0.5").assertIsDisplayed()
        composeTestRule.onNodeWithText("100.0").assertIsDisplayed()
        
        // Verify product is displayed
        composeTestRule.onNodeWithText("Test Product").assertIsDisplayed()
        composeTestRule.onNodeWithText("Quantité: 2").assertIsDisplayed()
        composeTestRule.onNodeWithText("Test description").assertIsDisplayed()
    }

    @Test
    fun testAddProduct_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Click add product button
        composeTestRule.onNode(
            hasContentDescription("Ajouter un produit")
        ).performClick()

        // Verify dialog appears
        composeTestRule.onNodeWithText("Ajouter un produit").assertIsDisplayed()
        composeTestRule.onNodeWithText("Nom du produit").assertIsDisplayed()
        composeTestRule.onNodeWithText("Quantité").assertIsDisplayed()
        composeTestRule.onNodeWithText("Description (optionnel)").assertIsDisplayed()

        // Fill in product details
        composeTestRule.onNode(
            hasText("Nom du produit") and hasSetTextAction()
        ).performTextInput("New Product")

        composeTestRule.onNode(
            hasText("Quantité") and hasSetTextAction()
        ).performTextClearance()
        composeTestRule.onNode(
            hasText("") and hasSetTextAction()
        ).performTextInput("5")

        composeTestRule.onNode(
            hasText("Description (optionnel)") and hasSetTextAction()
        ).performTextInput("New product description")

        // Click Add button
        composeTestRule.onNodeWithText("Ajouter").performClick()

        // Verify dialog is dismissed and product is added
        composeTestRule.onNodeWithText("Ajouter un produit").assertDoesNotExist()
    }

    @Test
    fun testAddProduct_ClientRole_NoAccess() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.CLIENT.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify add product button doesn't exist for CLIENT role
        composeTestRule.onNode(
            hasContentDescription("Ajouter un produit")
        ).assertDoesNotExist()
    }

    @Test
    fun testDeleteProduct_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify delete button exists for products
        composeTestRule.onNode(
            hasContentDescription("Supprimer le produit")
        ).assertIsDisplayed()
    }

    @Test
    fun testShipmentStatusChange() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Test clicking different status chips
        composeTestRule.onNodeWithText(ShipmentStatus.PENDING.label).performClick()
        composeTestRule.onNodeWithText(ShipmentStatus.IN_CARGO.label).performClick()
        composeTestRule.onNodeWithText(ShipmentStatus.DELIVERED.label).performClick()
        
        // Verify all status chips are displayed
        ShipmentStatus.values().forEach { status ->
            composeTestRule.onNodeWithText(status.label).assertIsDisplayed()
        }
    }

    @Test
    fun testDeleteConfirmation_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify delete button exists for ADMIN
        composeTestRule.onNodeWithText("Supprimer").assertIsDisplayed()
        
        // Click delete button
        composeTestRule.onNodeWithText("Supprimer").performClick()
        
        // Verify confirmation dialog appears
        composeTestRule.onNodeWithText("Supprimer l'expédition").assertIsDisplayed()
        composeTestRule.onNodeWithText("Êtes-vous sûr de vouloir supprimer cette expédition? Cette action ne peut pas être annulée.").assertIsDisplayed()
        
        // Cancel deletion
        composeTestRule.onNodeWithText("Annuler").performClick()
        
        // Verify dialog is dismissed
        composeTestRule.onNodeWithText("Supprimer l'expédition").assertDoesNotExist()
    }

    @Test
    fun testSaveButton_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button exists for ADMIN
        composeTestRule.onNodeWithText("Enregistrer").assertIsDisplayed()
    }

    @Test
    fun testSaveButton_EmployeeRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.EMPLOYEE.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button exists for EMPLOYEE
        composeTestRule.onNodeWithText("Enregistrer").assertIsDisplayed()
        
        // But delete button should not exist
        composeTestRule.onNodeWithText("Supprimer").assertDoesNotExist()
    }

    @Test
    fun testSaveButton_ClientRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = listOf(testShipment),
                    setHideAppbar = {},
                    role = UserRole.CLIENT.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button does not exist for CLIENT
        composeTestRule.onNodeWithText("Enregistrer").assertDoesNotExist()
    }

    @Test
    fun testInputValidation_NumericFields() = runTest {
        composeTestRule.setContent {
            JACTheme {
                ShipmentDetailsScreen(
                    shipments = emptyList(),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Test weight input
        val weightField = composeTestRule.onNode(
            hasText("Poids (KG)") and hasSetTextAction()
        )
        weightField.performTextInput("abc") // Invalid input
        
        // Test volume input
        val volumeField = composeTestRule.onNode(
            hasText("Volume (CBM)") and hasSetTextAction()
        )
        volumeField.performTextInput("xyz") // Invalid input
        
        // Test amount due input
        val amountField = composeTestRule.onNode(
            hasText("Montant dû") and hasSetTextAction()
        )
        amountField.performTextInput("123.45") // Valid input
    }
}
