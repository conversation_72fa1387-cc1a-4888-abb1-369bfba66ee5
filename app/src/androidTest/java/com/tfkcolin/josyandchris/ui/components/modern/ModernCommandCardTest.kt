package com.tfkcolin.josyandchris.ui.components.modern

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.theme.JosyAndChrisTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.util.*

/**
 * Compose UI tests for ModernCommandCard component
 * Tests card interactions and display states
 */
@RunWith(AndroidJUnit4::class)
class ModernCommandCardTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val sampleCommand = Command(
        id = "test-command-id",
        client = mapOf(
            "name" to "John Doe",
            "tel" to "1234567890",
            "country" to "USA",
            "city" to "New York",
            "address" to "123 Test Street"
        ),
        commandStepIndex = CommandStep.BUYING.ordinal,
        created = Date(),
        proofUploaded = true,
        products = listOf(
            mapOf(
                "name" to "Test Product 1",
                "price" to 100.0,
                "quantity" to 2
            ),
            mapOf(
                "name" to "Test Product 2", 
                "price" to 50.0,
                "quantity" to 1
            )
        ),
        totalPrice = 250.0
    )

    @Test
    fun modernCommandCard_displaysBasicInfo() {
        // Arrange
        var onClickCalled = false
        var onViewDetailsCalled = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { onClickCalled = true },
                    onViewDetails = { onViewDetailsCalled = true }
                )
            }
        }

        // Assert - Basic command info is displayed
        composeTestRule.onNodeWithText("John Doe").assertIsDisplayed()
        composeTestRule.onNodeWithText("1234567890").assertIsDisplayed()
        composeTestRule.onNodeWithText("USA, New York").assertIsDisplayed()
        composeTestRule.onNodeWithText("$250.00").assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_displaysCommandStep() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Command step is displayed
        composeTestRule.onNodeWithText(CommandStep.BUYING.step).assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_showsProductCount() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Product count is displayed
        composeTestRule.onNodeWithText("3 items").assertIsDisplayed() // 2 + 1 quantities
    }

    @Test
    fun modernCommandCard_clickTriggersCallback() {
        // Arrange
        var onClickCalled = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { onClickCalled = true },
                    onViewDetails = { }
                )
            }
        }

        // Perform click on card
        composeTestRule.onNodeWithText("John Doe").performClick()

        // Assert
        assert(onClickCalled)
    }

    @Test
    fun modernCommandCard_viewDetailsButtonTriggersCallback() {
        // Arrange
        var onViewDetailsCalled = false

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { onViewDetailsCalled = true }
                )
            }
        }

        // Find and click view details button (assuming it has this text or icon)
        composeTestRule.onNodeWithContentDescription("View details").performClick()

        // Assert
        assert(onViewDetailsCalled)
    }

    @Test
    fun modernCommandCard_displaysProofUploadedIndicator() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand.copy(proofUploaded = true),
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Proof uploaded indicator is shown
        composeTestRule.onNodeWithContentDescription("Proof uploaded").assertExists()
    }

    @Test
    fun modernCommandCard_handlesNoProofUploaded() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand.copy(proofUploaded = false),
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - No proof uploaded indicator should not be present
        composeTestRule.onNodeWithContentDescription("Proof uploaded").assertDoesNotExist()
    }

    @Test
    fun modernCommandCard_handlesEmptyProducts() {
        // Arrange
        val commandWithNoProducts = sampleCommand.copy(
            products = emptyList(),
            totalPrice = 0.0
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = commandWithNoProducts,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert
        composeTestRule.onNodeWithText("0 items").assertIsDisplayed()
        composeTestRule.onNodeWithText("$0.00").assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_handlesNullClientData() {
        // Arrange
        val commandWithNullClient = sampleCommand.copy(
            client = mapOf()
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = commandWithNullClient,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Should not crash and display some fallback text
        composeTestRule.onRoot().assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_displaysFormattedDate() {
        // Arrange
        val specificDate = Calendar.getInstance().apply {
            set(2023, Calendar.JUNE, 15, 10, 30, 0)
        }.time
        
        val commandWithSpecificDate = sampleCommand.copy(created = specificDate)

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = commandWithSpecificDate,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Date should be formatted and displayed (format may vary based on implementation)
        // This test would need to be adjusted based on the actual date format used
        composeTestRule.onRoot().assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_handlesLongClientName() {
        // Arrange
        val commandWithLongName = sampleCommand.copy(
            client = mapOf(
                "name" to "This is a very long client name that should be handled properly by the UI",
                "tel" to "1234567890",
                "country" to "USA",
                "city" to "New York"
            )
        )

        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = commandWithLongName,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Long name should be displayed (may be truncated)
        composeTestRule.onNodeWithText("This is a very long client name that should be handled properly by the UI")
            .assertExists() // Use assertExists() instead of assertIsDisplayed() in case of truncation
    }

    @Test
    fun modernCommandCard_handlesAnimationDelay() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { },
                    animationDelay = 500 // 500ms delay
                )
            }
        }

        // Assert - Card should still be displayed despite animation delay
        composeTestRule.onNodeWithText("John Doe").assertIsDisplayed()
    }

    @Test
    fun modernCommandCard_handlesCommandStepColors() {
        // Test different command steps to ensure proper styling
        val stepTestCases = CommandStep.entries

        stepTestCases.forEach { step ->
            val commandWithStep = sampleCommand.copy(commandStepIndex = step.ordinal)

            composeTestRule.setContent {
                JosyAndChrisTheme {
                    ModernCommandCard(
                        command = commandWithStep,
                        onClick = { },
                        onViewDetails = { }
                    )
                }
            }

            // Assert - Each step should display correctly
            composeTestRule.onNodeWithText(step.step).assertIsDisplayed()
            
            // Clean up for next iteration
            composeTestRule.waitForIdle()
        }
    }

    @Test
    fun modernCommandCard_accessibilitySupport() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert - Basic accessibility support
        composeTestRule.onNodeWithText("John Doe")
            .assertHasClickAction()
            .assertIsDisplayed()

        // Check for semantic roles and content descriptions
        composeTestRule.onNodeWithContentDescription("Command card").assertExists()
    }

    @Test
    fun modernCommandCard_semanticProperties() {
        // Act
        composeTestRule.setContent {
            JosyAndChrisTheme {
                ModernCommandCard(
                    command = sampleCommand,
                    onClick = { },
                    onViewDetails = { }
                )
            }
        }

        // Assert semantic properties for accessibility
        composeTestRule.onRoot()
            .onChildren()
            .filterToOne(hasClickAction())
            .assertIsDisplayed()
    }
}
