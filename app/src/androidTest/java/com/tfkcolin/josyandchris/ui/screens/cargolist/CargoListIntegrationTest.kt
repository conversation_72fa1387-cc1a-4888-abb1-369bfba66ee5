package com.tfkcolin.josyandchris.ui.screens.cargolist

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.util.*

@RunWith(AndroidJUnit4::class)
class CargoListIntegrationTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val testCargos = listOf(
        Cargo(
            id = "cargo1",
            origin = "Yaoundé",
            destination = "Paris",
            statusIndex = CargoStatus.PENDING.ordinal,
            departureDate = Date(),
            searchIndex = listOf("yaoundé", "paris", "en attente")
        ),
        Cargo(
            id = "cargo2",
            origin = "Douala",
            destination = "Lyon",
            statusIndex = CargoStatus.IN_TRANSIT.ordinal,
            departureDate = Date(),
            searchIndex = listOf("douala", "lyon", "en transit")
        ),
        Cargo(
            id = "cargo3",
            origin = "Bafoussam",
            destination = "Marseille",
            statusIndex = CargoStatus.DELIVERED.ordinal,
            departureDate = Date(),
            actualArrivalDate = Date(),
            searchIndex = listOf("bafoussam", "marseille", "livré")
        ),
        Cargo(
            id = "cargo4",
            origin = "Bamenda",
            destination = "Bordeaux",
            statusIndex = CargoStatus.CANCELLED.ordinal,
            departureDate = Date(),
            searchIndex = listOf("bamenda", "bordeaux", "annulé")
        )
    )

    @Test
    fun testCargoListScreen_DisplaysAllCargos() = runTest {
        var navigatedToCargoId = ""

        composeTestRule.setContent {
            JACTheme {
                CargoListScreen(
                    onNavigateToCargoDetail = { cargoId ->
                        navigatedToCargoId = cargoId
                    }
                )
            }
        }

        // Wait for the screen to load
        composeTestRule.waitForIdle()

        // Verify the screen title is displayed
        composeTestRule.onNodeWithText("Cargo List").assertIsDisplayed()

        // Verify search field is displayed
        composeTestRule.onNodeWithText("Rechercher un cargo...").assertIsDisplayed()

        // Verify status filter tabs are displayed
        composeTestRule.onNodeWithText("All").assertIsDisplayed()
        CargoStatus.entries.forEach { status ->
            composeTestRule.onNodeWithText(status.label).assertIsDisplayed()
        }
    }

    @Test
    fun testCargoListScreen_FilterByStatus() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoListScreen(
                    onNavigateToCargoDetail = { }
                )
            }
        }

        // Wait for initial load
        composeTestRule.waitForIdle()

        // Click on "En transit" status filter
        composeTestRule.onNodeWithText(CargoStatus.IN_TRANSIT.label).performClick()

        // Wait for filter to apply
        composeTestRule.waitForIdle()

        // The list should now show only in-transit cargos
        // This would require the actual paging data to be mocked
    }

    @Test
    fun testCargoListScreen_SearchFunctionality() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoListScreen(
                    onNavigateToCargoDetail = { }
                )
            }
        }

        // Find and interact with search field
        val searchField = composeTestRule.onNode(
            hasText("Rechercher un cargo...") and hasSetTextAction()
        )

        // Type search query
        searchField.performTextInput("Yaoundé")

        // Verify clear button appears
        composeTestRule.onNode(
            hasContentDescription("Clear search")
        ).assertIsDisplayed()

        // Clear search
        composeTestRule.onNode(
            hasContentDescription("Clear search")
        ).performClick()

        // Verify search field is cleared
        searchField.assert(hasText(""))
    }

    @Test
    fun testCargoListScreen_NavigationToDetail() = runTest {
        var navigatedToCargoId = ""

        composeTestRule.setContent {
            JACTheme {
                // Mock implementation that shows a single cargo item
                androidx.compose.foundation.lazy.LazyColumn {
                    item {
                        com.tfkcolin.josyandchris.ui.components.JACCargoItem(
                            cargo = testCargos[0],
                            onClick = { navigatedToCargoId = testCargos[0].id }
                        )
                    }
                }
            }
        }

        // Click on the cargo item
        composeTestRule.onNodeWithText("Yaoundé").performClick()

        // Verify navigation callback was called with correct cargo ID
        assert(navigatedToCargoId == "cargo1")
    }

    @Test
    fun testCargoStatusChip_DisplaysCorrectStatus() = runTest {
        composeTestRule.setContent {
            JACTheme {
                androidx.compose.foundation.lazy.LazyColumn {
                    items(testCargos.size) { index ->
                        com.tfkcolin.josyandchris.ui.components.JACCargoItem(
                            cargo = testCargos[index],
                            onClick = { }
                        )
                    }
                }
            }
        }

        // Verify each cargo displays its correct status
        composeTestRule.onNodeWithText(CargoStatus.PENDING.label).assertIsDisplayed()
        composeTestRule.onNodeWithText(CargoStatus.IN_TRANSIT.label).assertIsDisplayed()
        composeTestRule.onNodeWithText(CargoStatus.DELIVERED.label).assertIsDisplayed()
        composeTestRule.onNodeWithText(CargoStatus.CANCELLED.label).assertIsDisplayed()
    }

    @Test
    fun testCargoItem_DisplaysOriginAndDestination() = runTest {
        composeTestRule.setContent {
            JACTheme {
                com.tfkcolin.josyandchris.ui.components.JACCargoItem(
                    cargo = testCargos[0],
                    onClick = { }
                )
            }
        }

        // Verify origin and destination are displayed
        composeTestRule.onNodeWithText("Yaoundé").assertIsDisplayed()
        composeTestRule.onNodeWithText("Paris").assertIsDisplayed()
    }
}
