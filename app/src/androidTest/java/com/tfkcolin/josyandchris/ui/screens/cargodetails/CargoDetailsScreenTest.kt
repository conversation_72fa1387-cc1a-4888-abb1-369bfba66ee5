package com.tfkcolin.josyandchris.ui.screens.cargodetails

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.UserRole
import com.tfkcolin.josyandchris.ui.data.SnackbarState
import com.tfkcolin.josyandchris.ui.theme.JACTheme
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class CargoDetailsScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val testCargo = Cargo(
        id = "test_cargo_1",
        origin = "Yaoundé",
        destination = "Paris",
        statusIndex = CargoStatus.IN_TRANSIT.ordinal,
        departureDate = null,
        estimatedArrivalDate = null,
        actualArrivalDate = null
    )

    private val testShipments = listOf(
        Shipment(
            id = "test_shipment_1",
            cargoId = "test_cargo_1",
            clientName = "Test Client 1",
            clientPhone = "+237 656 123 456",
            weightKg = 25.0,
            volumeCbm = 0.5,
            amountDue = 100.0
        ),
        Shipment(
            id = "test_shipment_2",
            cargoId = "test_cargo_1",
            clientName = "Test Client 2",
            clientPhone = "+237 656 789 012",
            weightKg = 30.0,
            volumeCbm = 0.75,
            amountDue = 150.0
        )
    )

    @Test
    fun testCargoDetailsScreen_CreateMode() = runTest {
        // Test create mode (no cargoId)
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = emptyList(),
                    shipments = emptyList(),
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify create mode UI
        composeTestRule.onNodeWithText("Nouveau cargo").assertIsDisplayed()
        composeTestRule.onNodeWithText("Origine").assertIsDisplayed()
        composeTestRule.onNodeWithText("Destination").assertIsDisplayed()
        
        // Verify no shipments section in create mode
        composeTestRule.onNodeWithText("Expéditions").assertDoesNotExist()
        composeTestRule.onNodeWithText("Ajouter une expédition").assertDoesNotExist()
    }

    @Test
    fun testCargoDetailsScreen_EditMode() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify edit mode UI
        composeTestRule.onNodeWithText("Détails du cargo").assertIsDisplayed()
        
        // Verify cargo data is displayed
        composeTestRule.onNodeWithText("Yaoundé").assertIsDisplayed()
        composeTestRule.onNodeWithText("Paris").assertIsDisplayed()
        
        // Verify shipments section exists
        composeTestRule.onNodeWithText("Expéditions").assertIsDisplayed()
        composeTestRule.onNodeWithText("Ajouter une expédition").assertIsDisplayed()
        
        // Verify shipments are displayed
        composeTestRule.onNodeWithText("Test Client 1").assertIsDisplayed()
        composeTestRule.onNodeWithText("Test Client 2").assertIsDisplayed()
    }

    @Test
    fun testToggleLiveMode() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Find and click the live mode switch
        composeTestRule.onNode(hasContentDescription("Switch")).performClick()
        
        // Verify LIVE indicator appears
        composeTestRule.onNodeWithText("LIVE").assertIsDisplayed()
        
        // Click again to disable
        composeTestRule.onNode(hasContentDescription("Switch")).performClick()
        
        // Verify LIVE indicator disappears
        composeTestRule.onNodeWithText("LIVE").assertDoesNotExist()
    }

    @Test
    fun testCargoStatusChange() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Test clicking different status chips
        composeTestRule.onNodeWithText(CargoStatus.PENDING.label).performClick()
        composeTestRule.onNodeWithText(CargoStatus.IN_TRANSIT.label).performClick()
        composeTestRule.onNodeWithText(CargoStatus.DELIVERED.label).performClick()
        composeTestRule.onNodeWithText(CargoStatus.CANCELLED.label).performClick()
        
        // Verify all status chips are displayed
        CargoStatus.values().forEach { status ->
            composeTestRule.onNodeWithText(status.label).assertIsDisplayed()
        }
    }

    @Test
    fun testDeleteConfirmation_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify delete button exists for ADMIN
        composeTestRule.onNodeWithText("Supprimer").assertIsDisplayed()
        
        // Click delete button
        composeTestRule.onNodeWithText("Supprimer").performClick()
        
        // Verify confirmation dialog appears
        composeTestRule.onNodeWithText("Supprimer le cargo").assertIsDisplayed()
        composeTestRule.onNodeWithText("Êtes-vous sûr de vouloir supprimer ce cargo et toutes ses expéditions? Cette action ne peut pas être annulée.").assertIsDisplayed()
        
        // Cancel deletion
        composeTestRule.onNodeWithText("Annuler").performClick()
        
        // Verify dialog is dismissed
        composeTestRule.onNodeWithText("Supprimer le cargo").assertDoesNotExist()
    }

    @Test
    fun testDeleteConfirmation_NonAdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.EMPLOYEE.role, // Employee role
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify delete button does not exist for non-ADMIN
        composeTestRule.onAllNodesWithText("Supprimer").assertCountEquals(0)
    }

    @Test
    fun testShipmentCreationNavigation() = runTest {
        var navigatedToShipmentDetails = false
        var passedShipmentId = ""
        var passedCargoId = ""

        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { shipmentId, cargoId ->
                        navigatedToShipmentDetails = true
                        passedShipmentId = shipmentId
                        passedCargoId = cargoId
                    },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Click add shipment button
        composeTestRule.onNodeWithText("Ajouter une expédition").performClick()
        
        // Verify navigation was triggered with correct parameters
        assert(navigatedToShipmentDetails)
        assert(passedShipmentId.isEmpty()) // New shipment has empty ID
        assert(passedCargoId == testCargo.id)
    }

    @Test
    fun testExistingShipmentNavigation() = runTest {
        var navigatedToShipmentDetails = false
        var passedShipmentId = ""
        var passedCargoId = ""

        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { shipmentId, cargoId ->
                        navigatedToShipmentDetails = true
                        passedShipmentId = shipmentId
                        passedCargoId = cargoId
                    },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Click on existing shipment
        composeTestRule.onNodeWithText("Test Client 1").performClick()
        
        // Verify navigation was triggered with correct parameters
        assert(navigatedToShipmentDetails)
        assert(passedShipmentId == "test_shipment_1")
        assert(passedCargoId == testCargo.id)
    }

    @Test
    fun testSaveButton_AdminRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.ADMIN.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button exists for ADMIN
        composeTestRule.onNodeWithText("Enregistrer").assertIsDisplayed()
    }

    @Test
    fun testSaveButton_EmployeeRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.EMPLOYEE.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button exists for EMPLOYEE
        composeTestRule.onNodeWithText("Enregistrer").assertIsDisplayed()
    }

    @Test
    fun testSaveButton_ClientRole() = runTest {
        composeTestRule.setContent {
            JACTheme {
                CargoDetailsScreen(
                    cargos = listOf(testCargo),
                    shipments = testShipments,
                    setHideAppbar = {},
                    role = UserRole.CLIENT.role,
                    trySuspendFunction = { _, _ -> },
                    onShowSnackbar = {},
                    onNavigateToShipmentDetailsScreen = { _, _ -> },
                    onTitleDescriptionChange = {}
                )
            }
        }

        // Verify save button does not exist for CLIENT
        composeTestRule.onNodeWithText("Enregistrer").assertDoesNotExist()
    }
}
