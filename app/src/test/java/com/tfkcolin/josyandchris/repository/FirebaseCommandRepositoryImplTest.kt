package com.tfkcolin.josyandchris.repository

import androidx.paging.PagingData
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.Command
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.remote.datasource.FirebaseCommandDataSource
import com.tfkcolin.josyandchris.ui.data.CommandSortOrder
import com.tfkcolin.josyandchris.util.query.CursorConfig
import com.tfkcolin.josyandchris.util.query.DateRange
import com.tfkcolin.josyandchris.util.query.QueryFilters
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Unit tests for FirebaseCommandRepositoryImpl query operations
 * Tests the repository layer mapping and data source integration
 */
class FirebaseCommandRepositoryImplTest {

    private lateinit var mockDataSource: FirebaseCommandDataSource
    private lateinit var repository: FirebaseCommandRepositoryImpl

    private val sampleCommand = Command(
        id = "test-command-id",
        client = mapOf(
            "name" to "John Doe",
            "tel" to "1234567890",
            "country" to "USA",
            "city" to "New York"
        ),
        commandStepIndex = CommandStep.BUYING.ordinal,
        created = Date(),
        proofUploaded = true
    )

    @Before
    fun setup() {
        mockDataSource = mockk()
        repository = FirebaseCommandRepositoryImpl(mockDataSource)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `getCommandsPagingDataWithFilters maps sort order to cursor config correctly`() = runTest {
        // Arrange
        val mockPagingData = mockk<PagingData<Command>>()
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(
                any<QueryFilters>(), 
                any<CursorConfig>()
            ) 
        } returns flowOf(mockPagingData)

        val filters = QueryFilters(
            additionalFilters = mapOf(
                "sortOrder" to CommandSortOrder.CLIENT_NAME_ASC,
                "client.name" to "John Doe"
            )
        )

        // Act
        repository.getCommandsPagingDataWithFilters(filters)

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { queryFilters ->
                    // Verify sortOrder is removed from filters passed to data source
                    !queryFilters.additionalFilters.containsKey("sortOrder") &&
                    queryFilters.additionalFilters["client.name"] == "John Doe"
                },
                match { cursorConfig ->
                    cursorConfig.orderBy == "client.name" &&
                    cursorConfig.orderDirection == Query.Direction.ASCENDING
                }
            )
        }
    }

    @Test
    fun `getCommandsPagingDataWithFilters handles different sort orders correctly`() = runTest {
        // Test cases for different sort orders
        val testCases = mapOf(
            CommandSortOrder.CREATED_ASC to ("created" to Query.Direction.ASCENDING),
            CommandSortOrder.CREATED_DESC to ("created" to Query.Direction.DESCENDING),
            CommandSortOrder.CLIENT_NAME_ASC to ("client.name" to Query.Direction.ASCENDING),
            CommandSortOrder.CLIENT_NAME_DESC to ("client.name" to Query.Direction.DESCENDING),
            CommandSortOrder.STATUS_ASC to ("commandStepIndex" to Query.Direction.ASCENDING),
            CommandSortOrder.STATUS_DESC to ("commandStepIndex" to Query.Direction.DESCENDING)
        )

        testCases.forEach { (sortOrder, expectedConfig) ->
            clearMocks(mockDataSource)
            every { 
                mockDataSource.getCommandsPagingDataWithFilters(any(), any()) 
            } returns flowOf(mockk())

            val filters = QueryFilters(
                additionalFilters = mapOf("sortOrder" to sortOrder)
            )

            // Act
            repository.getCommandsPagingDataWithFilters(filters)

            // Assert
            verify {
                mockDataSource.getCommandsPagingDataWithFilters(
                    any(),
                    match { cursorConfig ->
                        cursorConfig.orderBy == expectedConfig.first &&
                        cursorConfig.orderDirection == expectedConfig.second
                    }
                )
            }
        }
    }

    @Test
    fun `getCommandsPagingDataWithFiltersAndSorting creates correct query filters`() = runTest {
        // Arrange
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(any(), any()) 
        } returns flowOf(mockk())

        val clientName = "John Doe"
        val phone = "1234567890"
        val country = "USA"
        val city = "New York"
        val statusIndex = CommandStep.BUYING.ordinal
        val searchTerm = "test search"
        val dateRange = DateRange(Date(0), Date())
        val proofUploaded = true
        val sortOrder = CommandSortOrder.CLIENT_NAME_ASC

        // Act
        repository.getCommandsPagingDataWithFiltersAndSorting(
            clientName = clientName,
            phone = phone,
            country = country,
            city = city,
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            dateRange = dateRange,
            proofUploaded = proofUploaded,
            sortOrder = sortOrder
        )

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { filters ->
                    filters.statusIndex == statusIndex &&
                    filters.searchTerm == searchTerm &&
                    filters.dateRange == dateRange &&
                    filters.additionalFilters["client.name"] == clientName &&
                    filters.additionalFilters["client.tel"] == phone &&
                    filters.additionalFilters["client.country"] == country &&
                    filters.additionalFilters["client.city"] == city &&
                    filters.additionalFilters["proofUploaded"] == proofUploaded
                },
                match { cursor ->
                    cursor.orderBy == "client.name" &&
                    cursor.orderDirection == Query.Direction.ASCENDING
                }
            )
        }
    }

    @Test
    fun `getCommandsPagingDataWithFiltersAndSorting ignores null and blank values`() = runTest {
        // Arrange
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(any(), any()) 
        } returns flowOf(mockk())

        // Act - pass null and blank values
        repository.getCommandsPagingDataWithFiltersAndSorting(
            clientName = null,
            phone = "",
            country = "  ", // Whitespace only
            city = null,
            statusIndex = null,
            searchTerm = "",
            dateRange = null,
            proofUploaded = null,
            sortOrder = CommandSortOrder.CREATED_DESC
        )

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { filters ->
                    filters.statusIndex == null &&
                    filters.searchTerm == null &&
                    filters.dateRange == null &&
                    filters.additionalFilters.isEmpty() // No filters should be added
                },
                any()
            )
        }
    }

    @Test
    fun `getCommandsByClient creates correct multi-criteria query`() = runTest {
        // Arrange
        val clientName = "John Doe"
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsByMultipleCriteria(
                clientName = clientName,
                limit = 100
            ) 
        } returns Result.success(expectedCommands)

        // Act
        val result = repository.getCommandsByClient(clientName)

        // Assert
        assertTrue(result.isSuccess)
        assertEquals(expectedCommands, result.getOrNull())
        verify { mockDataSource.getCommandsByMultipleCriteria(clientName = clientName, limit = 100) }
    }

    @Test
    fun `getCommandsByStatus creates correct multi-criteria query`() = runTest {
        // Arrange
        val statusIndex = CommandStep.READY.ordinal
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsByMultipleCriteria(
                statusIndex = statusIndex,
                limit = 100
            ) 
        } returns Result.success(expectedCommands)

        // Act
        val result = repository.getCommandsByStatus(statusIndex)

        // Assert
        assertTrue(result.isSuccess)
        assertEquals(expectedCommands, result.getOrNull())
        verify { mockDataSource.getCommandsByMultipleCriteria(statusIndex = statusIndex, limit = 100) }
    }

    @Test
    fun `getCommandsByClientFlow creates correct query filters`() = runTest {
        // Arrange
        val clientName = "John Doe"
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsFlowWithFilters(any()) 
        } returns flowOf(expectedCommands)

        // Act
        repository.getCommandsByClientFlow(clientName)

        // Assert
        verify {
            mockDataSource.getCommandsFlowWithFilters(
                match { filters ->
                    filters.additionalFilters["client.name"] == clientName
                }
            )
        }
    }

    @Test
    fun `getCommandsByStatusFlow creates correct query filters`() = runTest {
        // Arrange
        val statusIndex = CommandStep.READY.ordinal
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsFlowWithFilters(any()) 
        } returns flowOf(expectedCommands)

        // Act
        repository.getCommandsByStatusFlow(statusIndex)

        // Assert
        verify {
            mockDataSource.getCommandsFlowWithFilters(
                match { filters ->
                    filters.statusIndex == statusIndex
                }
            )
        }
    }

    @Test
    fun `searchCommands creates correct multi-criteria query`() = runTest {
        // Arrange
        val searchTerm = "john doe"
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsByMultipleCriteria(
                searchTerm = searchTerm,
                limit = 100
            ) 
        } returns Result.success(expectedCommands)

        // Act
        val result = repository.searchCommands(searchTerm)

        // Assert
        assertTrue(result.isSuccess)
        assertEquals(expectedCommands, result.getOrNull())
        verify { mockDataSource.getCommandsByMultipleCriteria(searchTerm = searchTerm, limit = 100) }
    }

    @Test
    fun `searchCommandsFlow creates correct query filters`() = runTest {
        // Arrange
        val searchTerm = "search query"
        val expectedCommands = listOf(sampleCommand)
        every { 
            mockDataSource.getCommandsFlowWithFilters(any()) 
        } returns flowOf(expectedCommands)

        // Act
        repository.searchCommandsFlow(searchTerm)

        // Assert
        verify {
            mockDataSource.getCommandsFlowWithFilters(
                match { filters ->
                    filters.searchTerm == searchTerm
                }
            )
        }
    }

    @Test
    fun `getCommandsPagingDataWithSearch creates correct filters`() = runTest {
        // Arrange
        val searchTerm = "test search"
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(any()) 
        } returns flowOf(mockk())

        // Act
        repository.getCommandsPagingDataWithSearch(searchTerm)

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { filters ->
                    filters.searchTerm == searchTerm
                }
            )
        }
    }

    @Test
    fun `getCommandsPagingDataWithStatus creates correct filters`() = runTest {
        // Arrange
        val statusIndex = CommandStep.BUYING.ordinal
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(any()) 
        } returns flowOf(mockk())

        // Act
        repository.getCommandsPagingDataWithStatus(statusIndex)

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { filters ->
                    filters.statusIndex == statusIndex
                }
            )
        }
    }

    @Test
    fun `repository getCommandsPagingDataWithFilters creates correct combined filters`() = runTest {
        // Arrange
        val statusIndex = CommandStep.READY.ordinal
        val searchTerm = "test"
        val clientName = "John Doe"
        every { 
            mockDataSource.getCommandsPagingDataWithFilters(any()) 
        } returns flowOf(mockk())

        // Act
        repository.getCommandsPagingDataWithFilters(
            statusIndex = statusIndex,
            searchTerm = searchTerm,
            clientName = clientName
        )

        // Assert
        verify {
            mockDataSource.getCommandsPagingDataWithFilters(
                match { filters ->
                    filters.statusIndex == statusIndex &&
                    filters.searchTerm == searchTerm &&
                    filters.additionalFilters["client.name"] == clientName
                }
            )
        }
    }

    @Test
    fun `aggregate count operations delegate to data source correctly`() = runTest {
        // Arrange
        every { mockDataSource.getCommandsCount() } returns Result.success(100L)
        every { mockDataSource.getCommandsCountByStatus(any()) } returns Result.success(50L)
        every { mockDataSource.getCommandsCountByAllStatuses() } returns Result.success(mapOf(0 to 25L, 1 to 25L))
        every { mockDataSource.getCommandsCountByCountry(any()) } returns Result.success(30L)
        every { mockDataSource.clientSuggestionsFlow(any()) } returns flowOf(listOf("John", "Jane"))
        every { mockDataSource.getCommandsCountByAllStatusesFlow() } returns flowOf(mapOf(0 to 25L, 1 to 25L))

        // Act & Assert
        val totalCount = repository.getCommandsCount()
        assertTrue(totalCount.isSuccess)
        assertEquals(100L, totalCount.getOrNull())

        val statusCount = repository.getCommandsCountByStatus(1)
        assertTrue(statusCount.isSuccess)
        assertEquals(50L, statusCount.getOrNull())

        val allStatusCounts = repository.getCommandsCountByAllStatuses()
        assertTrue(allStatusCounts.isSuccess)
        assertEquals(mapOf(0 to 25L, 1 to 25L), allStatusCounts.getOrNull())

        val countryCount = repository.getCommandsCountByCountry("USA")
        assertTrue(countryCount.isSuccess)
        assertEquals(30L, countryCount.getOrNull())

        // Verify all calls were made to data source
        verify { mockDataSource.getCommandsCount() }
        verify { mockDataSource.getCommandsCountByStatus(1) }
        verify { mockDataSource.getCommandsCountByAllStatuses() }
        verify { mockDataSource.getCommandsCountByCountry("USA") }
    }

    @Test
    fun `CRUD operations delegate to data source correctly`() = runTest {
        // Arrange
        every { mockDataSource.createCommand(any()) } returns Result.success("new-id")
        every { mockDataSource.getCommand(any()) } returns Result.success(sampleCommand)
        every { mockDataSource.updateCommand(any()) } returns Result.success(Unit)
        every { mockDataSource.deleteCommand(any()) } returns Result.success(Unit)
        every { mockDataSource.getCommandsFlow() } returns flowOf(listOf(sampleCommand))
        every { mockDataSource.getCommandsPagingData() } returns flowOf(mockk())

        // Act
        val createResult = repository.createCommand(sampleCommand)
        val getResult = repository.getCommand("test-id")
        val updateResult = repository.updateCommand(sampleCommand)
        val deleteResult = repository.deleteCommand("test-id")

        // Assert
        assertTrue(createResult.isSuccess)
        assertEquals("new-id", createResult.getOrNull())
        
        assertTrue(getResult.isSuccess)
        assertEquals(sampleCommand, getResult.getOrNull())
        
        assertTrue(updateResult.isSuccess)
        assertTrue(deleteResult.isSuccess)

        // Verify all calls
        verify { mockDataSource.createCommand(sampleCommand) }
        verify { mockDataSource.getCommand("test-id") }
        verify { mockDataSource.updateCommand(sampleCommand) }
        verify { mockDataSource.deleteCommand("test-id") }
    }
}
