package com.tfkcolin.josyandchris.ui.screens.shipmentdetails

import androidx.lifecycle.SavedStateHandle
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.tfkcolin.josyandchris.data.Shipment
import com.tfkcolin.josyandchris.data.ShipmentProduct
import com.tfkcolin.josyandchris.data.ShipmentStatus
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

@ExperimentalCoroutinesApi
class ShipmentDetailsViewModelTest {

    private lateinit var viewModel: ShipmentDetailsViewModel
    private lateinit var savedStateHandle: SavedStateHandle
    private lateinit var shipmentDB: CollectionReference
    private lateinit var documentRef: DocumentReference

    private val testShipmentId = "test_shipment_123"
    private val testCargoId = "test_cargo_456"

    @Before
    fun setup() {
        savedStateHandle = mockk {
            every { get<String>(shipmentIdArgs) } returns testShipmentId
            every { get<String>(cargoIdArgs) } returns testCargoId
        }

        documentRef = mockk(relaxed = true) {
            every { id } returns testShipmentId
        }
        
        shipmentDB = mockk {
            every { document(testShipmentId) } returns documentRef
            every { document() } returns documentRef
        }

        viewModel = ShipmentDetailsViewModel(savedStateHandle, shipmentDB)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `viewModel should properly initialize with cargoId from SavedStateHandle`() {
        assertEquals(testCargoId, viewModel.cargoId)
        assertEquals(testShipmentId, viewModel.shipmentId)
        assertFalse(viewModel.isCreate)
    }

    @Test
    fun `viewModel should identify create mode when shipmentId is empty`() {
        val createSavedStateHandle = mockk<SavedStateHandle> {
            every { get<String>(shipmentIdArgs) } returns ""
            every { get<String>(cargoIdArgs) } returns testCargoId
        }
        
        val createViewModel = ShipmentDetailsViewModel(createSavedStateHandle, shipmentDB)
        
        assertTrue(createViewModel.isCreate)
        assertEquals(testCargoId, createViewModel.cargoId)
    }

    @Test
    fun `updateShipmentSync should set cargoId when creating new shipment`() = runTest {
        // Setup create mode
        val createSavedStateHandle = mockk<SavedStateHandle> {
            every { get<String>(shipmentIdArgs) } returns ""
            every { get<String>(cargoIdArgs) } returns testCargoId
        }
        
        val newDocRef = mockk<DocumentReference>(relaxed = true) {
            every { id } returns "new_shipment_id"
        }
        
        val createShipmentDB = mockk<CollectionReference> {
            every { document() } returns newDocRef
        }
        
        val createViewModel = ShipmentDetailsViewModel(createSavedStateHandle, createShipmentDB)
        
        val newShipment = Shipment(
            clientName = "New Client",
            clientPhone = "+237 656 789 012",
            weightKg = 50.0,
            volumeCbm = 1.5,
            amountDue = 200.0,
            statusIndex = ShipmentStatus.RECEIVED_AT_ORIGIN.ordinal
        )

        // Capture the shipment being saved
        val slot = slot<Shipment>()
        coEvery { 
            com.google.android.gms.tasks.Tasks.await(newDocRef.set(capture(slot)), any(), any()) 
        } returns mockk()

        // Execute
        createViewModel.updateShipmentSync(newShipment)

        // Verify the saved shipment has correct cargoId and id
        val savedShipment = slot.captured
        assertEquals(testCargoId, savedShipment.cargoId)
        assertEquals("new_shipment_id", savedShipment.id)
        assertEquals("New Client", savedShipment.clientName)
    }

    @Test
    fun `updateShipmentSync should not modify cargoId when updating existing shipment`() = runTest {
        val existingShipment = Shipment(
            id = testShipmentId,
            cargoId = testCargoId,
            clientName = "Updated Client",
            clientPhone = "+237 656 321 654",
            weightKg = 60.0,
            volumeCbm = 2.0,
            amountDue = 300.0,
            statusIndex = ShipmentStatus.IN_CARGO.ordinal
        )

        // Capture the shipment being saved
        val slot = slot<Shipment>()
        coEvery { 
            com.google.android.gms.tasks.Tasks.await(documentRef.set(capture(slot)), any(), any()) 
        } returns mockk()

        // Execute
        viewModel.updateShipmentSync(existingShipment)

        // Verify the saved shipment maintains its data
        val savedShipment = slot.captured
        assertEquals(testShipmentId, savedShipment.id)
        assertEquals(testCargoId, savedShipment.cargoId)
        assertEquals("Updated Client", savedShipment.clientName)
    }

    @Test
    fun `newProduct should be properly initialized and reset`() {
        // Initial state
        assertEquals("", viewModel.newProduct.name)
        assertEquals(0, viewModel.newProduct.quantity)
        assertEquals("", viewModel.newProduct.description)

        // Update product
        val testProduct = ShipmentProduct(
            name = "Test Product",
            quantity = 5,
            description = "Test Description"
        )
        viewModel.updateNewProduct(testProduct)

        assertEquals("Test Product", viewModel.newProduct.name)
        assertEquals(5, viewModel.newProduct.quantity)
        assertEquals("Test Description", viewModel.newProduct.description)

        // Reset product
        viewModel.resetNewProduct()

        assertEquals("", viewModel.newProduct.name)
        assertEquals(0, viewModel.newProduct.quantity)
        assertEquals("", viewModel.newProduct.description)
    }

    @Test
    fun `deleteShipmentSync should call delete on correct document`() = runTest {
        coEvery { 
            com.google.android.gms.tasks.Tasks.await(documentRef.delete(), any(), any()) 
        } returns mockk()

        // Execute
        viewModel.deleteShipmentSync()

        // Verify delete was called on the correct document
        verify { documentRef.delete() }
    }

    @Test
    fun `changeMade state should be properly managed`() {
        // Initial state
        assertFalse(viewModel.changeMade.value)

        // Set change made
        viewModel.setChangeMade(true)
        assertTrue(viewModel.changeMade.value)

        // Reset change made
        viewModel.setChangeMade(false)
        assertFalse(viewModel.changeMade.value)
    }

    @Test
    fun `viewModel should handle shipment with products correctly`() = runTest {
        val products = listOf(
            ShipmentProduct(name = "Product 1", quantity = 2, description = "Desc 1"),
            ShipmentProduct(name = "Product 2", quantity = 3, description = "Desc 2")
        )

        val shipmentWithProducts = Shipment(
            id = testShipmentId,
            cargoId = testCargoId,
            clientName = "Client with Products",
            clientPhone = "+237 656 111 222",
            weightKg = 75.0,
            volumeCbm = 2.5,
            amountDue = 500.0,
            statusIndex = ShipmentStatus.IN_CARGO.ordinal,
            products = products
        )

        // Capture the shipment being saved
        val slot = slot<Shipment>()
        coEvery { 
            com.google.android.gms.tasks.Tasks.await(documentRef.set(capture(slot)), any(), any()) 
        } returns mockk()

        // Execute
        viewModel.updateShipmentSync(shipmentWithProducts)

        // Verify products are preserved
        val savedShipment = slot.captured
        assertEquals(2, savedShipment.products.size)
        assertEquals("Product 1", savedShipment.products[0].name)
        assertEquals(2, savedShipment.products[0].quantity)
        assertEquals("Product 2", savedShipment.products[1].name)
        assertEquals(3, savedShipment.products[1].quantity)
    }
}
