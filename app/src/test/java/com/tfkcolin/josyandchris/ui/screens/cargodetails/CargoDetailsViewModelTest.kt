package com.tfkcolin.josyandchris.ui.screens.cargodetails

import androidx.lifecycle.SavedStateHandle
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.EventListener
import com.google.firebase.firestore.FirebaseFirestoreException
import com.google.firebase.firestore.ListenerRegistration
import com.tfkcolin.josyandchris.data.Cargo
import com.tfkcolin.josyandchris.data.CargoStatus
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

@ExperimentalCoroutinesApi
class CargoDetailsViewModelTest {

    private lateinit var viewModel: CargoDetailsViewModel
    private lateinit var savedStateHandle: SavedStateHandle
    private lateinit var cargoDB: CollectionReference
    private lateinit var shipmentDB: CollectionReference
    private lateinit var documentRef: DocumentReference
    private lateinit var listenerRegistration: ListenerRegistration

    private val testCargoId = "test_cargo_123"
    private val testDispatcher = UnconfinedTestDispatcher()

    @Before
    fun setup() {
        savedStateHandle = mockk {
            every { get<String>(cargoIdArgs) } returns testCargoId
            every { get<Boolean>(isCreateArgs) } returns false
        }

        documentRef = mockk(relaxed = true)
        listenerRegistration = mockk(relaxed = true)
        
        cargoDB = mockk {
            every { document(testCargoId) } returns documentRef
            every { document() } returns documentRef
        }
        
        shipmentDB = mockk(relaxed = true)

        viewModel = CargoDetailsViewModel(savedStateHandle, cargoDB, shipmentDB)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `initial state should have live mode disabled`() = runTest {
        assertFalse(viewModel.isLiveMode.value)
        assertNull(viewModel.currentCargo.value)
    }

    @Test
    fun `toggleLiveMode should enable live updates when off`() = runTest {
        // Setup mock listener
        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        // Toggle live mode on
        viewModel.toggleLiveMode()

        // Verify live mode is enabled
        assertTrue(viewModel.isLiveMode.value)

        // Verify listener was added
        verify { documentRef.addSnapshotListener(any()) }
    }

    @Test
    fun `toggleLiveMode should disable live updates when on`() = runTest {
        // First enable live mode
        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        viewModel.toggleLiveMode()
        assertTrue(viewModel.isLiveMode.value)

        // Now disable it
        viewModel.toggleLiveMode()

        // Verify live mode is disabled
        assertFalse(viewModel.isLiveMode.value)
    }

    @Test
    fun `startLiveUpdates should not start if already in live mode`() = runTest {
        // Enable live mode first
        every { 
            documentRef.addSnapshotListener(any()) 
        } returns listenerRegistration

        viewModel.startLiveUpdates()
        clearMocks(documentRef, answers = false)

        // Try to start again
        viewModel.startLiveUpdates()

        // Verify listener was not added again
        verify(exactly = 0) { documentRef.addSnapshotListener(any()) }
    }

    @Test
    fun `startLiveUpdates should not start in create mode`() = runTest {
        // Create a viewModel in create mode
        val createSavedStateHandle = mockk<SavedStateHandle> {
            every { get<String>(cargoIdArgs) } returns ""
            every { get<Boolean>(isCreateArgs) } returns true
        }
        
        val createViewModel = CargoDetailsViewModel(createSavedStateHandle, cargoDB, shipmentDB)

        createViewModel.startLiveUpdates()

        // Verify listener was not added
        verify(exactly = 0) { documentRef.addSnapshotListener(any()) }
        assertFalse(createViewModel.isLiveMode.value)
    }

    @Test
    fun `live updates should update currentCargo when document changes`() = runTest {
        val testCargo = Cargo(
            id = testCargoId,
            origin = "Yaoundé",
            destination = "Paris",
            statusIndex = CargoStatus.IN_TRANSIT.ordinal
        )

        val snapshot = mockk<DocumentSnapshot> {
            every { exists() } returns true
            every { id } returns testCargoId
            every { toObject(Cargo::class.java) } returns testCargo
        }

        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        // Start live updates
        viewModel.startLiveUpdates()

        // Simulate document update
        slot.captured.onEvent(snapshot, null)

        // Verify currentCargo was updated
        val currentCargo = viewModel.currentCargo.first()
        assertNotNull(currentCargo)
        assertEquals(testCargoId, currentCargo?.id)
        assertEquals("Yaoundé", currentCargo?.origin)
        assertEquals("Paris", currentCargo?.destination)
    }

    @Test
    fun `live updates should handle document deletion`() = runTest {
        val snapshot = mockk<DocumentSnapshot> {
            every { exists() } returns false
        }

        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        // Start live updates
        viewModel.startLiveUpdates()

        // Simulate document deletion
        slot.captured.onEvent(snapshot, null)

        // Verify currentCargo was set to null
        assertNull(viewModel.currentCargo.value)
    }

    @Test
    fun `live updates should handle errors`() = runTest {
        val error = mockk<FirebaseFirestoreException>()

        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        // Start live updates
        viewModel.startLiveUpdates()

        // Simulate error
        slot.captured.onEvent(null, error)

        // Verify live mode is still enabled (error doesn't disable it)
        assertTrue(viewModel.isLiveMode.value)
    }

    @Test
    fun `stopLiveUpdates should cancel listener and disable live mode`() = runTest {
        // First start live updates
        every { 
            documentRef.addSnapshotListener(any()) 
        } returns listenerRegistration

        viewModel.startLiveUpdates()
        assertTrue(viewModel.isLiveMode.value)

        // Stop live updates
        viewModel.stopLiveUpdates()

        // Verify live mode is disabled
        assertFalse(viewModel.isLiveMode.value)
    }

    @Test
    fun `deleteCargoSync should delete all shipments and cargo`() = runTest {
        val shipmentQuery = mockk<com.google.firebase.firestore.Query> {
            every { whereEqualTo("cargoId", testCargoId) } returns this
        }
        
        val shipmentSnapshot = mockk<com.google.firebase.firestore.QuerySnapshot> {
            every { documents } returns listOf(
                mockk { every { id } returns "shipment1" },
                mockk { every { id } returns "shipment2" }
            )
        }

        every { shipmentDB.whereEqualTo("cargoId", testCargoId) } returns shipmentQuery
        coEvery { com.google.android.gms.tasks.Tasks.await(shipmentQuery.get()) } returns shipmentSnapshot
        coEvery { com.google.android.gms.tasks.Tasks.await(shipmentDB.document(any()).delete()) } returns mockk()
        coEvery { com.google.android.gms.tasks.Tasks.await(documentRef.delete()) } returns mockk()

        // Delete cargo
        viewModel.deleteCargoSync()

        // Verify shipments were deleted first
        verify { shipmentDB.document("shipment1").delete() }
        verify { shipmentDB.document("shipment2").delete() }

        // Verify cargo was deleted
        verify { documentRef.delete() }
    }

    @Test
    fun `getCurrentCargo should return live value when in live mode`() = runTest {
        val testCargo = Cargo(
            id = testCargoId,
            origin = "Douala",
            destination = "Lyon"
        )

        // Enable live mode and set current cargo
        viewModel.startLiveUpdates()
        
        // Use reflection or a test-specific method to set the current cargo
        // Since _currentCargo is private, we'll test through the live update mechanism
        val snapshot = mockk<DocumentSnapshot> {
            every { exists() } returns true
            every { id } returns testCargoId
            every { toObject(Cargo::class.java) } returns testCargo
        }

        val slot = slot<EventListener<DocumentSnapshot>>()
        every { 
            documentRef.addSnapshotListener(capture(slot)) 
        } returns listenerRegistration

        viewModel.startLiveUpdates()
        slot.captured.onEvent(snapshot, null)

        // Get current cargo
        val result = viewModel.getCurrentCargo()

        assertNotNull(result)
        assertEquals("Douala", result?.origin)
        assertEquals("Lyon", result?.destination)
    }

    @Test
    fun `getCurrentCargo should fetch from Firestore when not in live mode`() = runTest {
        val testCargo = Cargo(
            id = testCargoId,
            origin = "Bafoussam",
            destination = "Marseille"
        )

        val snapshot = mockk<DocumentSnapshot> {
            every { id } returns testCargoId
            every { toObject(Cargo::class.java) } returns testCargo
        }

        coEvery { 
            com.google.android.gms.tasks.Tasks.await(documentRef.get(), any(), any()) 
        } returns snapshot

        // Get current cargo without live mode
        val result = viewModel.getCurrentCargo()

        assertNotNull(result)
        assertEquals("Bafoussam", result?.origin)
        assertEquals("Marseille", result?.destination)

        // Verify get() was called
        verify { documentRef.get() }
    }
}
