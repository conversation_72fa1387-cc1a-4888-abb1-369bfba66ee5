package com.tfkcolin.josyandchris.ui.data

import com.tfkcolin.josyandchris.data.CargoStatus
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.data.TransactionType
import com.tfkcolin.josyandchris.util.query.DateRange
import org.junit.Test
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * Unit tests for UI filter state to QueryFilters mapping logic
 */
class UiFilterStateTest {

    @Test
    fun `CommandUiFilterState toQueryFilters maps all fields correctly`() {
        // Arrange
        val dateRange = DateRange(Date(0), Date())
        val filterState = CommandUiFilterState(
            selectedStep = CommandStep.BUYING,
            searchTerm = "test search",
            proofUploaded = true,
            paymentStatus = false,
            dateRange = dateRange,
            clientName = "John Doe",
            clientPhone = "1234567890",
            clientCountry = "USA",
            clientCity = "New York",
            sortOrder = CommandSortOrder.CLIENT_NAME_ASC
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertEquals(CommandStep.BUYING.ordinal, queryFilters.statusIndex)
        assertEquals("test search", queryFilters.searchTerm)
        assertEquals(dateRange, queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals(true, additionalFilters["proofUploaded"])
        assertEquals("John Doe", additionalFilters["client.name"])
        assertEquals("1234567890", additionalFilters["client.tel"])
        assertEquals("USA", additionalFilters["client.country"])
        assertEquals("New York", additionalFilters["client.city"])
        assertEquals(CommandSortOrder.CLIENT_NAME_ASC, additionalFilters["sortOrder"])
    }

    @Test
    fun `CommandUiFilterState handles legacy field name compatibility`() {
        // Arrange - using legacy field names
        val filterState = CommandUiFilterState(
            phone = "legacy_phone",
            country = "legacy_country",
            city = "legacy_city",
            clientPhone = "", // Empty new field names
            clientCountry = "",
            clientCity = ""
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("legacy_phone", additionalFilters["client.tel"])
        assertEquals("legacy_country", additionalFilters["client.country"])
        assertEquals("legacy_city", additionalFilters["client.city"])
    }

    @Test
    fun `CommandUiFilterState new fields take priority over legacy fields`() {
        // Arrange - both legacy and new field names
        val filterState = CommandUiFilterState(
            phone = "legacy_phone",
            country = "legacy_country",
            city = "legacy_city",
            clientPhone = "new_phone",
            clientCountry = "new_country",
            clientCity = "new_city"
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("new_phone", additionalFilters["client.tel"])
        assertEquals("new_country", additionalFilters["client.country"])
        assertEquals("new_city", additionalFilters["client.city"])
    }

    @Test
    fun `CommandUiFilterState ignores empty fields`() {
        // Arrange
        val filterState = CommandUiFilterState(
            searchTerm = "",
            clientName = "  ", // Whitespace only
            clientPhone = "",
            clientCountry = "",
            clientCity = ""
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertNull(queryFilters.searchTerm)
        assertTrue(queryFilters.additionalFilters.isEmpty())
    }

    @Test
    fun `CargoUiFilterState toQueryFilters maps correctly`() {
        // Arrange
        val dateRange = DateRange(Date(0), Date())
        val filterState = CargoUiFilterState(
            selectedStatus = CargoStatus.IN_TRANSIT,
            searchTerm = "cargo search",
            originFilter = "Origin City",
            destinationFilter = "Destination City",
            dateRange = dateRange
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertEquals(CargoStatus.IN_TRANSIT.ordinal, queryFilters.statusIndex)
        assertEquals("cargo search", queryFilters.searchTerm)
        assertEquals(dateRange, queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("Origin City", additionalFilters["origin"])
        assertEquals("Destination City", additionalFilters["destination"])
    }

    @Test
    fun `CargoUiFilterState handles empty filters`() {
        // Arrange
        val filterState = CargoUiFilterState(
            searchTerm = "",
            originFilter = "",
            destinationFilter = ""
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertNull(queryFilters.statusIndex)
        assertNull(queryFilters.searchTerm)
        assertNull(queryFilters.dateRange)
        assertTrue(queryFilters.additionalFilters.isEmpty())
    }

    @Test
    fun `ShipmentUiFilterState toQueryFilters maps correctly`() {
        // Arrange
        val dateRange = DateRange(Date(0), Date())
        val filterState = ShipmentUiFilterState(
            selectedStatus = 1,
            searchTerm = "shipment search",
            cargoId = "cargo123",
            clientName = "Client Name",
            clientPhone = "9876543210",
            dateRange = dateRange
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertEquals(1, queryFilters.statusIndex)
        assertEquals("shipment search", queryFilters.searchTerm)
        assertEquals(dateRange, queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("cargo123", additionalFilters["cargoId"])
        assertEquals("Client Name", additionalFilters["clientName"])
        assertEquals("9876543210", additionalFilters["clientPhone"])
    }

    @Test
    fun `TransactionUiFilterState toQueryFilters maps correctly`() {
        // Arrange
        val dateRange = DateRange(Date(0), Date())
        val filterState = TransactionUiFilterState(
            selectedTransactionType = TransactionType.INCOME,
            searchTerm = "transaction search",
            country = "Country",
            commandId = "cmd123",
            marked = true,
            selectedMonth = 5,
            selectedYear = 2023,
            dateRange = dateRange
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertEquals(TransactionType.INCOME.ordinal, queryFilters.statusIndex)
        assertEquals("transaction search", queryFilters.searchTerm)
        assertEquals(dateRange, queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("Country", additionalFilters["country"])
        assertEquals("cmd123", additionalFilters["commandId"])
        assertEquals(true, additionalFilters["marked"])
        assertEquals(5, additionalFilters["selectedMonth"])
        assertEquals(2023, additionalFilters["selectedYear"])
    }

    @Test
    fun `CountryUiFilterState toQueryFilters maps correctly`() {
        // Arrange
        val searchTokens = listOf("token1", "token2", "token3")
        val filterState = CountryUiFilterState(
            searchTerm = "country search",
            name = "Country Name",
            devise = "Currency",
            searchTokens = searchTokens
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertNull(queryFilters.statusIndex)
        assertEquals("country search", queryFilters.searchTerm)
        assertNull(queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("Country Name", additionalFilters["name"])
        assertEquals("Currency", additionalFilters["devise"])
        assertEquals(searchTokens, additionalFilters["searchTokens"])
    }

    @Test
    fun `ImageDataUiFilterState toQueryFilters maps correctly`() {
        // Arrange
        val filterState = ImageDataUiFilterState(
            searchTerm = "image search",
            category = "Category",
            genre = "Genre",
            uploaded = true
        )

        // Act
        val queryFilters = filterState.toQueryFilters()

        // Assert
        assertNull(queryFilters.statusIndex)
        assertEquals("image search", queryFilters.searchTerm)
        assertNull(queryFilters.dateRange)
        
        val additionalFilters = queryFilters.additionalFilters
        assertEquals("Category", additionalFilters["category"])
        assertEquals("Genre", additionalFilters["genre"])
        assertEquals(true, additionalFilters["upload"])
    }

    @Test
    fun `CommandSortOrder enum values are correctly defined`() {
        // Assert all expected sort order values exist
        val sortOrders = CommandSortOrder.entries
        assertTrue(sortOrders.contains(CommandSortOrder.CREATED_DESC))
        assertTrue(sortOrders.contains(CommandSortOrder.CREATED_ASC))
        assertTrue(sortOrders.contains(CommandSortOrder.CLIENT_NAME_ASC))
        assertTrue(sortOrders.contains(CommandSortOrder.CLIENT_NAME_DESC))
        assertTrue(sortOrders.contains(CommandSortOrder.STATUS_ASC))
        assertTrue(sortOrders.contains(CommandSortOrder.STATUS_DESC))
    }

    @Test
    fun `blank search terms are filtered out correctly`() {
        // Test various blank scenarios
        val testCases = listOf(
            "",
            " ",
            "  ",
            "\t",
            "\n",
            " \t\n "
        )

        testCases.forEach { blankTerm ->
            val filterState = CommandUiFilterState(searchTerm = blankTerm)
            val queryFilters = filterState.toQueryFilters()
            assertNull(queryFilters.searchTerm, "Search term '$blankTerm' should be filtered out")
        }
    }

    @Test
    fun `valid search terms are preserved`() {
        val validTerms = listOf(
            "a",
            "valid search",
            "123",
            "<EMAIL>",
            " valid with spaces "
        )

        validTerms.forEach { validTerm ->
            val filterState = CommandUiFilterState(searchTerm = validTerm)
            val queryFilters = filterState.toQueryFilters()
            assertEquals(validTerm, queryFilters.searchTerm, "Valid search term '$validTerm' should be preserved")
        }
    }
}
