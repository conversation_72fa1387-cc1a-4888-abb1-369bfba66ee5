package com.tfkcolin.josyandchris.util.query

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.josyandchris.data.CommandStep
import com.tfkcolin.josyandchris.ui.data.CommandSortOrder
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.util.*
import kotlin.test.assertEquals

/**
 * Unit tests for CommandQueryBuilder filter mapper logic
 * Tests the mapping from UI filter state to Firestore queries
 */
@RunWith(RobolectricTestRunner::class)
class CommandQueryBuilderTest {

    private lateinit var mockFirestore: FirebaseFirestore
    private lateinit var mockCollectionReference: com.google.firebase.firestore.CollectionReference
    private lateinit var mockQuery: Query
    private lateinit var queryBuilder: CommandQueryBuilder

    @Before
    fun setup() {
        // Mock Firebase dependencies
        mockFirestore = mockk()
        mockCollectionReference = mockk()
        mockQuery = mockk()
        
        // Setup mock behavior
        every { mockFirestore.collection("commands") } returns mockCollectionReference
        every { mockCollectionReference.whereEqualTo(any<String>(), any()) } returns mockQuery
        every { mockCollectionReference.whereGreaterThanOrEqualTo(any<String>(), any()) } returns mockQuery
        every { mockCollectionReference.whereLessThan(any<String>(), any()) } returns mockQuery
        every { mockCollectionReference.whereArrayContainsAny(any<String>(), any<List<Any>>()) } returns mockQuery
        every { mockCollectionReference.orderBy(any<String>(), any<Query.Direction>()) } returns mockQuery
        every { mockCollectionReference.startAfter(any()) } returns mockQuery
        every { mockCollectionReference.limit(any()) } returns mockQuery
        
        every { mockQuery.whereEqualTo(any<String>(), any()) } returns mockQuery
        every { mockQuery.whereGreaterThanOrEqualTo(any<String>(), any()) } returns mockQuery
        every { mockQuery.whereLessThan(any<String>(), any()) } returns mockQuery
        every { mockQuery.whereArrayContainsAny(any<String>(), any<List<Any>>()) } returns mockQuery
        every { mockQuery.orderBy(any<String>(), any<Query.Direction>()) } returns mockQuery
        every { mockQuery.startAfter(any()) } returns mockQuery
        every { mockQuery.limit(any()) } returns mockQuery
        
        queryBuilder = CommandQueryBuilder(mockFirestore, "commands")
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `build query with status filter maps correctly`() {
        // Arrange
        val statusIndex = CommandStep.BUYING.ordinal
        val filters = QueryFilters(statusIndex = statusIndex)
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereEqualTo("commandStepIndex", statusIndex) }
        verify { mockQuery.orderBy("created", Query.Direction.DESCENDING) }
        verify { mockQuery.limit(20L) }
    }

    @Test
    fun `build query with date range filter maps correctly`() {
        // Arrange
        val startDate = Date(System.currentTimeMillis() - 86400000) // Yesterday
        val endDate = Date()
        val dateRange = DateRange(startDate, endDate)
        val filters = QueryFilters(dateRange = dateRange)
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereGreaterThanOrEqualTo("created", startDate) }
        verify { mockQuery.whereLessThan("created", endDate) }
    }

    @Test
    fun `build query with search term creates search tokens correctly`() {
        // Arrange
        val searchTerm = "John Doe"
        val expectedTokens = listOf("john", "doe")
        val filters = QueryFilters(searchTerm = searchTerm)
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereArrayContainsAny("searchIndex", expectedTokens) }
    }

    @Test
    fun `build query with search tokens takes priority over search term`() {
        // Arrange
        val searchTerm = "John Doe"
        val searchTokens = listOf("priority", "tokens")
        val filters = QueryFilters(
            searchTerm = searchTerm,
            searchTokens = searchTokens
        )
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereArrayContainsAny("searchIndex", searchTokens) }
        // Verify search term is not used when tokens are present
        verify(exactly = 0) { mockQuery.whereArrayContainsAny("searchIndex", listOf("john", "doe")) }
    }

    @Test
    fun `build query with client additional filters maps correctly`() {
        // Arrange
        val additionalFilters = mapOf(
            "client.name" to "John Doe",
            "client.tel" to "+1234567890",
            "client.country" to "USA",
            "client.city" to "New York",
            "proofUploaded" to true
        )
        val filters = QueryFilters(additionalFilters = additionalFilters)
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        additionalFilters.forEach { (field, value) ->
            verify { mockQuery.whereEqualTo(field, value) }
        }
    }

    @Test
    fun `build query with complex filters combines all conditions`() {
        // Arrange
        val statusIndex = CommandStep.READY.ordinal
        val dateRange = DateRange(Date(0), Date())
        val additionalFilters = mapOf(
            "client.name" to "Test Client",
            "proofUploaded" to false
        )
        val filters = QueryFilters(
            statusIndex = statusIndex,
            dateRange = dateRange,
            searchTerm = "test search",
            additionalFilters = additionalFilters
        )
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereEqualTo("commandStepIndex", statusIndex) }
        verify { mockQuery.whereGreaterThanOrEqualTo("created", dateRange.startDate) }
        verify { mockQuery.whereLessThan("created", dateRange.endDate) }
        verify { mockQuery.whereArrayContainsAny("searchIndex", listOf("test", "search")) }
        additionalFilters.forEach { (field, value) ->
            verify { mockQuery.whereEqualTo(field, value) }
        }
    }

    @Test
    fun `buildClientSearch creates correct filter`() {
        // Arrange
        val clientName = "John Smith"
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.buildClientSearch(clientName, cursor)

        // Assert
        verify { mockQuery.whereArrayContainsAny("searchIndex", listOf("john", "smith")) }
    }

    @Test
    fun `buildStatusFilter creates correct filter`() {
        // Arrange
        val statusIndex = CommandStep.OK.ordinal
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.buildStatusFilter(statusIndex, cursor)

        // Assert
        verify { mockQuery.whereEqualTo("commandStepIndex", statusIndex) }
    }

    @Test
    fun `buildPaymentProofFilter creates correct filter`() {
        // Arrange
        val proofUploaded = true
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.buildPaymentProofFilter(proofUploaded, cursor)

        // Assert
        verify { mockQuery.whereEqualTo("proofUploaded", proofUploaded) }
    }

    @Test
    fun `buildDateRangeAndStatusFilter combines date and status correctly`() {
        // Arrange
        val dateRange = DateRange(Date(0), Date())
        val statusIndex = CommandStep.BUYING.ordinal
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.buildDateRangeAndStatusFilter(dateRange, statusIndex, cursor)

        // Assert
        verify { mockQuery.whereEqualTo("commandStepIndex", statusIndex) }
        verify { mockQuery.whereGreaterThanOrEqualTo("created", dateRange.startDate) }
        verify { mockQuery.whereLessThan("created", dateRange.endDate) }
    }

    @Test
    fun `cursor config applies pagination correctly`() {
        // Arrange
        val mockDocumentSnapshot = mockk<com.google.firebase.firestore.DocumentSnapshot>()
        val cursor = CursorConfig(
            startAfter = mockDocumentSnapshot,
            limit = 50,
            orderBy = "client.name",
            orderDirection = Query.Direction.ASCENDING
        )
        val filters = QueryFilters()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.orderBy("client.name", Query.Direction.ASCENDING) }
        verify { mockQuery.startAfter(mockDocumentSnapshot) }
        verify { mockQuery.limit(50L) }
    }

    @Test
    fun `empty search term is ignored`() {
        // Arrange
        val filters = QueryFilters(searchTerm = "")
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify(exactly = 0) { mockQuery.whereArrayContainsAny(any<String>(), any<List<Any>>()) }
    }

    @Test
    fun `whitespace only search term is ignored`() {
        // Arrange
        val filters = QueryFilters(searchTerm = "   ")
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify(exactly = 0) { mockQuery.whereArrayContainsAny(any<String>(), any<List<Any>>()) }
    }

    @Test
    fun `search term with multiple spaces creates correct tokens`() {
        // Arrange
        val searchTerm = "  John   Doe  Smith  "
        val expectedTokens = listOf("john", "doe", "smith")
        val filters = QueryFilters(searchTerm = searchTerm)
        val cursor = CursorConfig()

        // Act
        val result = queryBuilder.build(filters, cursor)

        // Assert
        verify { mockQuery.whereArrayContainsAny("searchIndex", expectedTokens) }
    }
}
