package com.tfkcolin.josyandchris.util

import com.tfkcolin.josyandchris.domain.exception.DomainException
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import java.io.IOException
import java.net.UnknownHostException
import java.util.concurrent.TimeoutException

@ExperimentalCoroutinesApi
class CoroutineUtilsTest {

    private val testDispatcher = StandardTestDispatcher()

    @Test
    fun `safeCall should return success result when no exception is thrown`() = runTest(testDispatcher) {
        // Given
        val expectedValue = "Test Value"
        
        // When
        val result = safeCall {
            expectedValue
        }
        
        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedValue, result.getOrNull())
    }
    
    @Test
    fun `safeCall should return failure with NetworkException when IOException occurs`() = runTest(testDispatcher) {
        // Given
        val exception = IOException("Network error")
        
        // When
        val result = safeCall {
            throw exception
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.NetworkException)
        assertEquals(exception, error.cause)
    }
    
    @Test
    fun `safeCall should return failure with NetworkException when UnknownHostException occurs`() = runTest(testDispatcher) {
        // Given
        val exception = UnknownHostException("Unknown host")
        
        // When
        val result = safeCall {
            throw exception
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.NetworkException)
        assertEquals(exception, error.cause)
    }
    
    @Test
    fun `safeCall should return failure with NetworkException when TimeoutException occurs`() = runTest(testDispatcher) {
        // Given
        val exception = TimeoutException("Request timed out")
        
        // When
        val result = safeCall {
            throw exception
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.NetworkException)
        assertEquals(exception, error.cause)
    }
    
    @Test
    fun `safeCall should pass through DomainException without changing it`() = runTest(testDispatcher) {
        // Given
        val originalException = DomainException.StorageException.UploadException()
        
        // When
        val result = safeCall {
            throw originalException
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.StorageException.UploadException)
        assertEquals(originalException, error)
    }
    
    @Test
    fun `safeCall should map unknown exceptions to UnknownException`() = runTest(testDispatcher) {
        // Given
        val exception = IllegalArgumentException("Test exception")
        
        // When
        val result = safeCall {
            throw exception
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.UnknownException)
        assertEquals(exception, error.cause)
    }
    
    @Test
    fun `safeCall should include context in error mapping`() = runTest(testDispatcher) {
        // Given
        val context = "TestContext"
        val exception = IllegalArgumentException("Test exception")
        
        // When
        val result = safeCall(context) {
            throw exception
        }
        
        // Then
        assertTrue(result.isFailure)
        val error = result.exceptionOrNull()
        assertTrue(error is DomainException.UnknownException)
        assertEquals(exception, error.cause)
    }
}
