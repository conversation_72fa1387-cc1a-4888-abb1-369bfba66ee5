import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
    id 'com.google.dagger.hilt.android'
    id 'kotlin-parcelize'
    id('com.google.devtools.ksp')
    id 'com.google.firebase.crashlytics'
    id "org.jetbrains.kotlin.plugin.compose"
    id 'jacoco'
    id 'kotlin-kapt'
}
android {
//    signingConfigs {
//        release {
//            storeFile file('E:\\josyAndChris\\jas_key.jks')
//            storePassword 'Ornelapatricia1'
//            keyAlias 'key0'
//            keyPassword 'Ornelapatricia1'
//        }
//    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
    // Enable JaCoCo for code coverage
    buildTypes {
        debug {
            testCoverageEnabled true
        }
    }
    namespace 'com.tfkcolin.josyandchris'
    compileSdk 35

    defaultConfig {
        applicationId "com.tfkcolin.josyandchris"
        minSdk 23
        targetSdk 34
        versionCode 36
        versionName "1.40"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }
//    signingConfigs {
//        release {
//            storeFile file("E:\\josyAndChris\\jas_key.jks")
//            storePassword "Ornelapatricia1"
//            keyAlias "key0"
//            keyPassword "Ornelapatricia1"
//        }
//    }

    buildTypes {
        debug {
            testCoverageEnabled true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//            signingConfig signingConfigs.release
        }
    }

    buildFeatures {
        compose true
        buildConfig true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.4'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:33.16.0')
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'
    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.google.firebase:firebase-config-ktx'               // Remote Config
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3' // Coroutines for Play Services
    implementation 'com.google.android.gms:play-services-auth:21.3.0'      // One-Tap
    implementation 'androidx.biometric:biometric:1.2.0-alpha05'              // Biometric
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'  // Encrypted SharedPreferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'   // DataStore
    implementation 'com.jakewharton.timber:timber:5.0.1'              // Timber logging
	implementation 'com.google.firebase:firebase-analytics-ktx'
	implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'androidx.navigation:navigation-testing-android:2.9.1'

    def roomVersion = '2.7.2'
    def version_moshi = "1.15.2"

    implementation("com.google.dagger:hilt-android:2.51.1")
    implementation("androidx.hilt:hilt-navigation-compose:1.2.0")
    ksp("com.google.dagger:hilt-android-compiler:2.56.2")
    implementation "androidx.navigation:navigation-compose:2.9.1"

    implementation "com.squareup.moshi:moshi-kotlin:$version_moshi"
    implementation "com.squareup.moshi:moshi:$version_moshi"
    implementation "com.squareup.moshi:moshi-kotlin-codegen:$version_moshi"

    // Room dependencies
    implementation "androidx.room:room-runtime:$roomVersion"
    implementation "androidx.room:room-ktx:$roomVersion"
    ksp "androidx.room:room-compiler:$roomVersion"
    implementation "androidx.room:room-paging:$roomVersion"
    
    // Jetpack Paging 3
    implementation "androidx.paging:paging-runtime-ktx:3.3.6"
    implementation "androidx.paging:paging-compose:3.3.6"

    implementation("io.coil-kt:coil-compose:2.7.0")

    implementation "com.google.accompanist:accompanist-systemuicontroller:0.36.0"
    implementation "com.google.accompanist:accompanist-insets:0.30.1"
    implementation "com.google.accompanist:accompanist-insets-ui:0.36.0"
    implementation "com.google.accompanist:accompanist-swiperefresh:0.36.0"

    implementation "androidx.compose.material3:material3-window-size-class:1.3.2"

    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.9.1'
    implementation "androidx.compose.runtime:runtime-livedata:1.8.3"
    implementation "org.jetbrains.kotlin:kotlin-reflect:2.0.0"

    //Import okHttp dependencies
    // define a BOM and its version
    implementation(platform("com.squareup.okhttp3:okhttp-bom:5.1.0"))

    // define any required OkHttp artifacts without version
    implementation("com.squareup.okhttp3:okhttp")
    implementation("com.squareup.okhttp3:logging-interceptor")

    //Import Retrofit dependencies
    implementation 'com.squareup.retrofit2:converter-gson:3.0.0'
    implementation 'com.squareup.retrofit2:retrofit:3.0.0'

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.9.1'
    implementation 'androidx.activity:activity-compose:1.10.1'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.compose.material3:material3:1.3.2'
    // Unit test dependencies
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'com.google.truth:truth:1.4.4'
    testImplementation 'org.mockito:mockito-core:5.18.0'
    testImplementation 'org.mockito:mockito-inline:5.2.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.4.0'
    testImplementation 'app.cash.turbine:turbine:1.2.1'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.2'
    testImplementation 'org.robolectric:robolectric:4.15.1'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.jetbrains.kotlin:kotlin-test-junit:1.7.20'
    testImplementation 'org.mockito:mockito-core:5.18.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.4.0'
    testImplementation 'io.mockk:mockk:1.13.11'
    implementation("androidx.compose.material:material-icons-extended:1.6.7")

// Kotlin Coroutines Test
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.2'

// Turbine for Flow testing
    testImplementation 'app.cash.turbine:turbine:1.2.1'
    // Android test dependencies
    androidTestImplementation 'androidx.test.ext:junit-ktx:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    androidTestImplementation 'com.google.dagger:hilt-android-testing:2.56.2'
    androidTestImplementation 'org.mockito:mockito-android:5.18.0'
    androidTestImplementation 'com.squareup.okhttp3:mockwebserver:5.1.0'
    
    // Hilt for Android tests
    kaptAndroidTest 'com.google.dagger:hilt-android-compiler:2.56.2'
    kspAndroidTest 'com.google.dagger:hilt-android-compiler:2.56.2'

    testImplementation 'org.jetbrains.kotlin:kotlin-test:1.7.20'
    
    // Architecture Components Test
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:1.8.3"
}

kotlin {
    jvmToolchain(13)
}

// JaCoCo configuration for test coverage
tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}

task jacocoTestReport(type: JacocoReport, dependsOn: ['testDebugUnitTest']) {
    reports {
        xml.required = true
        html.required = true
    }
    
    def fileFilter = ['**/R.class', '**/R$*.class', '**/BuildConfig.*', '**/Manifest*.*', '**/*Test*.*', 'android/**/*.*']
    def debugTree = fileTree(dir: "${buildDir}/intermediates/classes/debug", excludes: fileFilter)
    def mainSrc = "${project.projectDir}/src/main/java"
    
    sourceDirectories.from = files([mainSrc])
    classDirectories.from = files([debugTree])
    executionData.from = fileTree(dir: "$buildDir", includes: [
        "jacoco/testDebugUnitTest.exec",
        "outputs/code-coverage/connected/*coverage.ec"
    ])
}

tasks.withType(KotlinCompile).configureEach{
    kotlinOptions.freeCompilerArgs += "-opt-in=kotlin.RequiresOptIn"
}
