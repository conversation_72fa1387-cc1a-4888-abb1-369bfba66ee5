# FirebaseRepository.tryFetch Migration Tracking Sheet

This document tracks all instances of `FirebaseRepository.tryFetch` in the codebase that need to be migrated.

## Summary

- **Total Occurrences**: 67
- **All Inside Coroutines**: Yes, all calls are within suspend functions
- **Primary Consumers**:
  - ViewModels (6 calls)
  - Data Sources (61 calls)

### Distribution by Component
| Component | Number of Calls |
|-----------|----------------|
| FirebaseImageDataDataSource | 9 |
| FirebaseImageDataRepositoryImpl | 5 |
| FirebaseCommandDataSource | 6 |
| FirebaseCargoDataSource | 8 |
| FirebaseShipmentDataSource | 8 |
| FirebaseCountryDataSource | 10 |
| FirebaseFinancialTransactionDataSource | 9 |
| EditImageDataViewModel | 2 |
| ShipmentDetailsViewModel | 2 |
| AddImageDataScreenViewModel | 2 |

### Migration Pattern
All occurrences of `FirebaseRepository.tryFetch` are within suspend functions, making the migration to a new async pattern straightforward.

| File Path | Line Number | Inside Coroutine? | Consumer Component | Migration Status |
|-----------|-------------|-------------------|-------------------|------------------|
| ui/screens/editimagedata/EditImageDataViewModel.kt | 55 | Yes - suspend function | EditImageDataViewModel | Not Started |
| ui/screens/editimagedata/EditImageDataViewModel.kt | 60 | Yes - suspend function | EditImageDataViewModel | Not Started |
| ui/screens/shipmentdetails/ShipmentDetailsViewModel.kt | 85 | Yes - suspend function | ShipmentDetailsViewModel | Not Started |
| ui/screens/shipmentdetails/ShipmentDetailsViewModel.kt | 99 | Yes - suspend function | ShipmentDetailsViewModel | Not Started |
| ui/screens/addimagedatascreen/AddImageDataScreenViewModel.kt | 70 | Yes - suspend function | AddImageDataScreenViewModel | Not Started |
| ui/screens/addimagedatascreen/AddImageDataScreenViewModel.kt | 77 | Yes - suspend function | AddImageDataScreenViewModel | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 43 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 53 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 64 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 75 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 102 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 114 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 126 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 307 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| remote/datasource/FirebaseImageDataDataSource.kt | 325 | Yes - suspend function | FirebaseImageDataDataSource | Not Started |
| repository/FirebaseImageDataRepositoryImpl.kt | 115 | Yes - suspend function | FirebaseImageDataRepositoryImpl | Not Started |
| repository/FirebaseImageDataRepositoryImpl.kt | 130 | Yes - suspend function | FirebaseImageDataRepositoryImpl | Not Started |
| repository/FirebaseImageDataRepositoryImpl.kt | 145 | Yes - suspend function | FirebaseImageDataRepositoryImpl | Not Started |
| repository/FirebaseImageDataRepositoryImpl.kt | 160 | Yes - suspend function | FirebaseImageDataRepositoryImpl | Not Started |
| repository/FirebaseImageDataRepositoryImpl.kt | 207 | Yes - suspend function | FirebaseImageDataRepositoryImpl | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 60 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 70 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 81 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 92 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 139 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCommandDataSource.kt | 151 | Yes - suspend function | FirebaseCommandDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 55 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 65 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 76 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 87 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 133 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 145 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 157 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseCargoDataSource.kt | 169 | Yes - suspend function | FirebaseCargoDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 48 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 66 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 77 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 88 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 133 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 145 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 157 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseShipmentDataSource.kt | 169 | Yes - suspend function | FirebaseShipmentDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 44 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 54 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 65 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 76 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 84 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 111 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 133 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 145 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 162 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseCountryDataSource.kt | 175 | Yes - suspend function | FirebaseCountryDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 49 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 59 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 70 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 79 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 106 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 118 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 130 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 142 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
| remote/datasource/FirebaseFinancialTransactionDataSource.kt | 158 | Yes - suspend function | FirebaseFinancialTransactionDataSource | Not Started |
