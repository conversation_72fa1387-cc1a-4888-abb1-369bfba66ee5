# Full-Text Search Index Implementation Summary

## Overview
This implementation adds comprehensive full-text search functionality with prefix tokens to the Firebase Firestore data layer. The system now supports efficient prefix search using Firestore's `array-contains-any` queries.

## Key Features

### 1. SearchIndexUtils Utility Class
**Location**: `app/src/main/java/com/tfkcolin/josyandchris/util/SearchIndexUtils.kt`

**Key Functions**:
- `generatePrefixTokens(text: String)`: Generates prefix tokens for a given text
  - Example: "Yaoundé" → ["y", "ya", "yao", "yaou", "yaoun", "yaound", "yaoundé"]
- `generateSearchIndex(texts: List<String>)`: Combines prefix tokens from multiple text fields
- `generateQueryTokens(query: String)`: Generates search tokens for queries
- `normalizeText(text: String)`: Normalizes text by removing accents and converting to lowercase

### 2. Updated Data Models
All searchable entities now include a `searchIndex: List<String>` field:

- **Command**: Already had `searchIndex` as `List<String>`
- **Cargo**: Updated from `String` to `List<String>`
- **CountryData**: Added `searchIndex: List<String>`
- **Shipment**: Added `searchIndex: List<String>`

### 3. Data Source Implementations

#### FirebaseCommandDataSource
- **Search Fields**: Client name, phone, country, city, product names, descriptions, observation notes, command ID
- **Generate Index**: On `createCommand()` and `updateCommand()`
- **Search Methods**: `searchCommands()`, `searchCommandsFlow()`

#### FirebaseCargoDataSource  
- **Search Fields**: Origin, destination
- **Generate Index**: On `createCargo()` and `updateCargo()`
- **Search Methods**: `searchCargos()`, `searchCargosFlow()`

#### FirebaseCountryDataSource
- **Search Fields**: Country name, devise (currency)
- **Generate Index**: On `createCountry()` and `updateCountry()`
- **Search Methods**: `searchCountries()`, `searchCountriesFlow()`, `getCountriesBySearchTokens()`

#### FirebaseShipmentDataSource
- **Search Fields**: Client name, phone, cargo ID, product names, descriptions, shipment ID
- **Generate Index**: On `createShipment()` and `updateShipment()`
- **Search Methods**: `searchShipments()`, `searchShipmentsFlow()`

## How It Works

### 1. Index Generation (On Write/Update)
When creating or updating documents, the system:
1. Extracts searchable text from relevant fields
2. Generates lowercase prefix tokens for each word
3. Stores tokens in the `searchIndex` array field
4. Handles accent removal and normalization

**Example**:
```kotlin
// Input: Command with client name "José María"
// Generated tokens: ["j", "jo", "jos", "josé", "m", "ma", "mar", "marí", "maría"]
val command = Command(client = ClientData(name = "José María"))
val tokens = generateSearchIndex(command)
// Result: searchIndex = ["j", "jo", "jos", "josé", "m", "ma", "mar", "marí", "maría", ...]
```

### 2. Search Queries (On Read)
When searching:
1. Normalize the search query
2. Generate query tokens (word-level, not prefix)
3. Use Firestore's `whereArrayContainsAny("searchIndex", queryTokens)`
4. Return matching documents

**Example**:
```kotlin
// Search query: "jose"
// Query tokens: ["jose"] (normalized from "José")
// Firestore query: .whereArrayContainsAny("searchIndex", ["jose"])
// Matches documents with "josé" in searchIndex
```

## Benefits

### 1. Efficient Prefix Search
- **Before**: String-based prefix queries with `whereGreaterThanOrEqualTo` and `whereLessThanOrEqualTo`
- **After**: Array-based queries with `whereArrayContainsAny` for better performance

### 2. Multi-field Search
- Single query can search across multiple fields (name, phone, products, etc.)
- Unified search experience across different entity types

### 3. Accent-insensitive Search
- "jose" matches "José"
- "yaounde" matches "Yaoundé"

### 4. Partial Match Support
- "ya" matches "Yaoundé"
- "mar" matches "María"

## Usage Examples

### Basic Search
```kotlin
// Search commands by client name
val results = commandDataSource.searchCommands("jose")

// Search countries by name  
val countries = countryDataSource.searchCountries("came")

// Search cargos by destination
val cargos = cargoDataSource.searchCargos("doua")
```

### Real-time Search
```kotlin
// Real-time search with Flow
commandDataSource.searchCommandsFlow("client name")
    .collect { commands ->
        // Handle search results
    }
```

## Implementation Notes

### 1. Index Size Considerations
- Firestore has a 1MB document size limit
- Prefix tokens can grow quickly for long texts
- The implementation uses minimum length of 1 character by default
- Consider increasing `minLength` parameter for very long texts

### 2. Query Limitations
- `array-contains-any` supports up to 10 values per query
- For complex multi-word searches, consider combining results client-side
- Single-word searches are most efficient

### 3. Migration Strategy
- Existing documents without `searchIndex` will need to be updated
- Consider batch updates for existing data
- New documents automatically get search index on creation

## Future Enhancements

1. **Batch Migration Script**: Update existing documents with search indexes
2. **Advanced Query Support**: Multi-word search with AND/OR logic
3. **Search Analytics**: Track search performance and popular queries
4. **Fuzzy Matching**: Support for typos and similar words
5. **Search Highlighting**: Highlight matching terms in results

## Files Modified

1. `app/src/main/java/com/tfkcolin/josyandchris/util/SearchIndexUtils.kt` (NEW)
2. `app/src/main/java/com/tfkcolin/josyandchris/data/Cargo.kt`
3. `app/src/main/java/com/tfkcolin/josyandchris/data/CountryData.kt`
4. `app/src/main/java/com/tfkcolin/josyandchris/remote/datasource/FirebaseCommandDataSource.kt`
5. `app/src/main/java/com/tfkcolin/josyandchris/remote/datasource/FirebaseCargoDataSource.kt`
6. `app/src/main/java/com/tfkcolin/josyandchris/remote/datasource/FirebaseCountryDataSource.kt`
7. `app/src/main/java/com/tfkcolin/josyandchris/remote/datasource/FirebaseShipmentDataSource.kt`

This implementation provides a robust foundation for full-text search across the application's main entities, enabling users to quickly find relevant data using natural, partial search terms.
