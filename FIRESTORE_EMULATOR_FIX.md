# Firestore Emulator Configuration Fix

## Issue
The app was not loading any data from Firestore because it was configured to connect to a local emulator at `192.168.43.153:8080` instead of the production Firestore database.

## Root Cause
In `JACApplication.kt`, the app was set to use emulator when in DEBUG mode:
```kotlin
if(BuildConfig.DEBUG){
    firestore.useEmulator("192.168.43.153", 8080)
    FirebaseStorage.getInstance().useEmulator("192.168.43.153", 9199)
}
```

## Quick Fix (Applied)
Temporarily commented out the emulator configuration to allow connection to production Firestore.

## Recommended Long-term Solution

### 1. Add Build Configuration Flag
In your app's `build.gradle` file, add a build config field:

```gradle
android {
    buildTypes {
        debug {
            buildConfigField "boolean", "USE_FIREBASE_EMULATOR", "false"
            // Set to true only when you actually have emulator running
        }
        release {
            buildConfigField "boolean", "USE_FIREBASE_EMULATOR", "false"
        }
    }
}
```

### 2. Update JACApplication.kt
```kotlin
override fun onCreate() {
    super.onCreate()
    
    // Initialize Timber for logging
    if (BuildConfig.DEBUG) {
        Timber.plant(Timber.DebugTree())
    } else {
        Timber.plant(CrashlyticsTree())
    }
    
    FirebaseApp.initializeApp(applicationContext)
    
    val firestore = FirebaseFirestore.getInstance()
    
    if (BuildConfig.USE_FIREBASE_EMULATOR) {
        // Use emulator only when explicitly enabled
        firestore.useEmulator("********", 8080) // Use ******** for Android emulator
        FirebaseStorage.getInstance().useEmulator("********", 9199)
        Timber.d("Using Firebase emulator")
    } else {
        Timber.d("Using production Firebase")
    }
    
    // Always enable persistence
    firestore.firestoreSettings = firestoreSettings { 
        isPersistenceEnabled = true 
    }
}
```

### 3. Alternative: Use Local Properties
Create a `local.properties` entry:
```
useFirebaseEmulator=false
```

Then in build.gradle:
```gradle
def localProperties = new Properties()
localProperties.load(new FileInputStream(rootProject.file("local.properties")))

android {
    buildTypes {
        debug {
            buildConfigField "boolean", "USE_FIREBASE_EMULATOR", 
                localProperties.getProperty('useFirebaseEmulator', 'false')
        }
    }
}
```

## Testing
After applying the fix:
1. Clean and rebuild the project
2. Uninstall the app from the device/emulator
3. Reinstall and run the app
4. The app should now connect to production Firestore and load data

## Notes
- When using emulator, ensure the IP address is correct:
  - `********` for Android emulator
  - Your machine's IP for physical devices
  - `localhost` or `127.0.0.1` won't work from Android devices/emulators
