# Selective Real-Time Listeners Strategy

## Overview

This implementation provides a selective real-time listeners strategy that defaults to one-shot fetch with paging and adds optional real-time listeners only when needed. This approach optimizes performance and reduces unnecessary Firestore reads.

## Key Components

### 1. Repository Layer Enhancement

**CargoRepository.kt** - Added new methods:
- `listenForUpdates(filters: QueryFilters?)` - Main selective listener
- `listenForCargoUpdates(cargoId: String)` - Listen to specific cargo
- `listenForCargosByStatusUpdates(statusIndex: Int)` - Listen by status
- `listenForCargosByRouteUpdates(origin: String, destination: String)` - Listen by route

### 2. Data Source Layer Enhancement

**FirebaseCargoDataSource.kt** - Implemented real-time listeners:
- Uses `callbackFlow` and `addSnapshotListener` for real-time updates
- Proper cleanup with `awaitClose { listener.remove() }`
- Supports filtered listening with QueryBuilder integration

### 3. ViewModel Layer Implementation

#### CargoListViewModel
- **Default behavior**: One-shot fetch with paging using `getCargosPagingData()`
- **Optional real-time**: `startLiveUpdates()` method to enable real-time listening
- **Automatic cleanup**: `onCleared()` calls `stopLiveUpdates()`
- **Live mode state**: `isLiveMode` StateFlow to track listener status

#### CargoDetailsViewModel
- **Selective listening**: Only listens when user opens detail screen
- **Document-specific**: Listens to specific cargo document updates
- **Toggle capability**: `toggleLiveMode()` for user control
- **Fallback mechanism**: `getCurrentCargo()` uses real-time data if available, otherwise fetches once

## Usage Patterns

### 1. List Screen (Default: One-shot + Paging)

```kotlin
// Default behavior - no real-time listeners
val paginatedCargos = viewModel.getCargosPagingData()
    .collectAsLazyPagingItems()

// Optional: Enable live mode when user toggles
if (isLiveModeEnabled) {
    viewModel.startLiveUpdates()
}
```

### 2. Detail Screen (Selective Real-time)

```kotlin
// Start listening when screen opens
LaunchedEffect(cargoId) {
    viewModel.startLiveUpdates()
}

// Observe real-time updates
val currentCargo by viewModel.currentCargo.collectAsState()

// Cleanup happens automatically in onCleared()
```

### 3. User-Controlled Live Mode

```kotlin
// Toggle button for live updates
Switch(
    checked = isLiveMode,
    onCheckedChange = { 
        viewModel.toggleLiveMode()
    }
)
```

## Performance Benefits

1. **Reduced Firestore Reads**: Only attach listeners when actively needed
2. **Better Battery Life**: No unnecessary background listeners
3. **Automatic Cleanup**: Listeners are properly detached in `onCleared()`
4. **Efficient Paging**: Default pagination doesn't use real-time listeners
5. **Selective Updates**: Can listen to specific documents or filtered queries

## Implementation Status

✅ **Repository interfaces** - Enhanced with selective listener methods  
✅ **Data source layer** - Implemented real-time listeners with proper cleanup  
✅ **CargoDetailsViewModel** - Added selective real-time listening for individual cargo  
🔄 **CargoListViewModel** - Enhanced with optional real-time listeners  
⚠️ **Build issues** - Some existing codebase compilation errors need resolution  

## Next Steps

1. **Fix compilation errors** in existing codebase
2. **Update UI screens** to use selective listeners appropriately
3. **Add user controls** for toggling live mode
4. **Implement pattern** for other entities (Commands, Shipments, etc.)
5. **Add unit tests** for listener lifecycle management

## Best Practices

1. **Always call `stopLiveUpdates()`** in `onCleared()`
2. **Check `isLiveMode`** before starting new listeners
3. **Use appropriate listener type** (document vs collection vs filtered)
4. **Provide fallback** for when real-time data is not available
5. **Consider user experience** - show live indicators when active

## Code Example: Complete Implementation

```kotlin
@HiltViewModel
class CargoDetailsViewModel @Inject constructor(
    private val cargoRepository: CargoRepository
) : ViewModel() {
    
    private val _isLiveMode = MutableStateFlow(false)
    val isLiveMode: StateFlow<Boolean> = _isLiveMode.asStateFlow()
    
    private var liveUpdatesJob: Job? = null
    
    fun startLiveUpdates() {
        if (_isLiveMode.value) return
        
        _isLiveMode.value = true
        liveUpdatesJob?.cancel()
        
        liveUpdatesJob = viewModelScope.launch {
            cargoRepository.listenForCargoUpdates(cargoId)
                .collect { cargo ->
                    // Update UI state
                }
        }
    }
    
    fun stopLiveUpdates() {
        _isLiveMode.value = false
        liveUpdatesJob?.cancel()
        liveUpdatesJob = null
    }
    
    override fun onCleared() {
        super.onCleared()
        stopLiveUpdates()
    }
}
```

This strategy ensures optimal performance while providing real-time capabilities when and where they are needed most.
