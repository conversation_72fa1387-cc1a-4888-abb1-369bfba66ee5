2025-07-19 00:26:12.718 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=6.30ms min=3.01ms max=14.27ms count=60
2025-07-19 00:26:13.732 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=5.49ms min=2.32ms max=13.84ms count=61
2025-07-19 00:26:14.750 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=7.88ms min=3.15ms max=83.41ms count=56
2025-07-19 00:26:15.754 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=5.93ms min=2.98ms max=12.60ms count=60
2025-07-19 00:26:16.765 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=5.74ms min=2.40ms max=15.29ms count=61
2025-07-19 00:26:17.782 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=4.85ms min=2.38ms max=7.95ms count=61
2025-07-19 00:26:21.018 15041-15041 Choreographer           com.tfkcolin.josyandchris            I  Skipped 163 frames!  The application may be doing too much work on its main thread.
2025-07-19 00:26:21.025 15041-15086 EGL_emulation           com.tfkcolin.josyandchris            D  app_time_stats: avg=93.39ms min=2.99ms max=2730.83ms count=31
2025-07-19 00:26:21.026 15041-15041 AndroidRuntime          com.tfkcolin.josyandchris            D  Shutting down VM
2025-07-19 00:26:21.033 15041-15041 AndroidRuntime          com.tfkcolin.josyandchris            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.tfkcolin.josyandchris, PID: 15041
                                                                                                    java.lang.RuntimeException: Could not deserialize object. Expected a List, but got a class java.lang.String (found in field 'searchIndex')
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.deserializeError(CustomClassMapper.java:579)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.deserializeToParameterizedType(CustomClassMapper.java:282)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.deserializeToType(CustomClassMapper.java:194)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.access$300(CustomClassMapper.java:58)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper$BeanMapper.deserialize(CustomClassMapper.java:802)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper$BeanMapper.deserialize(CustomClassMapper.java:765)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.convertBean(CustomClassMapper.java:558)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.deserializeToClass(CustomClassMapper.java:259)
                                                                                                    	at com.google.firebase.firestore.util.CustomClassMapper.convertToCustomClass(CustomClassMapper.java:104)
                                                                                                    	at com.google.firebase.firestore.DocumentSnapshot.toObject(DocumentSnapshot.java:183)
                                                                                                    	at com.google.firebase.firestore.QueryDocumentSnapshot.toObject(QueryDocumentSnapshot.java:116)
                                                                                                    	at com.google.firebase.firestore.DocumentSnapshot.toObject(DocumentSnapshot.java:161)
                                                                                                    	at com.google.firebase.firestore.QueryDocumentSnapshot.toObject(QueryDocumentSnapshot.java:97)
                                                                                                    	at com.tfkcolin.josyandchris.ui.data.ImageDataListLiveData.onEvent(ImageDataListLiveData.kt:41)
                                                                                                    	at com.tfkcolin.josyandchris.ui.data.ImageDataListLiveData.onEvent(ImageDataListLiveData.kt:9)
                                                                                                    	at com.google.firebase.firestore.Query.lambda$addSnapshotListenerInternal$3$com-google-firebase-firestore-Query(Query.java:1176)
                                                                                                    	at com.google.firebase.firestore.Query$$ExternalSyntheticLambda0.onEvent(D8$$SyntheticClass:0)
                                                                                                    	at com.google.firebase.firestore.core.AsyncEventListener.lambda$onEvent$0$com-google-firebase-firestore-core-AsyncEventListener(AsyncEventListener.java:42)
                                                                                                    	at com.google.firebase.firestore.core.AsyncEventListener$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:205)
                                                                                                    	at android.os.Looper.loop(Looper.java:294)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8177)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:971)
2025-07-19 00:26:21.046 15041-15101 OpenGLRenderer          com.tfkcolin.josyandchris            I  Davey! duration=2752ms; Flags=0, FrameTimelineVsyncId=7858910, IntendedVsync=26457434773123, Vsync=26460151439681, InputEventId=0, HandleInputStart=26460168180243, AnimationStart=26460168183430, PerformTraversalsStart=26460169806002, DrawStart=26460169952206, FrameDeadline=26457484773121, FrameInterval=26460166031069, FrameStartTime=16666666, SyncQueued=26460170557147, SyncStart=26460170752653, IssueDrawCommandsStart=26460170849844, SwapBuffers=26460171569698, FrameCompleted=26460187049235, DequeueBufferDuration=4654, QueueBufferDuration=608864, GpuCompleted=26460187049235, SwapBuffersCompleted=26460173958447, DisplayPresentTime=0, CommandSubmissionCompleted=26460171569698,
2025-07-19 00:26:21.830 15041-15041 Process                 com.tfkcolin.josyandchris            I  Sending signal. PID: 15041 SIG: 9
---------------------------- PROCESS ENDED (15041) for package com.tfkcolin.josyandchris ----------------------------
